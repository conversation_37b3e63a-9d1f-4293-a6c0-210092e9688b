# OpenAI API key
OPENAI_API_KEY=your_openai_api_key_here

# Your API server configuration (if needed)
API_BASE_URL=http://localhost:8000

# Supabase JWT token (Optional - if you already have a token)
SUPABASE_JWT_TOKEN=your_supabase_jwt_token_here

# Supabase credentials (Required if JWT token is not provided)
SUPABASE_URL=http://localhost:54321  # Default for local Supabase
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...  # Your anon/public key
SUPABASE_EMAIL=<EMAIL>
SUPABASE_PASSWORD=your_test_user_password

# Auth token (if applicable to your setup)
AUTH_TOKEN=your_auth_token_here 