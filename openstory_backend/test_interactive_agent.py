#!/usr/bin/env python3
"""
Test script for the Interactive Story Agent
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "app"))


async def test_interactive_agent():
    """Test the interactive story agent functionality"""
    try:
        from app.services.agent_service import story_agent_service

        print("✅ Interactive Agent service imported successfully")

        # Test story creation request
        test_message = "Help me create a story about a brave knight"
        session_id = "test_session"

        print(f"\n🧪 Testing interactive agent with message: '{test_message}'")
        print(f"🔍 Using session_id: {session_id}")

        response = await story_agent_service.interactive_chat(test_message, session_id)
        print(f"📝 Agent response type: {response.get('type', 'unknown')}")
        print(f"📝 Agent response: {response.get('message', str(response))}")

        # If it's a confirmation request, test confirmation
        if response.get("type") == "confirmation_request":
            print(f"\n✅ Confirmation request received!")
            print(f"🔍 Confirmation ID: {response.get('confirmation_id')}")

            # Check pending confirmations
            pending = story_agent_service.get_pending_confirmations(session_id)
            print(f"🔍 Pending confirmations: {list(pending.keys())}")

            # Test confirming
            print(f"\n🧪 Testing confirmation with 'yes'")
            confirm_response = await story_agent_service.interactive_chat(
                "yes", session_id
            )
            print(
                f"📝 Confirmation response type: {confirm_response.get('type', 'unknown')}"
            )
            print(
                f"📝 Confirmation response: {confirm_response.get('message', str(confirm_response))[:200]}..."
            )

            # If it's another confirmation request, test the second step
            if confirm_response.get("type") == "confirmation_request":
                print(f"\n✅ Second confirmation request received!")
                print(f"🔍 Confirmation ID: {confirm_response.get('confirmation_id')}")

                # Test confirming second step
                print(f"\n🧪 Testing second confirmation with 'yes'")
                second_confirm_response = await story_agent_service.interactive_chat(
                    "yes", session_id
                )
                print(
                    f"📝 Second confirmation response type: {second_confirm_response.get('type', 'unknown')}"
                )
                print(
                    f"📝 Second confirmation response: {second_confirm_response.get('message', str(second_confirm_response))[:200]}..."
                )

        print("\n✅ Interactive agent test completed successfully!")

    except Exception as e:
        print(f"❌ Error testing interactive agent: {str(e)}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_interactive_agent())
