"""Add image_url to shows and episodes

Revision ID: 99f1227591f7
Revises: 466a0e235c09
Create Date: 2025-05-01 14:37:37.976695

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '99f1227591f7'
down_revision: Union[str, None] = '466a0e235c09'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
        op.add_column('episodes', sa.Column('image_url', sa.Text(), nullable=True))
        op.add_column('shows', sa.Column('image_url', sa.Text(), nullable=True))

def downgrade() -> None:
        op.drop_column('shows', 'image_url')
        op.drop_column('episodes', 'image_url')
