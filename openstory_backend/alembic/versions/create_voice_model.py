"""create voice model

Revision ID: create_voice_model
Revises: 466a0e235c09
Create Date: 2024-03-21 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'create_voice_model'
down_revision = '466a0e235c09'
branch_labels = None
depends_on = None


def upgrade():
    # Create voices table
    op.create_table(
        'voices',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('voice_id', sa.String(), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )

    # Add voice_id column to characters table
    op.add_column('characters', sa.Column('voice_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key(
        'fk_character_voice_id', 'characters',
        'voices', ['voice_id'], ['id']
    )

    # Migrate existing voice data
    op.execute("""
        INSERT INTO voices (id, voice_id, description)
        SELECT 
            gen_random_uuid(),
            voice_id,
            voice_description
        FROM characters
        WHERE voice_id IS NOT NULL OR voice_description IS NOT NULL
    """)

    # Update character voice_id references
    op.execute("""
        UPDATE characters c
        SET voice_id = v.id
        FROM voices v
        WHERE c.voice_id = v.voice_id
    """)

    # Drop old voice columns from characters
    op.drop_column('characters', 'voice_description')
    op.drop_column('characters', 'voice_id')


def downgrade():
    # Add back old voice columns to characters
    op.add_column('characters', sa.Column('voice_description', sa.Text(), nullable=True))
    op.add_column('characters', sa.Column('voice_id', sa.String(), nullable=True))

    # Migrate data back
    op.execute("""
        UPDATE characters c
        SET 
            voice_id = v.voice_id,
            voice_description = v.description
        FROM voices v
        WHERE c.voice_id = v.id
    """)

    # Drop foreign key and voice_id column
    op.drop_constraint('fk_character_voice_id', 'characters', type_='foreignkey')
    op.drop_column('characters', 'voice_id')

    # Drop voices table
    op.drop_table('voices') 