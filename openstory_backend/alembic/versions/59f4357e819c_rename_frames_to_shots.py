"""rename_frames_to_shots

Revision ID: 59f4357e819c
Revises: 99f1227591f7
Create Date: 2025-05-07 17:32:35.734287

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '59f4357e819c'
down_revision: Union[str, None] = '99f1227591f7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Rename tables
    op.execute('ALTER TABLE frames RENAME TO shots')
    op.execute('ALTER TABLE frame_components RENAME TO shot_components')
    
    # Rename columns in shot_components table
    op.execute('ALTER TABLE shot_components RENAME COLUMN frame_id TO shot_id')
    
    # Rename columns in shots table
    op.execute('ALTER TABLE shots RENAME COLUMN frame_number TO shot_number')
    
    # Add new columns to shots table for storyboard data
    op.add_column('shots', sa.Column('audio_url', sa.String(), nullable=True))
    op.add_column('shots', sa.Column('description', sa.String(), nullable=True))
    op.add_column('shots', sa.Column('camera_angle', sa.String(), nullable=True))
    op.add_column('shots', sa.Column('setting', sa.String(), nullable=True))
    op.add_column('shots', sa.Column('characters', sa.ARRAY(sa.String()), nullable=True))
    op.add_column('shots', sa.Column('actions', sa.String(), nullable=True))
    op.add_column('shots', sa.Column('dialogues', sa.JSON(), nullable=True))
    op.add_column('shots', sa.Column('mood', sa.String(), nullable=True))
    op.add_column('shots', sa.Column('lighting', sa.String(), nullable=True))
    op.add_column('shots', sa.Column('special_effects', sa.String(), nullable=True))
    op.add_column('shots', sa.Column('transitions', sa.String(), nullable=True))
    op.add_column('shots', sa.Column('storyboard_data', sa.JSON(), nullable=True))
    
    # Update component_type in shot_components to include 'music'
    op.execute("ALTER TABLE shot_components DROP CONSTRAINT IF EXISTS frame_components_component_type_check")
    op.execute("ALTER TABLE shot_components ADD CONSTRAINT shot_components_component_type_check CHECK (component_type IN ('dialogue', 'sound_effect', 'music'))")


def downgrade() -> None:
    # Remove new columns from shots table
    op.drop_column('shots', 'storyboard_data')
    op.drop_column('shots', 'transitions')
    op.drop_column('shots', 'special_effects')
    op.drop_column('shots', 'lighting')
    op.drop_column('shots', 'mood')
    op.drop_column('shots', 'dialogues')
    op.drop_column('shots', 'actions')
    op.drop_column('shots', 'characters')
    op.drop_column('shots', 'setting')
    op.drop_column('shots', 'camera_angle')
    op.drop_column('shots', 'description')
    op.drop_column('shots', 'audio_url')
    
    # Revert component_type in shot_components
    op.execute("ALTER TABLE shot_components DROP CONSTRAINT IF EXISTS shot_components_component_type_check")
    op.execute("ALTER TABLE shot_components ADD CONSTRAINT frame_components_component_type_check CHECK (component_type IN ('dialogue', 'sound_effect'))")
    
    # Rename columns in shots table
    op.execute('ALTER TABLE shots RENAME COLUMN shot_number TO frame_number')
    
    # Rename columns in shot_components table
    op.execute('ALTER TABLE shot_components RENAME COLUMN shot_id TO frame_id')
    
    # Rename tables
    op.execute('ALTER TABLE shot_components RENAME TO frame_components')
    op.execute('ALTER TABLE shots RENAME TO frames')
