"""Add is_public and updated_at to shows

Revision ID: 466a0e235c09
Revises: 
Create Date: 2024-05-09 16:10:41.185577

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '466a0e235c09'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Keep only the intended changes for the 'shows' table
    op.add_column('shows', sa.Column('is_public', sa.<PERSON>(), nullable=False, server_default=sa.text('false')))
    op.add_column('shows', sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True)) # Assuming nullable=True is acceptable, or match model
    
    # NOTE: All other autogenerated alter_column, index, and FK changes removed.
    # Review your schema and models. If other changes were needed, add them back manually or regenerate.

    # Example of removed changes (review if any were actually needed):
    # op.alter_column('character_progressions', 'created_at',
    #            existing_type=postgresql.TIMESTAMP(timezone=True),
    #            type_=sa.DateTime(),
    #            existing_nullable=True,
    #            existing_server_default=sa.text('now()'))
    # op.drop_index('idx_character_progressions_character_id', table_name='character_progressions')
    # ... many more ...
    # op.alter_column('episodes', 'permission',
    #            existing_type=sa.TEXT(),
    #            type_=sa.String(),
    #            existing_nullable=True) # <--- This caused the error
    # ... many more ...

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Keep only the reverse of the intended changes for the 'shows' table
    op.drop_column('shows', 'updated_at')
    op.drop_column('shows', 'is_public')
    
    # NOTE: All other autogenerated changes removed from downgrade as well.

    # Example of removed changes:
    # op.alter_column('users', 'updated_at',
    #            existing_type=sa.DateTime(),
    #            type_=postgresql.TIMESTAMP(timezone=True),
    #            existing_nullable=True,
    #            existing_server_default=sa.text('now()'))
    # ... many more ...
    # op.alter_column('episodes', 'permission',
    #            existing_type=sa.String(),
    #            type_=sa.TEXT(),
    #            existing_nullable=True)
    # ... many more ...
    # op.create_index('idx_shows_user_id', 'shows', ['user_id'], unique=False)

    # ### end Alembic commands ###
