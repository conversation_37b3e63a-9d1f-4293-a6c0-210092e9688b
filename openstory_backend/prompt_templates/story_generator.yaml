id: story_generator
description: Converts a paragraph into structured drama content with a story and characters
variables:
  - name: user_story
    type: string
    required: true
    description: The user's paragraph describing their story concept
  - name: story_type
    type: string
    required: true
    description: The type of story to generate

voice_mapping:
  - voice_id: "tLqgUTqf4d43AJi9ae"
    gender: "Female"
    age: "Young"
    accent: "British"
  - voice_id: "UgBYS2sOqTuMpoF3BRO"
    gender: "Male"
    age: "Middle-aged"
    accent: "American"
  - voice_id: "NOPBlnGlnO9m6vDvFkFC"
    gender: "Male"
    age: "Older"
    accent: "American"
  - voice_id: "RpwaOIB8s6tVGdp0MUrP"
    gender: "Female"
    age: "Middle-aged"
    accent: "American"
  - voice_id: "jyYV4jm5Wq39xQvc4ERa"
    gender: "Male"
    age: "Young"
    accent: "British"
  - voice_id: "AZnzktXvdvUeBnXmlld"
    gender: "Female"
    age: "Young"
    accent: "American"
  - voice_id: "v7AjzCg6vdhCmXwBrbt1"
    gender: "Male"
    age: "Middle-aged"
    accent: "British"
  - voice_id: "asI5kXBtm8kKmescIRSS"
    gender: "Female"
    age: "Middle-aged"
    accent: "British"
  - voice_id: "19STyYD15bswVz5inLf"
    gender: "Female"
    age: "Older"
    accent: "British"
  - voice_id: "3IERyHnub5B02E4Kv7P"
    gender: "Female"
    age: "Older"
    accent: "American"
  - voice_id: "WTUK29trZZ9CLPCFTTh"
    gender: "Male"
    age: "Young"
    accent: "American"
  - voice_id: "JBFqnCBsdsRMkjVDRZzb"
    gender: "Male"
    age: "Older"
    accent: "British"

age_categories:
  young:
    min: 1
    max: 15
    category: "Young"
  adult:
    min: 16
    max: 45
    category: "Middle-aged"
  old:
    min: 46
    max: 100
    category: "Older"

system_template: |
  You are a creative AI assistant specializing in converting brief story descriptions into structured drama content in JSON format. 
  
  IMPORTANT: You must ALWAYS return a complete JSON structure using function calling regardless of how brief the user's input is. NEVER respond conversationally, ask questions, or provide explanations. Your entire response must be structured JSON data only.
  
  Even with minimal input, generate creative and complete drama content rather than asking for more information. If the user's input is very brief, expand upon it creatively while maintaining logical consistency.

  For voice selection:
  1. Determine age category based on character's age:
     - Young (1-15 years)
     - Middle-aged (16-45 years)
     - Older (46+ years)
  2. Select appropriate voice_id based on character's gender and age category
  3. If multiple voices match, randomly select one

user_template: |
  # AI Drama Content Generator: From Paragraph to JSON

  ## Input:
  {{ user_story }}

  ## Story Type:
  {{ story_type }}

  ## Instructions:
  Analyze the above input (even if brief) and create a complete drama structure with:
  - A story with title, category, oneliner, detailed synopsis, and most suitable video bgm
  - 2-4 characters with name, gender, age, oneliner, personality, appearance, and voice_description

  ## Format Requirements:
  - IMPORTANT: YOU MUST USE FUNCTION CALLING to return your response in JSON format
  - DO NOT respond conversationally or ask follow-up questions
  - ALWAYS return a complete drama structure even if input is minimal
  - If input is very short, use your creativity to expand it into a full drama concept

  ## Content Guidelines:

  1. **Show Details:**
     - Title: Creative and catchy (1-5 words)
     - Category: Choose one appropriate category (Drama, Mystery, Sci-Fi, Comedy, Fantasy, Thriller, Romance, etc.)
     - Oneliner: A captivating single sentence hook/tagline
     - Synopsis: It should be 3-5 sentences detailing the story's plot, be specific on what happens.

  2. **Characters (2-4):**
     - Name: Full name appropriate to the character's background
     - Gender: "male", "female", or "non-binary"
     - Age: Realistic integer between 18-90
     - Oneliner: A single sentence describing their defining trait/role
     - Backstory: 2-3 sentences detailing their background, motivations, and flaws
     - Personality: 1-2 sentences detailing their traits, motivations, flaws, and behavior patterns
     - Appearance: 10 key phrases to describe their physical attributes to someone to reconstruct what they look like. Be specific on any gender, age, race, face, body, hair, outfit features etc.
     - Voice_description: Choose the best voice (US female child, US female adult, US female elderly, US male child, US male adult, US male elderly)
     - Voice Selection: Based on age and gender:
       * Age 1-15: Young voices
       * Age 16-45: Middle-aged voices
       * Age 46+: Older voices
       * Select appropriate voice_id from the provided mapping

  For multiline text fields like personality and appearance, use the newline character '\n' to separate paragraphs or sentences when appropriate.

functions:
  - name: parse_drama_content
    description: Parses the generated drama content into structured format
    parameters:
      type: object
      properties:
        story:
          type: object
          properties:
            title:
              type: string
            oneliner:
              type: string
            category:
              type: string
            synopsis:
              type: string
            bgm:
              type: string
              enum: ["drum beats", "electronic chill", "energetic beats", "majestic orchestra", "melancholy instrumental", "serene piano", "upbeat chill"]
          required:
            - title
            - oneliner
            - category
            - synopsis
            - bgm
        characters:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              gender:
                type: string
                enum: ["male", "female", "non-binary"]
              age:
                type: integer
                minimum: 18
                maximum: 90
              oneliner:
                type: string
              backstory:
                type: string
              personality:
                type: string
              appearance:
                type: string
              voice_description:
                type: string
                enum: ["US female child", "US female adult", "US female elderly", "US male child", "US male adult", "US male elderly"]
              voice_id:
                type: string
                description: "Voice ID selected based on age and gender"
              is_public:
                type: boolean
                default: false
              id:
                type: string
                format: uuid
              creator_user_id:
                type: string
                format: uuid
              avatar_urls:
                type: object
                properties:
                  url:
                    type: string
                    format: uri
              preview_img:
                type: string
                format: uri
              created_at:
                type: string
                format: date-time
              updated_at:
                type: string
                format: date-time
              voice:
                type: string
              voice_name:
                type: string
            required:
              - name
              - gender
              - age
              - oneliner
              - backstory
              - personality
              - appearance
              - voice_description
              - voice_id
      required:
        - story
        - characters 
