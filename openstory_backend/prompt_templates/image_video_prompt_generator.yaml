id: image_video_prompt_generator
description: Converts structured scene inputs into JSON prompts for AI image‑generation and video‑generation models

variables:
  - name: inputs
    type: object
    required: true
    description: Structured payload describing the scene
    properties:
      shot_setting:
        type: string
        description: Location/environment, lighting, colors, architecture, time of day, etc.
      shot_framing:
        type: object
        description: Camera‑framing JSON object (framing type, camera angle, lens, movement, aspect ratio, etc.). You may include any keys you need.
        additionalProperties: true          # accept arbitrary sub‑keys
      characters:
        type: array
        description: Array of character objects (leave empty if no characters)
        items:
          type: object
          properties:
            name:
              type: string
            descriptor:
              type: string
          required:
            - name
            - descriptor
      visual_style:
        type: string
        description: Desired art style or medium (e.g., “digital oil paint, neon‑noir”)
    required:
      - shot_setting
      - shot_framing
      - visual_style            # characters may be an empty array

system_template: |
  You are a senior visual‑prompt engineer.

  **Inputs you receive**
  • A description of the setting: **{shot_setting}**

  • A JSON object with shot‑framing details (e.g., framing type, camera angle, lens, movement):  
    **{shot_framing | tojson}**

  • A list of characters (may be empty). Each character object contains `name` and `descriptor`:  
    **{characters | tojson}**

  • Desired visual style: **{visual_style}**

  **What you must do**
  1. Draft an ultra‑detailed **image prompt** describing a single, high‑fidelity frame that reflects *all* of the above information.  
  2. Draft a **video prompt** that begins with the same frame but focuses solely on 3‑5 seconds of motion (camera movement, subject actions, environmental dynamics, pacing, ambient sounds).  
  3. **Character handling**  
     • If the `characters` array is non‑empty, mention each character *once*, appending a short identifier in parentheses (e.g., Anne (girl with long auburn hair)) to avoid subject confusion.  
     • If the array is empty, omit any character references completely.  
  4. Respond **only** via the function **generate_prompts**. Output must be valid JSON; do not include conversational text, questions, or explanations.

user_template: |
  # AI Image & Video Prompt Generator

  **Shot Setting:** {{ inputs.shot_setting }}

  **Shot Framing (JSON):** {{ inputs.shot_framing | tojson }}

  **Characters (JSON array):** {{ inputs.characters | tojson }}

  **Visual Style:** {{ inputs.visual_style }}

  ---
  Follow the system instructions and return your answer *only* via the `generate_prompts` function.

functions:
  - name: generate_prompts
    description: Generates image‑generation and video‑generation prompts in JSON format
    parameters:
      type: object
      properties:
        image_generation:
          type: object
          properties:
            prompt:
              type: string
          required: [prompt]
        video_generation:
          type: object
          properties:
            prompt:
              type: string
          required: [prompt]
      required: [image_generation, video_generation]