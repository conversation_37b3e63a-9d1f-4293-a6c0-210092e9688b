  id: storyboard_generator
  description: Converts a storyline and character list into a structured JSON storyboard shot list
  variables:
    - name: storyline
      type: string
      required: true
      description: The story's narrative description
    - name: characters
      type: string
      required: true
      description: A list of characters with name, gender, age, backstory, and personality
    - name: story_type
      type: string
      required: true
      description: The type of story to generate
  system_template: |
    You are an expert film director and storyboard artist who transforms a story and character descriptions into a detailed cinematic storyboard.

    IMPORTANT: You must ALWAYS return a complete JSON structure using function calling. NEVER respond conversationally, ask questions, or provide explanations. Your entire response must be structured JSON data only using the function.

    Even with minimal input, generate a creative and complete storyboard shot list of 5-10 shots. If the input is brief, expand creatively while maintaining narrative consistency.

  user_template: |
    # AI Storyboard Generator: From Storyline and Characters to JSON Storyboard

    ## Input:
    ## Story Type:
    {{ story_type }}

    STORYLINE:
    {{ storyline }}

    CHARACTERS:
    {{ characters }}

    ## Instructions:
    Analyze the storyline and character information and create a cinematic storyboard shot list with ~10 shots that fits the story type.

    Each shot must include:
    - shot_number (integer): sequential number of the shot
    - shot_name (string): descriptive name of the shot
    - shot_type (string): one of ["setting_only", "setting_with_actors_no_dialogue", "setting_with_actors_dialogue"]
    - shot_setting (string): detailed descriptions of what the setting of the shot looks like, including aesthetics (lighting, mood, colors, architecture, time of day, etc.). Even if it is similar to the previous shot, still describe everything in detail each shot. This will be used to generate a detailed setting image, to make sure to describe all details.
    - shot_framing (string): one of ["medium", "close-up", "wide angle"]
    - shot_angle (string): one of ["eye-level", "low angle", "high angle", "over the shoulder", "dutch angle"]
    - shot_movement (string): one of ["dolly in", "pan", "tracking shot", "static", "slow push in", "slow push out"]
    - movie_style (string): one of ["Realistic", "Pixar", "Ghibli", "Neo Art"] - the visual style of the movie

    A shot could also include:
    - character_composition: map of character names to a short description of what they are doing in the scene. Be specific about how they are interacting with eachother or their surroundings.
    - character_dialogue: map of dialogue to a character in the scene

    Some rules:
    - If shot_type is "setting_with_actors_no_dialogue" or "setting_with_actors_dialogue", also include:
      - character_composition: map of character names to a short description of what they are doing in the scene
    - If shot_type is "setting_with_actors_dialogue", also include: character_dialogue: map of character names to { line: string, emotion: string }
    - If shot_type is "setting_with_actors_dialogue", then there should be no other characters in the shot or only the back of a character if it is an "over the shoulder shot"
    - If shot_type is "setting_only", do NOT include character_composition or character_dialogue.
    - Only shot_type "setting_with_actors_dialogue" could have "over the shoulder" shot_angle.
    - Each shot should have at most one character dialogue. Do not include multiple character dialogues in the same shot..
    - Should choose character model from characters maybe in the sence

    ## Format Requirements:
    - IMPORTANT: YOU MUST USE FUNCTION CALLING to return your response in JSON format
    - DO NOT respond conversationally or ask follow-up questions
    - ALWAYS return a complete storyboard even if input is minimal
    - Generate between 5-10 shots

  functions:
    - name: generate_storyboard
      description: Generates a structured storyboard shot list in JSON format from a storyline and characters
      parameters:
        type: object
        properties:
          shots:
            type: array
            description: List of storyboard shots
            items:
              type: object
              properties:
                shot_number:
                  type: integer
                shot_name:
                  type: string
                shot_type:
                  type: string
                  enum: ["setting_only", "setting_with_actors_no_dialogue", "setting_with_actors_dialogue"]
                shot_setting:
                  type: string
                shot_framing:
                  type: string
                  enum: ["medium close-up", "over the shoulder", "wide angle"]
                shot_angle:
                  type: string
                  enum: ["eye-level", "low angle", "high angle", "over the shoulder", "dutch angle"]
                shot_movement:
                  type: string
                  enum: ["dolly in", "pan", "tracking shot", "static", "slow push in", "slow push out"]
                characters:
                  type: array
                  description: select from characters list and maybe in the shot
                  items:
                    type: object
                character_composition:
                  type: object
                  description: Map of character names to their description in scene
                  additionalProperties:
                    type: string
                    description: Description of what the character is doing in the scene
                character_dialogue:
                  type: object
                  description: Single character dialogue for the shot (only if shot_type = setting_with_actors_dialogue)
                  properties:
                    character_name:
                      type: string
                      description: Name of the character speaking
                    line:
                      type: string
                      description: The dialogue line
                    emotion:
                      type: string
                      description: The emotion with which the line is delivered
                  required:
                    - character_name
                    - line
                    - emotion
                movie_style:
                  type: string
                  description: The visual style of the movie
              required:
                - shot_number
                - shot_name
                - shot_type
                - shot_setting
                - shot_framing
                - shot_angle
                - shot_movement
                - movie_style
        required:
          - shots
