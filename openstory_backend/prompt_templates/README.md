# Prompt Templates

This directory contains YAML templates for various LLM prompts used in the application. Each template defines the structured inputs, outputs, and logic for a specific LLM use case.

## Template Format

Templates are defined in YAML files with the following structure:

```yaml
id: unique_template_id
description: "A brief description of what this template does"

variables:
  - name: variable_name
    type: string|number|boolean|date|array|object
    required: true|false
    default: default_value  # Optional
    description: "Description of the variable"

system_template: |
  This is the system prompt for the LLM.
  It can reference {{ variables }} and use Jinja2 template syntax.

user_template: |
  This is the user prompt for the LLM.
  It can also reference {{ variables }} and use Jinja2 template syntax.
  {% if condition %}
  It can include conditional logic.
  {% endif %}

functions:
  - name: function_name
    description: "Description of the function"
    parameters:
      type: object
      properties:
        # Function parameters defined in JSON Schema format
```

## Variables

Variables define the inputs that the template requires. Each variable has:

- `name`: The variable identifier (used with `{{ name }}` in templates)
- `type`: The data type (string, number, boolean, date, array, object)
- `required`: Whether the variable is required (true/false)
- `default`: Optional default value if not provided
- `description`: A description of the variable

## Template Syntax

Templates use [Jinja2](https://jinja.palletsprojects.com/) syntax, which supports:

- Variable substitution: `{{ variable_name }}`
- Conditionals: `{% if condition %}...{% else %}...{% endif %}`
- Loops: `{% for item in items %}...{% endfor %}`
- Filters: `{{ value|filter }}`

### Available Custom Filters

- `format_date`: Format a date string or object - `{{ date|format_date('%Y-%m-%d') }}`

## Functions

Functions define structured outputs that the LLM can generate. They follow the [OpenAI function calling format](https://platform.openai.com/docs/guides/function-calling).

## Usage Example

To call a template from code:

```python
from app.services.prompt_service import get_prompt_service

prompt_service = get_prompt_service()
rendered = prompt_service.render_template(
    template_id="episode_generator",
    variables={
        "user_story": "A thrilling story about..."
    }
)

# rendered contains system_prompt, user_prompt, and functions
```

To call a template via the API:

```
POST /api/v1/llm/stream
{
  "template_id": "episode_generator",
  "variables": {
    "user_story": "A thrilling story about..."
  },
  "model": "gpt-4o",
  "temperature": 0.7
}
```

## Creating New Templates

1. Create a new YAML file in this directory
2. Define your template following the format above
3. Test it via the API or in your code
4. The template will be automatically loaded when the service starts

Templates can be reloaded at runtime using the PromptService's `reload_templates()` method. 