import os
from dotenv import load_dotenv
from supabase import create_client

# Load environment variables
load_dotenv()

# Get Supabase connection details
supabase_url = os.getenv("SUPABASE_URL", "http://127.0.0.1:54321")
supabase_key = os.getenv("SUPABASE_KEY")
email = os.getenv("SUPABASE_EMAIL")
password = os.getenv("SUPABASE_PASSWORD")

if not supabase_key or not email or not password:
    print("Please set SUPABASE_KEY, SUPABASE_EMAIL and SUPABASE_PASSWORD in .env file")
    exit(1)

# Initialize Supabase client
supabase = create_client(supabase_url, supabase_key)

# Sign in and get token
try:
    print(f"Authenticating with Supabase at {supabase_url}...")
    auth_response = supabase.auth.sign_in_with_password({
        "email": email,
        "password": password
    })
    
    if auth_response.session:
        token = auth_response.session.access_token
        print("\nJWT Token (copy this):\n")
        print(token)
        print("\nAdd this to your .env file as SUPABASE_JWT_TOKEN=<token>")
    else:
        print("Authentication succeeded but no session returned")
except Exception as e:
    print(f"Error authenticating with Supabase: {e}") 