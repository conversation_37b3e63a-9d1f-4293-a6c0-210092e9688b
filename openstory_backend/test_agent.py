#!/usr/bin/env python3
"""
Test script for the LlamaIndex Story Agent
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

async def test_agent():
    """Test the story agent functionality"""
    try:
        from app.services.agent_service import story_agent_service
        
        print("✅ Agent service imported successfully")
        
        # Test basic chat
        test_message = "Help me create a story about a brave knight"
        print(f"\n🧪 Testing agent with message: '{test_message}'")
        
        response = await story_agent_service.chat(test_message)
        print(f"📝 Agent response: {response}")
        
        print("\n✅ Agent test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error testing agent: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_agent())
