import logging
import pytest
from app.db.supabase import BucketEnum, StorageService

logger = logging.getLogger(__name__)


@pytest.mark.asyncio
async def test_upload_file():
    storage_service = StorageService(BucketEnum.AUDIO)
    with open("/Users/<USER>/Workspaces/PenyoAI/Test.mp3", "rb") as file:
        url = await storage_service.upload_file("Test.mp3", file.read())
        logger.info(f"file url: {url}")
        print(url)
