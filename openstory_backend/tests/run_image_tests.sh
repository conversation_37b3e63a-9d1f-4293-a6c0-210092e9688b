#!/bin/bash

# Script to run the image generation tests

# Set up environment
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
PROJECT_ROOT=$(dirname "$SCRIPT_DIR")
cd "$PROJECT_ROOT" || { echo "Failed to change to project root directory"; exit 1; }

# Make sure pytest is installed
python -m pip install pytest pytest-asyncio pillow

# Print test environment info
echo "===== Test Environment ====="
echo "Project root: $PROJECT_ROOT"
echo "Python: $(python --version)"
echo "Supabase URL: ${SUPABASE_URL:-'Not set'}"
echo "FAL API Key: ${FAL_KEY:0:4}... (${#FAL_KEY} chars)"
echo "========================="

# Run unit tests first
echo -e "\n===== Running Unit Tests (Mock Supabase/FAL) ====="
python -m pytest -v tests/test_image_generation.py

# Only run integration tests if FAL_KEY is provided
if [ -n "$FAL_KEY" ]; then
    echo -e "\n===== Running Integration Tests (Real API Calls) ====="
    python -m pytest -v tests/test_image_generation_integration.py
else
    echo -e "\n⚠️  Skipping integration tests: FAL_KEY environment variable not set"
    echo "To run integration tests, set your FAL_KEY environment variable:"
    echo "export FAL_KEY=your_fal_api_key"
fi 