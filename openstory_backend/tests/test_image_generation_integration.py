#!/usr/bin/env python3
import os
import sys
import uuid
import pytest
import logging
import requests
import subprocess
import time
from pathlib import Path
from PIL import Image
from io import BytesIO

# Add project root to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.services.image_generation import (
    FalImageGenerationService,
    ImageGenerationConfig,
    ImageGenerationResult,
)
from app.db.supabase import supabase_client

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Test constants
TEST_PROMPT = "a realistic portrait of a character with detailed features, professional photograph"
TEST_CHARACTER_ID = str(uuid.uuid4())  # Generate a unique ID for testing
TEST_IMAGE_PATH = os.path.join(
    os.path.dirname(__file__), "test_assets", "test_character.jpg"
)

# Skip the integration test if FAL_KEY is not available
pytestmark = pytest.mark.skipif(
    not os.getenv("FAL_KEY"), reason="FAL_KEY environment variable not set"
)


@pytest.fixture(scope="module")
def ensure_bucket_exists():
    """Ensure the 'characters' bucket exists in Supabase"""
    supabase = supabase_client
    try:
        # Check if bucket exists
        buckets = supabase.storage.list_buckets()
        bucket_names = [bucket["name"] for bucket in buckets]

        if "characters" not in bucket_names:
            logger.info("Creating 'characters' bucket for testing")
            response = supabase.storage.create_bucket("characters", {"public": False})
            logger.info(f"Bucket creation response: {response}")
        else:
            logger.info("'characters' bucket already exists")

        # Create test directory if it doesn't exist
        test_assets_dir = os.path.join(os.path.dirname(__file__), "test_assets")
        Path(test_assets_dir).mkdir(exist_ok=True)

        # Create a test image if it doesn't exist
        if not os.path.exists(TEST_IMAGE_PATH):
            # Generate a simple test image
            img = Image.new("RGB", (512, 512), color="white")
            # Add a simple pattern
            for i in range(0, 512, 10):
                for j in range(0, 512, 10):
                    img.putpixel((i, j), (255, 0, 0))
            img.save(TEST_IMAGE_PATH)
            logger.info(f"Created test image at {TEST_IMAGE_PATH}")

        return True
    except Exception as e:
        logger.error(f"Error setting up bucket: {e}")
        return False


@pytest.fixture
def service():
    """Create a FalImageGenerationService instance for testing with real FAL key"""
    fal_key = os.getenv("FAL_KEY")
    service = FalImageGenerationService(api_key=fal_key)
    yield service
    service.shutdown()  # Clean up after tests


class TestImageGenerationIntegration:
    @pytest.mark.integration
    def test_generate_and_store_image(self, service, ensure_bucket_exists):
        """Integration test for generating an image and storing it in Supabase"""
        # Skip if bucket setup failed
        if not ensure_bucket_exists:
            pytest.skip("Failed to set up test bucket")

        # Create a test character record in the database
        supabase = supabase_client

        try:
            # Create a test character
            character = {
                "id": TEST_CHARACTER_ID,
                "name": f"Test Character {uuid.uuid4().hex[:8]}",
                "description": "A test character for integration testing",
            }

            try:
                # Check if character already exists
                result = (
                    supabase.table("characters")
                    .select("id")
                    .eq("id", TEST_CHARACTER_ID)
                    .execute()
                )
                if not result.data:
                    char_result = (
                        supabase.table("characters").insert(character).execute()
                    )
                    logger.info(f"Created test character: {char_result.data}")
                else:
                    logger.info(
                        f"Test character already exists with ID: {TEST_CHARACTER_ID}"
                    )
            except Exception as e:
                logger.warning(
                    f"Could not create character record, tests may fail: {e}"
                )

            # Configure image generation
            config = ImageGenerationConfig(
                prompt=TEST_PROMPT,
                negative_prompt="blurry, distorted, low quality",
                model_id="fal-ai/fast-sdxl",  # Use a reliable model
                character_id=TEST_CHARACTER_ID,
                width=512,
                height=512,
                num_inference_steps=20,  # Faster generation for testing
            )

            # Generate the image
            logger.info(f"Generating image with character_id: {TEST_CHARACTER_ID}")
            start_time = time.time()
            result = service.generate_image(config)
            elapsed = time.time() - start_time
            logger.info(f"Image generation took {elapsed:.2f} seconds")

            # Basic assertions
            assert result.success is True, f"Image generation failed: {result.error}"
            assert result.image_url is not None, "No image URL was returned"

            # Print result info
            logger.info(f"Generated image URL: {result.image_url}")
            logger.info(f"Is URL from Supabase? {'supabase' in result.image_url}")

            # Verify that the image can be downloaded
            try:
                response = requests.get(result.image_url, timeout=10)
                response.raise_for_status()
                logger.info(
                    f"Successfully downloaded image: {len(response.content)} bytes"
                )
            except Exception as e:
                logger.error(f"Failed to download generated image: {e}")
                pytest.fail(f"Generated image could not be downloaded: {e}")

            # Check that the image metadata was stored in the character_images table
            try:
                query_result = (
                    supabase.table("character_images")
                    .select("*")
                    .eq("character_id", TEST_CHARACTER_ID)
                    .execute()
                )
                logger.info(f"Found {len(query_result.data)} images for character")

                if query_result.data:
                    # At least one image record exists
                    assert len(query_result.data) > 0, (
                        "No image records found in database"
                    )

                    # Get the latest image
                    latest_image = max(
                        query_result.data, key=lambda img: img.get("created_at", "")
                    )
                    logger.info(f"Latest image for character: {latest_image}")

                    # Verify image properties
                    assert latest_image["bucket"] == "characters", "Wrong bucket name"
                    assert latest_image["character_id"] == TEST_CHARACTER_ID, (
                        "Wrong character ID"
                    )
                    assert latest_image["width"] > 0, "Invalid image width"
                    assert latest_image["height"] > 0, "Invalid image height"

                    # Try to download the image directly from storage
                    try:
                        path = latest_image["path"]
                        signed_url = supabase.storage.from_(
                            "characters"
                        ).create_signed_url(path, 60)
                        assert signed_url.data, "Failed to create signed URL"

                        logger.info(
                            f"Created signed URL for direct storage access: {signed_url.data.get('signedURL')}"
                        )
                    except Exception as e:
                        logger.warning(
                            f"Could not create signed URL (non-critical): {e}"
                        )

                    return True
                else:
                    logger.warning(
                        "No image records found in database, but generation reported success"
                    )
                    return False
            except Exception as e:
                logger.error(f"Error checking character_images table: {e}")
                return False
        except Exception as e:
            logger.error(f"Integration test error: {e}")
            pytest.fail(f"Integration test failed: {e}")


if __name__ == "__main__":
    pytest.main(["-v", __file__])

