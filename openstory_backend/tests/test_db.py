import uuid
from sqlalchemy.orm import Session
from app.db.session import SessionL<PERSON>al
from app.models import User, Show, Episode

def test_database_connection():
    # Create a new database session
    db = SessionLocal()
    try:
        # Create a test user with a unique email that won't conflict with seed data
        test_user = User(
            email="<EMAIL>",
            username="testuser_unique",
            first_name="Test",
            display_name="Test User"
        )
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        print(f"Created user: {test_user.id}")

        # Create a show for the user
        test_show = Show(
            user_id=test_user.id,
            title="Test Show Unique",
            description="A test show for database testing"
        )
        db.add(test_show)
        db.commit()
        db.refresh(test_show)
        print(f"Created show: {test_show.id}")

        # Create an episode for the show
        test_episode = Episode(
            show_id=test_show.id,
            title="Test Episode Unique",
            synopsis="A test episode for database testing",
            oneliner="Test episode oneliner"
        )
        db.add(test_episode)
        db.commit()
        db.refresh(test_episode)
        print(f"Created episode: {test_episode.id}")

        # Test relationships
        print("\nTesting relationships:")
        print(f"User's shows: {len(test_user.shows)}")
        print(f"Show's episodes: {len(test_show.episodes)}")
        print(f"Episode's show title: {test_episode.show.title}")

        # Clean up
        db.delete(test_episode)
        db.delete(test_show)
        db.delete(test_user)
        db.commit()
        print("\nTest data cleaned up successfully")

    except Exception as e:
        print(f"Error during test: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    test_database_connection() 