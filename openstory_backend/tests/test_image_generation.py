#!/usr/bin/env python3
import os
import sys
import uuid
import pytest
import logging
import requests
from unittest.mock import patch, MagicMock, ANY
from io import Bytes<PERSON>
from PIL import Image
import numpy as np

# Add project root to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from app.services.image_generation import (
    FalImageGenerationService,
    ImageGenerationConfig,
    ImageGenerationResult,
)

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Test constants
TEST_PROMPT = "a realistic portrait of a character with detailed features"
TEST_MODEL = "fal-ai/fast-sdxl"
TEST_CHARACTER_ID = "test-character-id"
TEST_IMAGE_WIDTH = 512
TEST_IMAGE_HEIGHT = 512


# Helper function to create a test image
def create_test_image(width=512, height=512):
    """Create a simple test image for mocking responses"""
    image_array = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    image = Image.fromarray(image_array)
    img_byte_arr = BytesIO()
    image.save(img_byte_arr, format="JPEG")
    return img_byte_arr.getvalue()


class TestFalImageGenerationService:
    @pytest.fixture
    def service(self):
        """Create a FalImageGenerationService instance for testing"""
        return FalImageGenerationService(api_key="test_key", max_concurrent=1)

    @pytest.fixture
    def mock_fal_client(self):
        """Mock the fal_client.subscribe method"""
        with patch("fal_client.subscribe") as mock_subscribe:
            # Create a mock successful response from FAL API
            mock_response = {
                "images": [
                    {
                        "url": "https://fal.ai/fake-image-url.jpg",
                        "width": TEST_IMAGE_WIDTH,
                        "height": TEST_IMAGE_HEIGHT,
                    }
                ],
                "meta": {"seed": 123456, "model": TEST_MODEL},
            }
            mock_subscribe.return_value = mock_response
            yield mock_subscribe

    @pytest.fixture
    def mock_supabase_client(self):
        """Mock the Supabase client for testing storage operations"""
        with patch("app.db.supabase.get_supabase_client") as mock_get_client:
            # Create a mock Supabase client
            mock_client = MagicMock()

            # Mock storage operations
            mock_storage = MagicMock()
            mock_from = MagicMock()

            # Mock upload method
            mock_upload_response = MagicMock()
            mock_upload_response.error = None
            mock_from.upload.return_value = mock_upload_response

            # Mock create_signed_url method
            mock_signed_url_response = MagicMock()
            mock_signed_url_response.error = None
            mock_signed_url_response.data = {
                "signedURL": "https://supabase-signed-url/characters/test-image.jpg"
            }
            mock_from.create_signed_url.return_value = mock_signed_url_response

            # Connect the mocks
            mock_storage.from_.return_value = mock_from
            mock_client.storage = mock_storage

            # Mock table operations
            mock_table = MagicMock()
            mock_insert = MagicMock()
            mock_insert_response = MagicMock()
            mock_insert_response.error = None
            mock_insert_response.data = [{"id": "test-image-id"}]
            mock_insert.execute.return_value = mock_insert_response
            mock_table.insert.return_value = mock_insert
            mock_client.table.return_value = mock_table

            # Return the mock client
            mock_get_client.return_value = mock_client
            yield mock_client

    @pytest.fixture
    def mock_requests(self):
        """Mock requests.get for downloading images"""
        with patch("requests.get") as mock_get:
            # Create a mock response with test image data
            mock_response = MagicMock()
            mock_response.content = create_test_image()
            mock_response.headers = {"content-type": "image/jpeg"}
            mock_response.raise_for_status = MagicMock()
            mock_get.return_value = mock_response
            yield mock_get

    def test_generate_image_without_character_id(self, service, mock_fal_client):
        """Test image generation without character_id (no storage)"""
        # Configure test
        config = ImageGenerationConfig(
            prompt=TEST_PROMPT,
            model_id=TEST_MODEL,
        )

        # Execute
        result = service.generate_image(config)

        # Assertions
        assert result.success is True
        assert result.model_id == TEST_MODEL
        assert result.image_url == "https://fal.ai/fake-image-url.jpg"

        # Verify FAL client was called correctly
        mock_fal_client.assert_called_once()
        args, kwargs = mock_fal_client.call_args
        assert args[0] == TEST_MODEL
        assert kwargs["arguments"]["prompt"] == TEST_PROMPT

    def test_generate_image_with_character_id_storage_success(
        self, service, mock_fal_client, mock_supabase_client, mock_requests
    ):
        """Test image generation with character_id that stores to Supabase"""
        # Configure test
        config = ImageGenerationConfig(
            prompt=TEST_PROMPT,
            model_id=TEST_MODEL,
            character_id=TEST_CHARACTER_ID,
            width=TEST_IMAGE_WIDTH,
            height=TEST_IMAGE_HEIGHT,
        )

        # Execute
        result = service.generate_image(config)

        # Assertions
        assert result.success is True
        assert result.model_id == TEST_MODEL
        assert "supabase-signed-url" in result.image_url

        # Verify FAL client was called
        mock_fal_client.assert_called_once()

        # Verify Supabase storage operations
        mock_storage = mock_supabase_client.storage
        mock_storage.from_.assert_called_with("characters")
        mock_from = mock_storage.from_.return_value
        mock_from.upload.assert_called_once()

        # Verify character_images table insert
        mock_supabase_client.table.assert_called_with("character_images")
        mock_table = mock_supabase_client.table.return_value
        mock_table.insert.assert_called_once()

        # Verify the inserted data
        insert_call = mock_table.insert.call_args
        insert_data = insert_call[0][0]
        assert insert_data["character_id"] == TEST_CHARACTER_ID
        assert insert_data["bucket"] == "characters"
        assert "path" in insert_data
        assert insert_data["width"] == TEST_IMAGE_WIDTH
        assert insert_data["height"] == TEST_IMAGE_HEIGHT
        assert "mime_type" in insert_data
        assert "file_size" in insert_data
        assert "sha256" in insert_data

        # Verify signed URL was created
        mock_from.create_signed_url.assert_called_once()

    def test_generate_image_storage_failure_uses_fallback(
        self, service, mock_fal_client, mock_supabase_client, mock_requests
    ):
        """Test that fallback to FAL URL occurs when storage fails"""
        # Configure storage failure
        mock_from = mock_supabase_client.storage.from_.return_value
        mock_upload_response = MagicMock()
        mock_upload_response.error = MagicMock()
        mock_upload_response.error.message = "Storage error"
        mock_from.upload.return_value = mock_upload_response

        # Configure test
        config = ImageGenerationConfig(
            prompt=TEST_PROMPT,
            model_id=TEST_MODEL,
            character_id=TEST_CHARACTER_ID,
        )

        # Execute
        result = service.generate_image(config)

        # Assertions
        assert result.success is True
        assert result.model_id == TEST_MODEL
        assert (
            result.image_url == "https://fal.ai/fake-image-url.jpg"
        )  # Using FAL URL as fallback

        # Verify storage was attempted
        mock_supabase_client.storage.from_.assert_called_with("characters")
        mock_from.upload.assert_called_once()

    def test_generate_image_fal_api_failure(self, service):
        """Test handling of FAL API failures"""
        # Mock FAL API failure
        with patch("fal_client.subscribe", side_effect=Exception("API error")):
            # Configure test
            config = ImageGenerationConfig(
                prompt=TEST_PROMPT,
                model_id=TEST_MODEL,
            )

            # Execute
            result = service.generate_image(config)

            # Assertions
            assert result.success is False
            assert "API error" in result.error
            assert result.image_url is None

    @pytest.mark.asyncio
    async def test_generate_image_async(
        self, service, mock_fal_client, mock_supabase_client, mock_requests
    ):
        """Test the async image generation method"""
        # Configure test
        config = ImageGenerationConfig(
            prompt=TEST_PROMPT,
            model_id=TEST_MODEL,
            character_id=TEST_CHARACTER_ID,
        )

        # Execute
        result = await service.generate_image_async(config)

        # Assertions
        assert result.success is True
        assert result.model_id == TEST_MODEL
        assert "supabase-signed-url" in result.image_url

        # Verify operations occurred as expected
        mock_fal_client.assert_called_once()
        mock_supabase_client.storage.from_.assert_called_with("characters")


if __name__ == "__main__":
    pytest.main()

