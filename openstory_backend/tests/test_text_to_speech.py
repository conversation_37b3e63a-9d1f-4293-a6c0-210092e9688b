import pytest
from app.services.media_service import (
    TTSRequest,
    ImageToVideoRequest,
    elevenlabs_service,
    runway_service,
)


@pytest.mark.asyncio
async def test_text_to_speech():
    tts_request = TTSRequest(text="hello, how are you?")
    audio_url = await elevenlabs_service.text_to_speech(tts_request)
    print(audio_url)


@pytest.mark.skip()
@pytest.mark.asyncio
async def test_image_to_video():
    image_url = ""
    itv_request = ImageToVideoRequest(prompt_image=image_url)
    video_url = await runway_service.image_to_video(itv_request)
    print(video_url)
