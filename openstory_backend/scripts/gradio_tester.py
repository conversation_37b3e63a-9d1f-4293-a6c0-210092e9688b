#!/usr/bin/env python3
import sys
import os

# Add the project root to the Python path so we can import app modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

import gradio as gr
import requests
import json
import os
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv
from supabase import create_client, Client
import jwt
import uuid
import time
import logging
import pandas as pd
from media_config import (
    SHOT_IMAGES, SHOT_VIDEOS, FINAL_PRODUCT_VIDEO, 
    DEFAULT_SHOT_IMAGE, DEFAULT_SHOT_VIDEO, VIDEO_STATUS,
    HARDCODED_RESPONSES
)

# Set up logger
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# Import character prompt generator components
from app.services.character_services import CharacterDetails, generate_character_prompt, get_available_styles
from app.core.config import get_settings

# Load environment variables
load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
API_TOKEN = os.getenv("SUPABASE_JWT_TOKEN", "")
SUPABASE_URL = os.getenv("SUPABASE_URL", "http://127.0.0.1:54321")
SUPABASE_KEY = os.getenv("SUPABASE_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0")
FAL_KEY = os.getenv("FAL_KEY", "")  # Added for image generation

# Initialize Supabase client
supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

# Ensure the characters bucket exists
def ensure_characters_bucket_exists():
    try:
        # Check if bucket exists
        buckets = supabase.storage.list_buckets()
        bucket_names = [bucket['name'] for bucket in buckets]
        
        if 'characters' not in bucket_names:
            print("Creating 'characters' bucket in Supabase Storage...")
            response = supabase.storage.create_bucket('characters', {'public': False})
            print("Bucket creation response:", response)
        else:
            print("'characters' bucket already exists in Supabase Storage")
        
        # List files in bucket for debugging
        try:
            files = supabase.storage.from_('characters').list()
            print(f"Files in 'characters' bucket: {files}")
        except Exception as e:
            print(f"Could not list files in 'characters' bucket: {str(e)}")
            
    except Exception as e:
        print(f"Error checking/creating storage bucket: {str(e)}")

# Try to ensure the bucket exists
ensure_characters_bucket_exists()

# API functions
def api_headers():
    headers = {
        "Content-Type": "application/json",
    }
    if API_TOKEN:
        headers["Authorization"] = f"Bearer {API_TOKEN}"
    return headers

def api_request(endpoint: str, method: str = "GET", data: Optional[Dict] = None) -> Dict:
    """Make a request to the API and return the JSON response"""
    global API_TOKEN
    
    # Make sure we don't duplicate /api/v1 in the URL if it's already in the API_BASE_URL
    if endpoint.startswith("api/v1/") and API_BASE_URL.endswith("/api/v1"):
        # Remove api/v1/ from the beginning of the endpoint
        endpoint = endpoint[7:]
    
    url = f"{API_BASE_URL.rstrip('/')}/{endpoint.lstrip('/')}"
    
    try:
        print(f"Making {method} request to {url}")  # Debug log
        print(f"Headers: {api_headers()}")  # Debug log
        if data:
            print(f"Data: {json.dumps(data)}")  # Debug log
        
        if method == "GET":
            response = requests.get(url, headers=api_headers())
        elif method == "POST":
            response = requests.post(url, headers=api_headers(), json=data)
        elif method == "PUT":
            response = requests.put(url, headers=api_headers(), json=data)
        elif method == "DELETE":
            response = requests.delete(url, headers=api_headers())
        else:
            return {"error": f"Unsupported method: {method}"}
        
        # Check for unauthorized (likely expired token)
        if response.status_code == 401:
            print("Token expired. Attempting to refresh...")
            # Try to login again
            login_result = login_to_supabase("<EMAIL>", "testing")
            if login_result.get("success", False):
                print("Token refreshed. Retrying request...")
                # Retry the request with new token
                if method == "GET":
                    response = requests.get(url, headers=api_headers())
                elif method == "POST":
                    response = requests.post(url, headers=api_headers(), json=data)
                elif method == "PUT":
                    response = requests.put(url, headers=api_headers(), json=data)
                elif method == "DELETE":
                    response = requests.delete(url, headers=api_headers())
            else:
                print("Failed to refresh token")
                return {"error": "Authentication failed: Token expired and refresh failed"}
        
        response.raise_for_status()
        return response.json()
    except requests.exceptions.ConnectionError as e:
        print(f"Connection error: {str(e)}")
        return {"error": f"Could not connect to API server at {API_BASE_URL}. Please make sure the backend server is running."}
    except requests.exceptions.RequestException as e:
        if hasattr(e, 'response') and e.response:
            try:
                error_detail = e.response.json()
                return {"error": f"API Error: {e.response.status_code} - {error_detail.get('detail', str(e))}"}
            except:
                return {"error": f"API Error: {e.response.status_code} - {str(e)}"}
        return {"error": f"Request Error: {str(e)}"}
    except Exception as e:
        return {"error": f"Unexpected error: {str(e)}"}

def login_to_supabase(email, password):
    global API_TOKEN
    try:
        # Add debug logging
        print(f"Attempting to login with email: {email}")
        print(f"Using Supabase URL: {SUPABASE_URL}")
        
        response = supabase.auth.sign_in_with_password({
            "email": email,
            "password": password
        })
        
        # Add debug logging
        print("Login response received")
        
        jwt_token = response.session.access_token
        
        # Debug: Print token info
        token_data = jwt.decode(jwt_token, options={"verify_signature": False}, algorithms=["HS256"])
        print(f"Token payload: {json.dumps(token_data, indent=2)}")
        
        API_TOKEN = jwt_token
        return {
            "success": True,
            "message": "Login successful! Token obtained.",
            "token": jwt_token[:20] + "..." if jwt_token else None,  # Only show first part for security
            "payload": token_data
        }
    except Exception as e:
        print(f"Login error details: {str(e)}")
        return {
            "success": False,
            "message": f"Login failed: {str(e)}",
            "token": None
        }

def generate_episode_content(user_story, model="gpt-4o-mini", temperature=0.7, demo_mode=False):
    if demo_mode:
        print("Using hardcoded episode content (demo mode)")
        return HARDCODED_RESPONSES["episode_generator"]
    
    data = {
        "template_id": "episode_generator",
        "variables": {
            "user_story": user_story
        },
        "model": model,
        "temperature": temperature
    }
    print(f"Calling LLM API with: {json.dumps(data, indent=2)}")
    result = api_request("api/v1/llm/prompt", "POST", data)
    print(f"LLM API response received, type: {type(result)}")
    return result

def generate_show_storyboard(storyline, characters, model="gpt-4o", temperature=0.7, demo_mode=False):
    """Generate storyboard using the shows API storyboard endpoint"""
    if demo_mode:
        print("Using hardcoded storyboard content (demo mode)")
        return HARDCODED_RESPONSES["storyboard_generator"]
    
    data = {
        "storyline": storyline,
        "characters": characters,
        "model": model,
        "temperature": temperature
    }
    
    print("=" * 40)
    print("STORYBOARD API REQUEST:")
    print(f"URL: api/v1/shows/storyboard")
    print(f"Model: {model}, Temperature: {temperature}")
    print(f"Storyline length: {len(storyline)} chars")
    print(f"Characters: {characters[:100]}...")
    print("=" * 40)
    
    # Make the API call
    result = api_request("api/v1/shows/storyboard", "POST", data)
    
    # Log the results
    print("=" * 40)
    print("STORYBOARD API RESPONSE:")
    if isinstance(result, dict):
        if "error" in result:
            print(f"ERROR: {result['error']}")
        elif "shots" in result:
            print(f"SUCCESS: {len(result['shots'])} shots generated")
        else:
            print(f"UNEXPECTED FORMAT. Keys: {list(result.keys())}")
    else:
        print(f"NON-DICT RESPONSE: {type(result)}")
    print("=" * 40)
    
    return result

# Image Generation Functions
def get_available_models():
    """Get list of available image generation models"""
    result = api_request("api/v1/images/models")
    
    if "error" in result:
        # Fallback models if API call fails
        return [
            "fal-ai/fast-sdxl",
            "fal-ai/magnetique-v3.5",
            "fal-ai/hidream-i1-dev",
            "fal-ai/hidream-i1-fast",
            "fal-ai/flux/dev"
        ]
    
    return [model["id"] for model in result.get("models", [])]

def generate_image(prompt, model_id, negative_prompt, width, height, steps, seed, guidance_scale, character_id=None):
    """Generate an image with the specified parameters"""
    # Prepare the request body
    data = {
        "prompt": prompt,
        "model_id": model_id,
        "negative_prompt": negative_prompt,
        "width": int(width),
        "height": int(height),
        "num_inference_steps": int(steps)
    }
    if character_id:
        data["character_id"] = character_id
    
    # Only add guidance_scale for models that need it
    guidance_models = [
        "fal-ai/fast-sdxl",
        "fal-ai/magnetique-v3.5",
        "fal-ai/flux/dev"
    ]
    if model_id in guidance_models:
        data["guidance_scale"] = float(guidance_scale)
    
    # Only include seed if it's provided
    if seed and seed.strip():
        try:
            data["seed"] = int(seed)
        except ValueError:
            # If seed is not a valid integer, ignore it
            pass
    
    return api_request("api/v1/images/generate", "POST", data)

def save_character_image(char_name, image_url):
    """Save the character's image URL to Supabase"""
    try:
        print(f"Attempting to save image URL '{image_url}' for character '{char_name}'")
        
        # First check if character exists
        char_check = supabase.table('characters').select('id').eq('name', char_name).execute()
        if not char_check.data or len(char_check.data) == 0:
            print(f"Character '{char_name}' does not exist in database. Creating it.")
            # Create the character if it doesn't exist
            create_response = supabase.table('characters').insert({
                'name': char_name,
                'avatar_urls': [image_url]
            }).execute()
            
            if create_response.error:
                print(f"Error creating character: {create_response.error.message}")
                return {
                    "success": False,
                    "message": f"Failed to create character: {create_response.error.message}"
                }
            
            print(f"Created character: {create_response.data}")
            return {
                "success": True,
                "message": f"Created character {char_name} with image",
                "data": create_response.data
            }
        
        # Character exists, update the avatar_urls
        char_id = char_check.data[0]['id']
        print(f"Found character '{char_name}' with ID: {char_id}")
        
        # Check if image_url is external (http/https)
        if image_url.startswith(('http://', 'https://')):
            try:
                # Download the image
                print(f"Downloading image from URL: {image_url}")
                response = requests.get(image_url, timeout=30)
                response.raise_for_status()
                image_data = response.content
                
                # Generate a unique filename
                file_ext = "jpg"  # Default extension
                content_type = response.headers.get('content-type', '')
                if 'png' in content_type:
                    file_ext = 'png'
                elif 'jpeg' in content_type or 'jpg' in content_type:
                    file_ext = 'jpg'
                
                filename = f"{char_name.replace(' ', '_').lower()}_{uuid.uuid4()}.{file_ext}"
                path_in_bucket = f"{char_id}/{filename}"
                
                # Upload to Supabase Storage
                print(f"Uploading image to Supabase: {path_in_bucket}")
                storage_response = supabase.storage.from_('characters').upload(
                    path=path_in_bucket,
                    file=image_data, 
                    file_options={"content-type": content_type}
                )
                
                if hasattr(storage_response, 'error') and storage_response.error:
                    print(f"Storage upload error: {storage_response.error}")
                    # Try updating with original URL as fallback
                    print("Falling back to original URL without storage")
                else:
                    print(f"Successfully uploaded image to storage: {path_in_bucket}")
                    # Get a public URL for the uploaded file
                    try:
                        # First try with get_public_url
                        public_url = supabase.storage.from_('characters').get_public_url(path_in_bucket)
                        print(f"Public URL: {public_url}")
                        image_url = public_url
                    except:
                        try:
                            # Try with create_signed_url as fallback
                            signed_url = supabase.storage.from_('characters').create_signed_url(path_in_bucket, 60*60*24)
                            print(f"Signed URL: {signed_url}")
                            if signed_url and 'signedURL' in signed_url.data:
                                image_url = signed_url.data['signedURL']
                        except Exception as url_err:
                            print(f"Could not get URL for uploaded file: {url_err}")
            except Exception as download_err:
                print(f"Error downloading/uploading image: {download_err}")
                # Continue with original URL
        
        # Update the character's avatar_urls
        response = supabase.table('characters').update({
            'avatar_urls': [image_url]  # Store as an array of URLs
        }).eq('id', char_id).execute()
        
        # Check for Supabase errors
        if response.error:
            print(f"Supabase error updating avatar_urls: {response.error.message}")
            return {
                "success": False,
                "message": f"Failed to save image due to Supabase error: {response.error.message}"
            }

        # Check if data was returned
        if response.data:
            print(f"Successfully saved image URL for character {char_name}. Data: {response.data}")
            return {
                "success": True,
                "message": f"Successfully saved image for {char_name}",
                "data": response.data
            }
        else:
            print(f"No rows updated for character {char_name}.")
            return {
                "success": False,
                "message": f"No character with ID {char_id} found to update, or image was already set.",
                "data": []
            }

    except Exception as e:
        print(f"Exception saving character image for {char_name}: {str(e)}")
        import traceback
        traceback.print_exc() # Print full traceback for exceptions
        return {
            "success": False,
            "message": f"An unexpected error occurred: {str(e)}"
        }

# Modify the get_character_avatar function
def get_character_avatar(char_name):
    """Fetch character's avatar URL from Supabase"""
    try:
        # Check if Supabase is connected
        if not supabase:
            print("Supabase client not initialized")
            return None
            
        result = supabase.table('characters').select('avatar_urls').eq('name', char_name).execute()
        if result.data and len(result.data) > 0:
            avatar_urls = result.data[0].get('avatar_urls', [])
            return avatar_urls[0] if avatar_urls else None
        return None
    except Exception as e:
        print(f"Error fetching character avatar: {str(e)}")
        return None

# Build the app
with gr.Blocks(title="Drama API Tester") as app:
    gr.Markdown("# Drama API Tester")
    
    # Add demo mode toggle in a more visible location
    with gr.Row():
        with gr.Column(scale=4):
            gr.Markdown("### Demo Mode")
            gr.Markdown("Enable demo mode to use hardcoded responses instead of calling the LLM API")
        with gr.Column(scale=1):
            demo_mode = gr.Checkbox(
                label="Enable Demo Mode",
                value=True,  # Default to True since backend might not be running
                info="When enabled, uses hardcoded responses instead of calling the LLM API"
            )
    
    gr.Markdown("---")  # Add a separator
    
    # Create tabs
    with gr.Tabs():
        # Login Tab
        with gr.Tab("Login"):
            gr.Markdown("## Login")
            
            with gr.Row():
                with gr.Column():
                    email_input = gr.Textbox(
                        label="Email",
                        placeholder="Enter your email",
                        value="<EMAIL>"
                    )
                    password_input = gr.Textbox(
                        label="Password",
                        placeholder="Enter your password",
                        type="password",
                        value="testing"
                    )
                    # Add dummy component for direct event handling
                    login_input = gr.Textbox(visible=False, value="login")
                    login_button = gr.Button("Login", variant="primary")
                    login_status = gr.Textbox(label="Status", value="Ready to login", interactive=False)
                
                with gr.Column():
                    login_result = gr.JSON(label="Login Result")
                    token_display = gr.Textbox(
                        label="JWT Token (first few characters)",
                        interactive=False
                    )
            
            # BASIC TEST FUNCTION
            def debug_button_press():
                print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
                print("PYTHON DEBUG: Login button press detected!")
                print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
                return "Debug: Button press registered in Python."

            def direct_login(email, password, _trigger):
                # Log extensively for debugging
                print(f"==== LOGIN ATTEMPT ====")
                print(f"Email: {email}")
                print(f"Password: {password} (length: {len(password)})")
                print(f"SUPABASE_URL: {SUPABASE_URL}")
                
                try:
                    # Explicitly create a new Supabase client
                    temp_supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
                    print("Created new Supabase client")
                    
                    # Attempt direct auth call
                    response = temp_supabase.auth.sign_in_with_password({
                        "email": email,
                        "password": password
                    })
                    
                    print("Login response received!")
                    jwt_token = response.session.access_token
                    
                    # Store token globally
                    global API_TOKEN
                    API_TOKEN = jwt_token
                    
                    # Debug token info
                    token_data = jwt.decode(jwt_token, options={"verify_signature": False}, algorithms=["HS256"])
                    print(f"Token payload: {json.dumps(token_data, indent=2)}")
                    
                    result = {
                        "success": True,
                        "message": "Login successful! Token obtained.",
                        "token": jwt_token[:20] + "..." if jwt_token else None,
                        "payload": token_data
                    }
                    
                    return result, jwt_token[:20] + "...", "Login successful!"
                except Exception as e:
                    print(f"LOGIN ERROR: {type(e).__name__}: {str(e)}")
                    if hasattr(e, '__dict__'):
                        print(f"Error attributes: {e.__dict__}")
                    
                    return {
                        "success": False,
                        "message": f"Login failed: {str(e)}",
                        "error_type": type(e).__name__
                    }, "", f"Login failed: {str(e)}"
            
            # Connect to the ACTUAL direct_login function
            login_button.click(
                fn=direct_login,
                inputs=[email_input, password_input, login_input],
                outputs=[login_result, token_display, login_status],
                api_name="direct_login_attempt"
            )
            print("Login button click handler connected to direct_login")
        
        # Generate Episode
        with gr.Tab("Episode Generator"):
            gr.Markdown("## Generate Episode")
            
            episode_story = gr.Textbox(
                label="Episode Idea", 
                lines=5,
                placeholder="Enter your episode idea..."
            )
            
            with gr.Row():
                episode_model = gr.Radio(
                    label="Model",
                    choices=["gpt-4o-mini", "gpt-4o"],
                    value="gpt-4o-mini"
                )
                episode_temperature = gr.Slider(
                    label="Temperature",
                    minimum=0.1,
                    maximum=1.0,
                    value=0.7,
                    step=0.1
                )
            
            episode_gen_btn = gr.Button("Generate Episode")
            
            # Store the raw episode generation results for use in other tabs
            episode_result = gr.JSON(label="Result", visible=True)
            
            # Add these components to display a cleaner summary and make it accessible to other tabs
            with gr.Accordion("Generated Content Summary", open=False):
                with gr.Row():
                    with gr.Column():
                        show_title = gr.Textbox(label="Show Title", interactive=False)
                        show_description = gr.Textbox(label="Show Description", lines=3, interactive=False)
                    
                    with gr.Column():
                        show_genre = gr.Textbox(label="Genre", interactive=False)
                        show_category = gr.Textbox(label="Category", interactive=False)
                
                episodes_table = gr.Dataframe(
                    headers=["Episode #", "Title", "Description"],
                    datatype=["number", "str", "str"],
                    label="Generated Episodes",
                    interactive=False
                )
                
                characters_table = gr.Dataframe(
                    headers=["Name", "Gender", "Age", "Description", "Appearance"],
                    datatype=["str", "str", "str", "str", "str"],
                    label="Generated Characters",
                    interactive=False
                )
            
            def process_episode_result(result, demo_mode):
                """Process the raw episode generation result into a more usable format"""
                if result is None:
                    return result, None, None, None, None, [], []
                    
                if isinstance(result, dict) and "error" in result:
                    return result, None, None, None, None, [], []
                
                # Handle both direct response and function_call response formats
                data = result
                if isinstance(result, dict):
                    if "function_call" in result and "arguments" in result["function_call"]:
                        # Extract from function_call format
                        data = result["function_call"]["arguments"]
                        if isinstance(data, str):
                            # Parse the string if it's not already a dict
                            try:
                                data = json.loads(data)
                            except:
                                pass
                
                # Process show info
                show_data = {}
                if isinstance(data, dict):
                    show_data = data.get("show", {})
                    if not isinstance(show_data, dict):
                        show_data = {}
                
                title = show_data.get("title", "")
                desc = show_data.get("description", "")
                genre = show_data.get("genre", "")
                category = show_data.get("category", "")
                
                # Process episodes
                episodes_data = []
                if isinstance(data, dict) and "episodes" in data:
                    eps = data.get("episodes", [])
                    if isinstance(eps, list):
                        for ep in eps:
                            if isinstance(ep, dict):
                                episodes_data.append([
                                    ep.get("number", 0),
                                    ep.get("title", ""),
                                    ep.get("description", "")
                                ])
                
                # Process characters
                characters_data = []
                if isinstance(data, dict) and "characters" in data:
                    chars = data.get("characters", [])
                    if isinstance(chars, list):
                        for char in chars:
                            if isinstance(char, dict):
                                characters_data.append([
                                    char.get("name", ""),
                                    char.get("gender", ""),
                                    char.get("age", ""),
                                    char.get("description", ""),
                                    char.get("appearance", "")
                                ])
                
                # Print for debugging
                print(f"Processed episode result: {title}, {len(episodes_data)} episodes, {len(characters_data)} characters")
                
                return result, title, desc, genre, category, episodes_data, characters_data
            
            episode_gen_btn.click(
                fn=lambda story, model, temp, demo: process_episode_result(
                    generate_episode_content(story, model, temp, demo),
                    demo
                ),
                inputs=[episode_story, episode_model, episode_temperature, demo_mode],
                outputs=[
                    episode_result, 
                    show_title, 
                    show_description, 
                    show_genre, 
                    show_category, 
                    episodes_table, 
                    characters_table
                ]
            )
        
        # Character Generator Tab
        with gr.Tab("Character Generator"):
            gr.Markdown("## Character Prompt Generator")
            gr.Markdown("Generate images for characters created in the Episode Generator")
            
            # Add Generated Content Summary accordion to Character Generator tab
            with gr.Accordion("Generated Content Summary", open=True):
                with gr.Row():
                    with gr.Column():
                        char_gen_show_title = gr.Textbox(label="Show Title", interactive=False)
                        char_gen_show_description = gr.Textbox(label="Show Description", lines=3, interactive=False)
                    
                    with gr.Column():
                        char_gen_show_genre = gr.Textbox(label="Genre", interactive=False)
                        char_gen_show_category = gr.Textbox(label="Category", interactive=False)
                
                char_gen_characters_table = gr.Dataframe(
                    headers=["Name", "Gender", "Age", "Description", "Appearance"],
                    datatype=["str", "str", "str", "str", "str"],
                    label="Generated Characters",
                    interactive=False
                )
            
            # Character selector
            character_selector = gr.Dropdown(
                label="Select Character to Visualize",
                choices=[],
                value=None,
                interactive=True
            )
            
            # Character data display and form
            with gr.Row():
                with gr.Column():
                    char_name = gr.Textbox(label="Name", interactive=False)
                    gender_input = gr.Dropdown(
                        label="Gender",
                        choices=["male", "female", "non-binary"],
                        value="female",
                        interactive=True
                    )
                    age_input = gr.Textbox(
                        label="Age",
                        placeholder="e.g., 25, teenage, elderly",
                        value="",
                        interactive=True
                    )
                    appearance_input = gr.Textbox(
                        label="Appearance Description",
                        placeholder="Describe the character's appearance...",
                        lines=3,
                        interactive=True
                    )
                    
                    # Get available styles for the dropdown
                    available_styles = get_available_styles()
                    # Fix: Create choices with proper ID value handling
                    style_choices = {f"{style['name']}: {style['description']}": style["id"] for style in available_styles}
                    
                    style_input = gr.Dropdown(
                        label="Image Style",
                        choices=list(style_choices.keys()),
                        value=list(style_choices.keys())[0] if style_choices else None,
                        interactive=True
                    )
                    
                    additional_input = gr.Textbox(
                        label="Additional Details (optional)",
                        placeholder="Environment, clothing, props, etc...",
                        lines=2,
                        interactive=True
                    )
                    
                    generate_prompt_button = gr.Button("Generate Character Prompt", variant="primary")
                
                with gr.Column():
                    character_summary = gr.Textbox(label="Character Summary", interactive=False)
                    style_name_output = gr.Textbox(label="Style", interactive=False)
                    positive_prompt_output = gr.Textbox(label="Positive Prompt", lines=4, interactive=False)
                    negative_prompt_output = gr.Textbox(label="Negative Prompt", lines=3, interactive=False)
                    
                    # Get available models for image generation
                    model_choices = get_available_models()
                    char_model_input = gr.Dropdown(
                        label="Image Generation Model",
                        choices=model_choices,
                        value=model_choices[0] if model_choices else "fal-ai/fast-sdxl",
                        interactive=True
                    )
                    
                    with gr.Row():
                        width_char_input = gr.Slider(
                            label="Width",
                            minimum=512,
                            maximum=1536,
                            value=1024,
                            step=64,
                            interactive=True
                        )
                        height_char_input = gr.Slider(
                            label="Height",
                            minimum=512,
                            maximum=1536,
                            value=1024,
                            step=64,
                            interactive=True
                        )
                    
                    generate_char_image_button = gr.Button("Generate Character Image", variant="primary")
            
            with gr.Row():
                character_image_output = gr.Image(label="Generated Character")
                character_result_output = gr.JSON(label="Generation Details", visible=False)
            
            # Add save button
            save_image_button = gr.Button("Save Character Image", variant="primary")
            save_result = gr.JSON(label="Save Result", visible=True)
            
            with gr.Row():
                char_request_body_output = gr.Textbox(label="Image Generation Details", lines=3, interactive=False)
            
            # Character gallery to store all character images
            char_gallery = gr.Gallery(
                label="Character Gallery",
                columns=4,
                object_fit="contain",
                height="auto"
            )
            
            # Store character data and images (will be used by storyboard tab)
            character_data_store = gr.State({})
            
            def update_character_selector(characters_data):
                """Update the character selector with names from generated characters"""
                # Handle empty input
                if characters_data is None:
                    return gr.update(choices=[], value=None)
                    
                # Handle pandas DataFrame
                if isinstance(characters_data, pd.DataFrame):
                    if characters_data.empty:
                        return gr.update(choices=[], value=None)
                    # Convert DataFrame to list of lists
                    characters_data = characters_data.values.tolist()
                
                # If it's a list of lists
                if isinstance(characters_data, list) and len(characters_data) > 0 and isinstance(characters_data[0], list):
                    choices = [char[0] for char in characters_data if char[0]]  # Get character names from first column
                # If it's a list of dicts
                elif isinstance(characters_data, list) and len(characters_data) > 0 and isinstance(characters_data[0], dict):
                    choices = [char.get("name", "") for char in characters_data if char.get("name")]
                else:
                    choices = []
                
                return gr.update(choices=choices, value=choices[0] if choices else None)
            
            def fill_character_form(character_name, characters_data):
                """Fill the character form based on selected character"""
                import pandas as pd
                
                if not character_name or characters_data is None:
                    return "", "female", "", "", "", ""
                
                # Handle pandas DataFrame
                if isinstance(characters_data, pd.DataFrame):
                    if characters_data.empty:
                        return "", "female", "", "", "", ""
                    # Convert DataFrame to list of lists
                    characters_data = characters_data.values.tolist()
                
                # Find the character data
                # If it's a list of lists (dataframe format)
                if isinstance(characters_data, list) and len(characters_data) > 0 and isinstance(characters_data[0], list):
                    for char in characters_data:
                        if char[0] == character_name:
                            # Return name, gender, age, appearance (index 4 is appearance)
                            return char[0], char[1] or "female", char[2] or "", char[4] or "", "", ""
                
                # If it's a list of dicts
                elif isinstance(characters_data, list) and len(characters_data) > 0 and isinstance(characters_data[0], dict):
                    for char in characters_data:
                        if char.get("name") == character_name:
                            # Return name, gender, age, appearance
                            return char.get("name", ""), char.get("gender", "female"), char.get("age", ""), char.get("appearance", ""), "", ""
                
                # Default return if no match found
                return "", "female", "", "", "", ""
            
            def generate_character_prompt_fn(name, gender, age, appearance, style_display, additional_details):
                """Generate character prompt based on the inputs"""
                try:
                    # Convert from display value to actual style ID
                    style_id = style_choices[style_display]
                    
                    character = CharacterDetails(
                        gender=gender,
                        age=age,
                        appearance=appearance,
                        style=style_id,
                        additional_details=additional_details if additional_details.strip() else None
                    )
                    
                    result = generate_character_prompt(character)
                    
                    # Include the character name in the summary
                    character_summary = f"{name}: {result.character_summary}" if name else result.character_summary
                    
                    return (
                        character_summary,
                        result.style_name,
                        result.positive_prompt,
                        result.negative_prompt
                    )
                except Exception as e:
                    return (f"Error: {str(e)}", "", "", "")
            
            def generate_character_image(char_name, positive_prompt, negative_prompt, model, width, height, char_data_store):
                """Generate an image for the character and store it"""
                # ADDED: Fetch character_id from char_name
                character_id_to_send = None
                if char_name:
                    try:
                        logger.info(f"Fetching ID for character: {char_name}")
                        # Ensure supabase client is initialized and available
                        char_response = supabase.table("characters").select("id").eq("name", char_name).maybe_single().execute()
                        if char_response.data:
                            character_id_to_send = char_response.data["id"]
                            logger.info(f"Found ID {character_id_to_send} for character {char_name}")
                        else:
                            logger.warning(f"Character '{char_name}' not found in database for ID lookup.")
                            # Optionally, return an error message to display in UI
                            # return None, {"error": f"Character '{char_name}' not found."}, "{}", char_data_store, []
                    except Exception as e:
                        logger.error(f"Error fetching character ID for {char_name}: {str(e)}")
                        print(f"Error fetching character ID for {char_name}: {str(e)}")
                        # Optionally, return an error message
                        # return None, {"error": f"Database error fetching ID for {char_name}: {e}"}, "{}", char_data_store, []
                else:
                    logger.warning("No character name provided to generate_character_image, cannot fetch ID.")
                    print("No character name provided to generate_character_image, cannot fetch ID.")

                # Prepare the request body (This part is mostly illustrative now as generate_image helper does it)
                request_body_display = { # For display/logging only if needed, actual body built in generate_image
                    "prompt": positive_prompt,
                    "model_id": model,
                    "negative_prompt": negative_prompt,
                    "width": int(width),
                    "height": int(height),
                    "num_inference_steps": 30,
                    "character_id": character_id_to_send # Display purposes
                }
                
                # Only add guidance_scale for models that need it
                guidance_models = [
                    "fal-ai/fast-sdxl",
                    "fal-ai/magnetique-v3.5",
                    "fal-ai/flux/dev"
                ]
                # This guidance_scale logic will be handled inside generate_image if passed correctly
                
                # Call the API via the modified generate_image helper
                result = generate_image( # MODIFIED: Call to helper
                    prompt=positive_prompt,
                    model_id=model,
                    negative_prompt=negative_prompt,
                    width=int(width),
                    height=int(height),
                    steps=30, # num_inference_steps
                    seed=None, # seed
                    guidance_scale=7.5 if model in guidance_models else None, # guidance_scale
                    character_id=character_id_to_send # MODIFIED: Pass character_id
                )
                
                # Format the request body for display
                formatted_request = json.dumps(request_body_display, indent=2)
                
                # Update the character data store with generated image
                new_char_data = char_data_store.copy()
                image_url = None
                
                # Check if generation was successful
                if result.get("success") and result.get("image_url"):
                    image_url = result["image_url"]
                    
                    # Add or update the character in the store
                    if char_name not in new_char_data:
                        new_char_data[char_name] = {
                            "image_url": image_url,
                            "prompt": positive_prompt,
                            "negative_prompt": negative_prompt
                        }
                    else:
                        new_char_data[char_name]["image_url"] = image_url
                        new_char_data[char_name]["prompt"] = positive_prompt
                        new_char_data[char_name]["negative_prompt"] = negative_prompt
                    
                    # Debug output for character store
                    print(f"Updated character_data_store with: {char_name}")
                    print(f"Character store now contains {len(new_char_data)} characters: {list(new_char_data.keys())}")
                    
                    # Update the gallery with all character images
                    gallery_images = [info["image_url"] for name, info in new_char_data.items() if "image_url" in info]
                    
                    return image_url, result, formatted_request, new_char_data, gallery_images
                else:
                    return None, result, formatted_request, char_data_store, list(char_data_store.values())
            
            # Connect Episode Generator to Character Generator
            episodes_table.change(
                fn=update_character_selector,
                inputs=[characters_table],
                outputs=[character_selector]
            )
            
            # Also update character selector when the characters table in Character Generator changes
            char_gen_characters_table.change(
                fn=lambda char_data: gr.update(
                    choices=[char[0] for char in char_data.values.tolist() if char[0]] if not char_data.empty else [],
                    value=[char[0] for char in char_data.values.tolist() if char[0]][0] if not char_data.empty and len(char_data.values.tolist()) > 0 else None
                ),
                inputs=[char_gen_characters_table],
                outputs=[character_selector]
            )
            
            # Direct connection to update Character Generator tab from episode_result
            def update_char_gen_from_episode(result):
                """Update Character Generator tab from episode result"""
                if result is None or (isinstance(result, dict) and "error" in result):
                    return "", "", "", "", []
                
                # Handle both direct response and function_call response formats
                data = result
                if isinstance(result, dict):
                    if "function_call" in result and "arguments" in result["function_call"]:
                        # Extract from function_call format
                        data = result["function_call"]["arguments"]
                        if isinstance(data, str):
                            # Parse the string if it's not already a dict
                            try:
                                data = json.loads(data)
                            except:
                                pass
                
                # Process show info
                show_data = {}
                if isinstance(data, dict):
                    show_data = data.get("show", {})
                    if not isinstance(show_data, dict):
                        show_data = {}
                
                title = show_data.get("title", "")
                desc = show_data.get("description", "")
                genre = show_data.get("genre", "")
                category = show_data.get("category", "")
                
                # Process characters
                characters_data = []
                if isinstance(data, dict) and "characters" in data:
                    chars = data.get("characters", [])
                    if isinstance(chars, list):
                        for char in chars:
                            if isinstance(char, dict):
                                characters_data.append([
                                    char.get("name", ""),
                                    char.get("gender", ""),
                                    char.get("age", ""),
                                    char.get("description", ""),
                                    char.get("appearance", "")
                                ])
                
                return title, desc, genre, category, characters_data
            
            # Connect episode_result to Character Generator tab
            episode_result.change(
                fn=update_char_gen_from_episode,
                inputs=[episode_result],
                outputs=[
                    char_gen_show_title,
                    char_gen_show_description,
                    char_gen_show_genre,
                    char_gen_show_category,
                    char_gen_characters_table
                ]
            )
            
            # Direct connection between the two character tables to ensure they stay in sync
            characters_table.change(
                fn=lambda data: data,
                inputs=[characters_table],
                outputs=[char_gen_characters_table]
            )
            
            # Update character form when character is selected
            character_selector.change(
                fn=fill_character_form,
                inputs=[character_selector, characters_table],
                outputs=[char_name, gender_input, age_input, appearance_input, additional_input, positive_prompt_output]
            )
            
            # Wire up the character generator button
            generate_prompt_button.click(
                fn=generate_character_prompt_fn,
                inputs=[
                    char_name,
                    gender_input,
                    age_input,
                    appearance_input,
                    style_input,
                    additional_input
                ],
                outputs=[
                    character_summary,
                    style_name_output,
                    positive_prompt_output,
                    negative_prompt_output
                ]
            )
            
            # Wire up the "Generate Character Image" button
            generate_char_image_button.click(
                fn=generate_character_image,
                inputs=[
                    char_name,
                    positive_prompt_output,
                    negative_prompt_output,
                    char_model_input,
                    width_char_input,
                    height_char_input,
                    character_data_store
                ],
                outputs=[
                    character_image_output,
                    character_result_output,
                    char_request_body_output,
                    character_data_store,
                    char_gallery
                ]
            )
            
            # Add save button click handler
            def save_character_image_click(char_name, image_data):
                print(f"---- save_character_image_click ----")
                print(f"char_name: '{char_name}'")
                print(f"image_data type: {type(image_data)}")
                
                # Don't try to save if character data is missing
                if not char_name:
                    return {"success": False, "message": "Character name is missing"}
                
                # If image_data is a numpy array, this is from the image generated in Gradio
                # We need to get the URL from character_data_store instead
                temp_char_data = character_data_store.value
                print(f"Character data store contents: {json.dumps(temp_char_data, indent=2)}")
                
                if char_name in temp_char_data and "image_url" in temp_char_data[char_name]:
                    actual_image_url = temp_char_data[char_name]["image_url"]
                    print(f"Found image URL in character store: {actual_image_url}")
                    
                    # Check if the characters bucket exists
                    try:
                        buckets = supabase.storage.list_buckets()
                        bucket_names = [bucket['name'] for bucket in buckets]
                        if 'characters' not in bucket_names:
                            print("WARNING: 'characters' bucket doesn't exist, creating it now")
                            supabase.storage.create_bucket('characters', {'public': False})
                    except Exception as e:
                        print(f"Error checking/creating storage bucket: {str(e)}")
                        return {"success": False, "message": f"Supabase storage error: {str(e)}"}
                    
                    # Try to list files in bucket
                    try:
                        files = supabase.storage.from_('characters').list()
                        print(f"Files in 'characters' bucket BEFORE save: {files}")
                    except Exception as e:
                        print(f"Could not list files in 'characters' bucket: {str(e)}")
                    
                    # Now we have a URL to save
                    print(f"Proceeding to save: char_name='{char_name}', actual_image_url='{actual_image_url}'")
                    save_result = save_character_image(char_name, actual_image_url)
                    
                    # Check if save was successful
                    if save_result.get("success", False):
                        # Try to list files in bucket after save
                        try:
                            files = supabase.storage.from_('characters').list()
                            print(f"Files in 'characters' bucket AFTER save: {files}")
                        except Exception as e:
                            print(f"Could not list files in 'characters' bucket after save: {str(e)}")
                        
                        # Query the characters table to verify the avatar_urls was updated
                        try:
                            query_result = supabase.table('characters').select('name,avatar_urls').eq('name', char_name).execute()
                            print(f"Character record after save: {query_result.data}")
                        except Exception as e:
                            print(f"Could not query characters table: {str(e)}")
                    
                    return save_result
                else:
                    # Also try querying character_images table
                    try:
                        query_result = supabase.table('character_images').select('*').limit(5).execute()
                        print(f"Recent entries in character_images table: {query_result.data}")
                    except Exception as e:
                        print(f"Could not query character_images table: {str(e)}")
                    
                    return {"success": False, "message": f"No image URL found for character {char_name} in character store"}
                
                # Legacy code for URL extraction, kept as fallback
                actual_image_url = None
                if isinstance(image_data, list) and len(image_data) > 0:
                    actual_image_url = image_data[0]
                elif isinstance(image_data, str):
                    actual_image_url = image_data
                elif isinstance(image_data, dict) and 'path' in image_data:
                    actual_image_url = image_data['path']
                elif hasattr(image_data, 'name'):
                    actual_image_url = getattr(image_data, 'name')
                
                if actual_image_url and isinstance(actual_image_url, str):
                    print(f"Using fallback URL: {actual_image_url}")
                    return save_character_image(char_name, actual_image_url)
                else:
                    return {"success": False, "message": "No valid image URL found"}
            
            # Function to refresh character gallery and display
            def refresh_character_gallery(char_name):
                """Refresh the character gallery by fetching latest data from Supabase"""
                try:
                    # Query all characters with avatar_urls
                    query_result = supabase.table('characters').select('name,avatar_urls').execute()
                    print(f"Character refresh query result: {query_result.data}")
                    
                    # Extract images for gallery
                    gallery_images = []
                    character_data = {}
                    
                    for char in query_result.data:
                        if char.get('avatar_urls') and len(char['avatar_urls']) > 0:
                            avatar_url = char['avatar_urls'][0]
                            gallery_images.append(avatar_url)
                            character_data[char['name']] = {
                                "image_url": avatar_url
                            }
                    
                    return gallery_images, character_data
                except Exception as e:
                    print(f"Error refreshing gallery: {str(e)}")
                    return [], {}
            
            # Connect save button with refresh functionality
            def save_and_refresh(char_name, image_data):
                """Save character image and refresh gallery"""
                # First save the image
                save_result_data = save_character_image_click(char_name, image_data)
                
                # Then refresh the gallery
                gallery_images, updated_char_data = refresh_character_gallery(char_name)
                
                # Update character data store with refreshed data
                new_char_data = character_data_store.value.copy()
                for name, data in updated_char_data.items():
                    if name in new_char_data:
                        new_char_data[name].update(data)
                    else:
                        new_char_data[name] = data
                
                return save_result_data, gallery_images, new_char_data
            
            save_image_button.click(
                fn=save_and_refresh,
                inputs=[char_name, character_image_output],
                outputs=[save_result, char_gallery, character_data_store]
            )
            
        # Generate Storyboard
        with gr.Tab("Storyboard Generator"):
            gr.Markdown("## Generate Storyboard")
            
            with gr.Row():
                with gr.Column(scale=1):
                    gr.Markdown("### Select Episode and Characters")
                    
                    episode_dropdown = gr.Dropdown(
                        label="Select Episode",
                        choices=[],
                        value=None
                    )
                    
                    character_checkboxes = gr.CheckboxGroup(
                        label="Select Characters",
                        choices=[],
                        value=[]
                    )
                    
                    storyboard_model = gr.Radio(
                        label="Model",
                        choices=["gpt-4o-mini", "gpt-4o"],
                        value="gpt-4o"
                    )
                    
                    storyboard_temperature = gr.Slider(
                        label="Temperature",
                        minimum=0.1,
                        maximum=1.0,
                        value=0.7,
                        step=0.1
                    )
                    
                    storyboard_generate_btn = gr.Button("Generate Storyboard", variant="primary")
                
                with gr.Column(scale=2):
                    storyboard_status = gr.Markdown("Generate a storyboard using the episode and characters")
            
            # Storyboard results
            storyboard_result = gr.JSON(label="Generated Storyboard", visible=False)
            
            # Store video rendering states
            video_states = gr.State({})
            
            # Display storyboard shots in a tabular format
            shots_container = gr.HTML(
                label="Storyboard Shots", 
                visible=True,
                value="<div style='padding:20px; color:#666; text-align:center;'>Storyboard shots will appear here after generation</div>",
                elem_id="storyboard_shots_container"
            )
            
            # Final product video section
            with gr.Row():
                final_video_button = gr.Button("Generate Final Product Video", variant="primary")
                final_video_output = gr.Video(label="Final Product Video", visible=False)
            
            def render_shot_video(shot_id, shot_type, video_states):
                """Simulate video rendering for a shot"""
                # Update state to rendering
                new_states = video_states.copy()
                new_states[shot_id] = "rendering"
                
                # Simulate rendering delay
                time.sleep(2)
                
                # Get video URL based on shot type
                video_url = SHOT_VIDEOS.get(shot_type, DEFAULT_SHOT_VIDEO)
                
                # Update state to complete
                new_states[shot_id] = "complete"
                
                return video_url, new_states
            
            def generate_final_video(video_states):
                """Generate the final product video when all shots are rendered"""
                # Check if all videos are rendered
                if all(state == "complete" for state in video_states.values()):
                    return FINAL_PRODUCT_VIDEO, gr.update(visible=True)
                return None, gr.update(visible=False)
            
            def generate_storyboard_shots(storyboard_data, model, temperature, demo_mode):
                """Generate storyboard shots using the API"""
                if not storyboard_data:
                    print("No storyboard data provided")
                    error_html = '<div style="color:red; padding:20px;">Error: No storyboard data provided. Please generate an episode first and select characters.</div>'
                    return None, gr.update(value=error_html, visible=True), {}
                
                try:
                    print(f"Generating storyboard with model {model} at temperature {temperature}")
                    print(f"Storyline length: {len(storyboard_data['storyline'])} characters")
                    print(f"Characters description length: {len(storyboard_data['characters'])} characters")
                    
                    # Call the storyboard API
                    result = generate_show_storyboard(
                        storyline=storyboard_data["storyline"],
                        characters=storyboard_data["characters"],
                        model=model,
                        temperature=temperature,
                        demo_mode=demo_mode
                    )
                    
                    print(f"Storyboard generation result received: {type(result)}")
                    print(f"Result: {json.dumps(result, indent=2)[:500]}...")  # Print part of the result for debugging
                    
                    # Print detailed information about the storyboard data
                    if isinstance(result, dict) and "shots" in result:
                        shots = result.get("shots", [])
                        print(f"\n\n{'='*40}\nSTORYBOARD STRUCTURE ANALYSIS\n{'='*40}")
                        print(f"Total shots: {len(shots)}")
                        
                        if shots:
                            # Analyze first shot
                            first_shot = shots[0]
                            print(f"\nFirst shot structure:")
                            for key, value in first_shot.items():
                                if isinstance(value, dict):
                                    print(f"  {key} (object): {list(value.keys())}")
                                elif isinstance(value, list):
                                    print(f"  {key} (array): {len(value)} items")
                                else:
                                    print(f"  {key}: {value}")
                        
                            # Check for required fields
                            required_fields = ["shot_number", "shot_name", "shot_type", "shot_setting", 
                                            "shot_framing", "shot_angle", "shot_movement"]
                            
                            missing_fields = [field for field in required_fields if field not in first_shot]
                            if missing_fields:
                                print(f"\nWARNING: Missing required fields in response: {missing_fields}")
                        
                        print(f"{'='*40}\n")
                    
                    # Generate HTML for shots display
                    html = '<div style="width:100%;">'
                    
                    # Check for success
                    if isinstance(result, dict) and "shots" in result and result["shots"]:
                        for i, shot in enumerate(result["shots"]):
                            shot_id = f"shot_{i}"
                            shot_type = shot.get("shot_type", "")
                            
                            # Get image URL based on shot type
                            image_url = SHOT_IMAGES.get(shot_type, DEFAULT_SHOT_IMAGE)
                            
                            # Create shot card with standard fields
                            html += f'''
                            <div style="border:1px solid #ddd; margin:10px 0; padding:15px; border-radius:5px; background:#f9f9f9;">
                                <h3>Shot {shot.get("shot_number", i+1)}: {shot.get("shot_name", "")}</h3>
                                <div style="display:flex; flex-wrap:wrap;">
                                    <div style="flex:2; min-width:300px;">
                                        <p><strong>Type:</strong> {shot_type}</p>
                                        <p><strong>Setting:</strong> {shot.get("shot_setting", "")}</p>
                                        <p><strong>Camera:</strong> 
                                            {shot.get("shot_framing", "")} / 
                                            {shot.get("shot_angle", "")} / 
                                            {shot.get("shot_movement", "")}
                                        </p>
                                        
                                        <!-- Fallback for older format or different response structure -->
                                        {
                                            f'<p><strong>Description:</strong> {shot.get("description", "")}</p>' if "description" in shot else ""
                                        }
                                        
                                        {
                                            f'<p><strong>Action:</strong> {shot.get("action", "")}</p>' if "action" in shot else ""
                                        }
                                        
                                        <div style="margin-top:10px;">
                                            <p><strong>Characters:</strong></p>
                                            {
                                                # Check if composition exists and is a dict
                                                f'''
                                                <ul>
                                                {
                                                    ''.join([f'<li><strong>{char_name}:</strong> {desc}</li>' 
                                                            for char_name, desc in shot.get("character_composition", {}).items()])
                                                }
                                                </ul>
                                                ''' if isinstance(shot.get("character_composition"), dict) else f'<p>{character_name if character_name else "None"}</p>'
                                            }
                                        </div>
                                        
                                        {
                                            f"""
                                            <div style="margin-top:10px; background:#efefef; padding:10px; border-radius:5px;">
                                                <p><strong>Dialogue:</strong></p>
                                                <p><strong>{shot.get("character_dialogue", {}).get("character_name", "")}:</strong> 
                                                <em>({shot.get("character_dialogue", {}).get("emotion", "")})</em></p>
                                                <p>"{shot.get("character_dialogue", {}).get("line", "")}"</p>
                                            </div>
                                            """ if shot.get("shot_type") == "setting_with_actors_dialogue" and shot.get("character_dialogue") else ""
                                        }
                                        
                                        {
                                            f'<p><strong>Image Prompt:</strong> {shot.get("image_prompt", "")}</p>' if "image_prompt" in shot else ""
                                        }
                                    </div>
                                    <div style="flex:1; min-width:150px; text-align:center;">
                                        <img src="{image_url}" style="max-height:150px; max-width:150px;" />
                                        <div style="margin-top:10px;">
                                            <button onclick="renderVideo('{shot_id}')" id="render_btn_{shot_id}">Render Video</button>
                                            <div id="video_container_{shot_id}" style="display:none; margin-top:10px;">
                                                <video controls style="max-width:150px;">
                                                    <source src="" type="video/mp4">
                                                </video>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            '''
                        
                        html += '</div>'
                        print("HTML generated successfully. Length:", len(html))
                        return result, gr.update(value=html, visible=True), {shot_id: "pending" for shot_id in [f"shot_{i}" for i in range(len(result["shots"]))]}
                    elif isinstance(result, dict) and "error" in result:
                        error_message = result.get("error", "Unknown error")
                        html = f'<div style="color:red; padding:20px;">API Error: {error_message}</div>'
                        print(f"Error in API response: {error_message}")
                        return result, gr.update(value=html, visible=True), {}
                    else:
                        # Generic error for unexpected response format
                        html = '<div style="color:red; padding:20px;">Error: Unexpected response format from API. Check console for details.</div>'
                        print("Unexpected result format. Result:", result)
                        return result, gr.update(value=html, visible=True), {}
                    
                except Exception as e:
                    import traceback
                    traceback.print_exc()
                    error_html = f'<div style="color:red; padding:20px;">Error: {str(e)}</div>'
                    print(f"Exception in generate_storyboard_shots: {str(e)}")
                    return None, gr.update(value=error_html, visible=True), {}
            
            # Connect the video rendering button
            def render_video_click(shot_id, shot_type, video_states):
                video_url, new_states = render_shot_video(shot_id, shot_type, video_states)
                return video_url, new_states
            
            # Connect the final video button
            final_video_button.click(
                fn=generate_final_video,
                inputs=[video_states],
                outputs=[final_video_output, final_video_output]
            )
            
            # Connect Episode Generator data to Storyboard tab
            episodes_table.change(
                fn=lambda episodes_data: gr.update(
                    choices=[f"Episode {row[0]}: {row[1]}" for row in episodes_data.values.tolist() if isinstance(row, list) and len(row) > 1 and row[1]] 
                    if isinstance(episodes_data, pd.DataFrame) 
                    else [f"Episode {ep[0]}: {ep[1]}" for ep in episodes_data if isinstance(ep, list) and len(ep) > 1 and ep[1]]
                ),
                inputs=[episodes_table],
                outputs=[episode_dropdown]
            )
            
            # Add character avatar display
            character_avatar = gr.Image(label="Character Avatar", visible=False)
            
            def update_character_selection(char_name):
                """Update UI when character is selected"""
                if not char_name:
                    return gr.update(visible=False)
                
                # Get the avatar URL
                avatar_url = get_character_avatar(char_name)
                if avatar_url:
                    return gr.update(value=avatar_url, visible=True)
                return gr.update(visible=False)
            
            # Update character selection to also show avatar
            character_checkboxes.change(
                fn=lambda chars: (
                    gr.update(choices=chars),  # Keep current choices
                    update_character_selection(chars[0] if chars and len(chars) > 0 else None)
                ),
                inputs=[character_checkboxes],
                outputs=[character_checkboxes, character_avatar]
            )
            
            # Also update avatar when episode changes (in case characters change)
            episode_dropdown.change(
                fn=lambda episode_selected, chars: (
                    gr.update(), # Keep current episode selection, don't modify dropdown
                    update_character_selection(chars[0] if chars else None)
                ),
                inputs=[episode_dropdown, character_checkboxes],
                outputs=[episode_dropdown, character_avatar]
            )
            
            # Prepare storyboard generation when selections change
            def update_storyboard_status(episode, characters, episodes_data, characters_data, char_data_store):
                status, _ = prepare_storyboard_data(episode, characters, episodes_data, characters_data, char_data_store)
                return status
            
            episode_dropdown.change(
                fn=update_storyboard_status,
                inputs=[episode_dropdown, character_checkboxes, episodes_table, characters_table, character_data_store],
                outputs=[storyboard_status]
            )
            
            character_checkboxes.change(
                fn=update_storyboard_status,
                inputs=[episode_dropdown, character_checkboxes, episodes_table, characters_table, character_data_store],
                outputs=[storyboard_status]
            )
            
            # Define the missing function prepare_storyboard_data
            def prepare_storyboard_data(episode, characters, episodes_data, characters_data, char_data_store):
                """Prepare data for storyboard generation from selected episode and characters"""
                if not episode or not characters or episodes_data is None or characters_data is None:
                    return "Please select an episode and at least one character", None
                
                print(f"Preparing storyboard data for episode: {episode}")
                print(f"Selected characters: {characters}")
                
                # Extract episode number and description
                episode_num = None
                episode_desc = None
                episode_title = None
                
                # Handle the case where episode is a string from dropdown like "Episode 1: The Beginning"
                if isinstance(episode, str) and ":" in episode:
                    parts = episode.split(":")
                    ep_num_str = parts[0].strip()
                    if "Episode" in ep_num_str:
                        episode_num = ep_num_str.replace("Episode", "").strip()
                    episode_title = parts[1].strip() if len(parts) > 1 else ""
                
                # Find the episode in episodes_data (format could be DataFrame or list)
                if isinstance(episodes_data, pd.DataFrame):
                    # Handle DataFrame format
                    for _, row in episodes_data.iterrows():
                        if row[0] == episode_num or row[1] == episode_title:
                            episode_num = row[0]
                            episode_title = row[1]
                            episode_desc = row[2]
                            break
                else:
                    # Handle list format
                    for ep in episodes_data:
                        if isinstance(ep, list) and (str(ep[0]) == str(episode_num) or ep[1] == episode_title):
                            episode_num = ep[0]
                            episode_title = ep[1]
                            episode_desc = ep[2] if len(ep) > 2 else ""
                            break
                
                if not episode_desc:
                    return f"Could not find description for episode {episode}", None
                
                # Extract character details
                char_details = []
                if isinstance(characters_data, pd.DataFrame):
                    # Handle DataFrame format
                    for _, row in characters_data.iterrows():
                        if row[0] in characters:
                            char_details.append({
                                "name": row[0],
                                "gender": row[1],
                                "age": row[2],
                                "description": row[3],
                                "appearance": row[4],
                                "image_url": get_character_avatar(row[0]) or (
                                    char_data_store.get(row[0], {}).get("image_url", "") if char_data_store else ""
                                )
                            })
                else:
                    # Handle list format
                    for char in characters_data:
                        if isinstance(char, list) and char[0] in characters:
                            char_details.append({
                                "name": char[0],
                                "gender": char[1],
                                "age": char[2],
                                "description": char[3],
                                "appearance": char[4],
                                "image_url": get_character_avatar(char[0]) or (
                                    char_data_store.get(char[0], {}).get("image_url", "") if char_data_store else ""
                                )
                            })
                
                if not char_details:
                    return "Could not find details for selected characters", None
                
                # Format the data for the API
                storyboard_data = {
                    "storyline": f"Episode {episode_num}: {episode_title}\n\n{episode_desc}",
                    "characters": json.dumps(char_details)
                }
                
                status_msg = (
                    f"Ready to generate storyboard for episode {episode_num}: {episode_title} "
                    f"with {len(char_details)} characters"
                )
                return status_msg, storyboard_data
            
            # Generate storyboard when button is clicked
            storyboard_generate_btn.click(
                fn=lambda ep, chars, eps, chars_data, char_store, model, temp, demo: 
                    generate_storyboard_shots(
                        prepare_storyboard_data(ep, chars, eps, chars_data, char_store)[1],
                        model,
                        temp,
                        demo
                    ),
                inputs=[
                    episode_dropdown,
                    character_checkboxes,
                    episodes_table,
                    characters_table,
                    character_data_store,
                    storyboard_model,
                    storyboard_temperature,
                    demo_mode
                ],
                outputs=[storyboard_result, shots_container, video_states]
            )
            
            # Function to update character checkboxes based on characters table
            def update_character_checkboxes(characters_data):
                """Update character checkboxes from characters table data"""
                if characters_data is None or (isinstance(characters_data, pd.DataFrame) and characters_data.empty):
                    return gr.update(choices=[], value=[])
                
                if isinstance(characters_data, pd.DataFrame):
                    # Extract character names from DataFrame
                    choices = [row[0] for row in characters_data.values.tolist() if isinstance(row, list) and len(row) > 0 and row[0]]
                elif isinstance(characters_data, list) and len(characters_data) > 0:
                    # Extract character names from list format
                    choices = [char[0] for char in characters_data if isinstance(char, list) and len(char) > 0 and char[0]]
                else:
                    choices = []
                
                return gr.update(choices=choices, value=choices[:2] if len(choices) >= 2 else choices)
            
            # Connect character checkboxes to characters table
            characters_table.change(
                fn=update_character_checkboxes,
                inputs=[characters_table],
                outputs=[character_checkboxes]
            )
            
            # Connect episode_result to character checkboxes
            episode_result.change(
                fn=lambda result: update_character_checkboxes(
                    # Extract characters from episode_result
                    process_episode_result(result, False)[6]
                ),
                inputs=[episode_result],
                outputs=[character_checkboxes]
            )

# Modify the app launch configuration at the bottom of the file
if __name__ == "__main__":
    try:
        # Test Supabase connection
        supabase.table('characters').select('count').limit(1).execute()
        print("Successfully connected to Supabase")
    except Exception as e:
        print(f"Warning: Could not connect to Supabase: {str(e)}")
        print("Running in limited mode - some features may not be available")
    
    # Launch the app with error handling
    print("Starting Gradio app...")
    try:
        app.launch(
            server_name="127.0.0.1", 
            server_port=7860,  # Use a specific port instead of 0
            share=False,  # Disable sharing temporarily
            show_error=True,  # Show detailed error messages
            quiet=False  # Show more console output for debugging
        )
    except Exception as e:
        print(f"Error launching Gradio app: {str(e)}")
        print("Trying alternative port...")
        try:
            app.launch(
                server_name="127.0.0.1", 
                server_port=7861,  # Try a different port
                share=False,
                show_error=True,
                quiet=False
            )
        except Exception as e2:
            print(f"Error launching Gradio app on alternative port: {str(e2)}")
            print("Please check if any other Gradio instances are running") 