#!/usr/bin/env python3
import sys
import os
import json
import requests
from dotenv import load_dotenv
from jwt import encode
import time

# Add the project root to the path if needed
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Load environment variables
load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
SUPABASE_URL = os.getenv("SUPABASE_URL", "http://127.0.0.1:54321")
SUPABASE_KEY = os.getenv("SUPABASE_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0")
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testing"

# Try to import the supabase client, falling back to manual JWT if not available
try:
    from supabase import create_client, Client
    supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
    
    def get_token():
        try:
            print(f"Logging in to Supabase with {TEST_EMAIL}")
            response = supabase.auth.sign_in_with_password({
                "email": TEST_EMAIL, 
                "password": TEST_PASSWORD
            })
            token = response.session.access_token
            print(f"Got token (first 20 chars): {token[:20]}...")
            return token
        except Exception as e:
            print(f"Supabase login failed: {e}")
            return generate_fake_token()
except ImportError:
    print("Supabase client not available, using manual JWT")
    
    def generate_fake_token():
        # Generate a fake JWT token for testing
        payload = {
            "aud": "authenticated",
            "exp": int(time.time()) + 3600,
            "sub": "test-user-id",
            "email": TEST_EMAIL,
            "role": "authenticated"
        }
        return encode(payload, "secret", algorithm="HS256")
    
    def get_token():
        return generate_fake_token()

def call_llm_prompt(template_id, variables, model="gpt-4o-mini", temperature=0.7):
    """Call the /api/v1/llm/prompt endpoint"""
    token = get_token()
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    data = {
        "template_id": template_id,
        "variables": variables,
        "model": model,
        "temperature": temperature,
        "stream": False
    }
    
    url = f"{API_BASE_URL}/api/v1/llm/prompt"
    print(f"Making POST request to {url}")
    print(f"Request data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("Success! Response:")
            print(json.dumps(result, indent=2))
            return result
        else:
            print(f"Error response: {response.text}")
            return {"error": response.text}
    except Exception as e:
        print(f"Request error: {e}")
        return {"error": str(e)}

def call_shows_generate(user_story, model="gpt-4o-mini", temperature=0.7):
    """Call the /api/v1/shows/generate-content endpoint as a reference"""
    token = get_token()
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    data = {
        "user_story": user_story,
        "model": model,
        "temperature": temperature
    }
    
    url = f"{API_BASE_URL}/api/v1/shows/generate-content"
    print(f"Making POST request to {url}")
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("Success! Got valid response from shows endpoint")
            return result
        else:
            print(f"Error response: {response.text}")
            return {"error": response.text}
    except Exception as e:
        print(f"Request error: {e}")
        return {"error": str(e)}

# Add a function to test the shows/storyboard endpoint
def call_shows_storyboard(storyline, characters, model="gpt-4o", temperature=0.7):
    """Call the /api/v1/shows/storyboard endpoint"""
    token = get_token()
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    data = {
        "storyline": storyline,
        "characters": characters,
        "model": model,
        "temperature": temperature
    }
    
    url = f"{API_BASE_URL}/api/v1/shows/storyboard"
    print(f"Making POST request to {url}")
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("Success! Got valid response from shows storyboard endpoint")
            return result
        else:
            print(f"Error response: {response.text}")
            return {"error": response.text}
    except Exception as e:
        print(f"Request error: {e}")
        return {"error": str(e)}

def main():
    """Run test for the LLM prompt endpoint"""
    print("=== Testing LLM Prompt Endpoint ===")
    
    # Test with episode generator template
    user_story = "A drama about a group of friends navigating life in a small town"
    
    # Test the endpoint
    print("\n\n1. Testing /api/v1/llm/prompt with episode_generator:")
    episode_result = call_llm_prompt(
        template_id="episode_generator",
        variables={"user_story": user_story}
    )
    
    # Test storyboard generator template
    storyline = "Friends gather for a reunion after years apart"
    characters = "John, a successful businessman; Sarah, a teacher; Mike, a struggling artist"
    
    print("\n\n2. Testing /api/v1/llm/prompt with storyboard_generator:")
    storyboard_result = call_llm_prompt(
        template_id="storyboard_generator",
        variables={
            "storyline": storyline,
            "characters": characters
        }
    )
    
    # Compare with the working endpoint
    print("\n\n3. Testing /api/v1/shows/generate-content for reference:")
    shows_result = call_shows_generate(user_story)
    
    # Test the new shows/storyboard endpoint
    print("\n\n4. Testing /api/v1/shows/storyboard endpoint:")
    shows_storyboard_result = call_shows_storyboard(storyline, characters)
    
    # Summary
    print("\n\n=== Test Summary ===")
    print(f"Episode Generator Test: {'SUCCESS' if 'content' in episode_result else 'FAILED'}")
    print(f"Storyboard Generator Test: {'SUCCESS' if 'content' in storyboard_result else 'FAILED'}")
    print(f"Shows Generate Test: {'SUCCESS' if 'show' in shows_result else 'FAILED'}")
    print(f"Shows Storyboard Test: {'SUCCESS' if 'shots' in shows_storyboard_result else 'FAILED'}")

if __name__ == "__main__":
    main() 