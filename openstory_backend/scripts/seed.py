import sys
import os
import asyncio
import traceback # Import traceback
from uuid import uuid4
from datetime import datetime
from faker import Faker
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError

# Add project root to path to allow importing app modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.config import get_settings
from app.db.session import Base
from app.models.user import User
from app.models.show import Show
from app.models.episode import Episode
from app.models.character import Character
from app.models.progression import CharacterProgression
from app.models.frame import Frame, FrameComponent
from app.models.session import Session, Message
from app.models.job import Job

# --- Configuration ---
settings = get_settings()
DATABASE_URL = settings.DATABASE_URL
print(f"--- Connecting to DB: {DATABASE_URL} ---") # Print DB URL
engine = create_engine(str(DATABASE_URL))
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

fake = Faker()

# --- Seeding Logic ---
async def seed_data():
    print("Starting database seeding...")
    db = SessionLocal()
    try:
        # Clear existing data (optional, use with caution)
        print("Clearing existing data...")
        for table in reversed(Base.metadata.sorted_tables):
            print(f"  Deleting from {table.name}...")
            db.execute(table.delete())
        db.commit() # Commit deletion separately
        print("Existing data cleared.")

        # Start a new transaction for seeding
        print("Starting seed transaction...")
        # 1. Seed Users
        print("Seeding Users...")
        user1 = User(email="<EMAIL>", first_name="Alice", username="alice")
        user2 = User(email="<EMAIL>", first_name="Bob", username="bob")
        db.add_all([user1, user2])
        db.flush() # Flush to get IDs
        print("  Users flushed.")

        # 2. Seed Characters (linked to users)
        print("Seeding Characters...")
        char1 = Character(creator_user_id=user1.id, name="Sir Reginald", backstory="A brave knight.", age=35)
        char2 = Character(creator_user_id=user2.id, name="Luna", backstory="A mysterious mage.", age=120)
        db.add_all([char1, char2])
        db.flush()
        print("  Characters flushed.")

        # 3. Seed Shows (linked to users)
        print("Seeding Shows...")
        show1 = Show(user_id=user1.id, title="The Dragon's Quest", description="An epic fantasy adventure.")
        show2 = Show(user_id=user1.id, title="Space Cadets", description="A sci-fi comedy.")
        db.add_all([show1, show2])
        db.flush()
        print("  Shows flushed.")

        # 4. Seed Episodes (linked to shows)
        print("Seeding Episodes...")
        ep1_show1 = Episode(show_id=show1.id, title="The First Trial", synopsis="Reginald faces his first challenge.")
        ep2_show1 = Episode(show_id=show1.id, title="The Shadow Cave", synopsis="A dark secret is revealed.")
        ep1_show2 = Episode(show_id=show2.id, title="Pilot Error", synopsis="The cadets crash land.")
        db.add_all([ep1_show1, ep2_show1, ep1_show2])
        db.flush()
        print("  Episodes flushed.")

        # 5. Seed Character Progressions (linked to characters and episodes)
        print("Seeding Character Progressions...")
        prog1 = CharacterProgression(character_id=char1.id, episode_id=ep1_show1.id, cumulative_traits={"bravery": 5})
        prog2 = CharacterProgression(character_id=char1.id, episode_id=ep2_show1.id, cumulative_traits={"bravery": 6, "wisdom": 2})
        prog3 = CharacterProgression(character_id=char2.id, episode_id=ep1_show1.id, cumulative_traits={"magic": 7})
        db.add_all([prog1, prog2, prog3])
        db.flush()
        print("  Character Progressions flushed.")

        # 6. Seed Frames (linked to episodes)
        print("Seeding Frames...")
        frame1_ep1 = Frame(episode_id=ep1_show1.id, frame_number=1, image_url="http://example.com/img1.jpg")
        frame2_ep1 = Frame(episode_id=ep1_show1.id, frame_number=2, video_url="http://example.com/vid1.mp4", visual_type="video", duration_seconds=10)
        frame1_ep2 = Frame(episode_id=ep2_show1.id, frame_number=1, image_url="http://example.com/img2.jpg")
        db.add_all([frame1_ep1, frame2_ep1, frame1_ep2])
        db.flush()
        print("  Frames flushed.")

        # 7. Seed Frame Components (linked to frames)
        print("Seeding Frame Components...")
        comp1_f1 = FrameComponent(frame_id=frame1_ep1.id, component_type="dialogue", content="Halt! Who goes there?")
        comp2_f1 = FrameComponent(frame_id=frame1_ep1.id, component_type="sound_effect", media_url="http://example.com/sound1.mp3")
        comp1_f2 = FrameComponent(frame_id=frame2_ep1.id, component_type="dialogue", content="The knight drew his sword.")
        db.add_all([comp1_f1, comp2_f1, comp1_f2])
        db.flush()
        print("  Frame Components flushed.")

        # 8. Seed Jobs (optional link to frames)
        print("Seeding Jobs...")
        job1 = Job(frame_id=frame1_ep1.id, job_type="image_gen", status="completed", result={"url": frame1_ep1.image_url})
        job2 = Job(job_type="script_gen", status="pending", payload={"prompt": "Write a scene"})
        db.add_all([job1, job2])
        db.flush()
        print("  Jobs flushed.")

        # 9. Seed Sessions (linked to user, progression, optional character)
        print("Seeding Sessions...")
        session1 = Session(user_id=user1.id, character_progression_id=prog1.id, impersonated_character_id=char1.id)
        session2 = Session(user_id=user2.id, character_progression_id=prog3.id)
        db.add_all([session1, session2])
        db.flush()
        print("  Sessions flushed.")

        # 10. Seed Messages (linked to sessions)
        print("Seeding Messages...")
        msg1_s1 = Message(session_id=session1.id, sender="user", content="What should I do?")
        msg2_s1 = Message(session_id=session1.id, sender="character", content="Follow the path ahead.")
        msg1_s2 = Message(session_id=session2.id, sender="user", content="Tell me a secret.")
        db.add_all([msg1_s1, msg2_s1, msg1_s2])
        db.flush()
        print("  Messages flushed.")

        print("Attempting final commit...") # Added before commit
        db.commit()
        print("Database seeding completed successfully!")

    except IntegrityError as e:
        db.rollback()
        print(f"\n!!! IntegrityError during seeding: {e} !!!")
        print("!!! Transaction rolled back. Check if schema matches models. Run migrations? !!!\n")
    except Exception as e:
        db.rollback()
        print(f"\n!!! An unexpected error occurred: {e} !!!")
        print("!!! Transaction rolled back. !!!\n")
        traceback.print_exc() # Print full traceback
    finally:
        db.close()
        print("Database connection closed.")

if __name__ == "__main__":
    # Check if Faker is installed
    try:
        import faker
    except ImportError:
        print("Error: 'Faker' library not found. Please install it:")
        print("pip install Faker")
        sys.exit(1)

    asyncio.run(seed_data()) 