// Function to handle video rendering
function renderVideo(shotId) {
    const button = document.getElementById(`render_btn_${shotId}`);
    const videoContainer = document.getElementById(`video_container_${shotId}`);
    const videoElement = videoContainer.querySelector('video source');
    
    // Disable button and show loading state
    button.disabled = true;
    button.textContent = 'Rendering...';
    
    // Call the Python function to render the video
    gradio.call('render_video_click', [shotId, shotType, videoStates])
        .then(([videoUrl, newStates]) => {
            // Update video source
            videoElement.src = videoUrl;
            videoElement.parentElement.load();
            
            // Show video container
            videoContainer.style.display = 'block';
            
            // Update button state
            button.textContent = 'Video Ready';
            button.disabled = true;
            
            // Update video states
            videoStates = newStates;
            
            // Check if all videos are rendered
            if (Object.values(videoStates).every(state => state === 'complete')) {
                document.getElementById('final_video_button').disabled = false;
            }
        })
        .catch(error => {
            console.error('Error rendering video:', error);
            button.textContent = 'Render Failed';
            button.disabled = false;
        });
}

// Function to handle final video generation
function generateFinalVideo() {
    const button = document.getElementById('final_video_button');
    const videoContainer = document.getElementById('final_video_container');
    
    // Disable button and show loading state
    button.disabled = true;
    button.textContent = 'Generating...';
    
    // Call the Python function to generate the final video
    gradio.call('generate_final_video', [videoStates])
        .then(([videoUrl, visible]) => {
            if (videoUrl) {
                // Update video source
                const videoElement = videoContainer.querySelector('video source');
                videoElement.src = videoUrl;
                videoElement.parentElement.load();
                
                // Show video container
                videoContainer.style.display = visible ? 'block' : 'none';
                
                // Update button state
                button.textContent = 'Final Video Ready';
            } else {
                button.textContent = 'Not all videos rendered';
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error generating final video:', error);
            button.textContent = 'Generation Failed';
            button.disabled = false;
        });
}

// Add login button click handler
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, looking for login button...');
    const loginButton = document.querySelector('button[data-testid="Login"]');
    if (loginButton) {
        console.log('Login button found, adding click handler');
        loginButton.addEventListener('click', function() {
            console.log('Login button clicked');
        });
    } else {
        console.log('Login button not found');
    }
}); 