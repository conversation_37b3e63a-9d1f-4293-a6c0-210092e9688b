#!/usr/bin/env node
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function seedData() {
  console.log('🌱 Starting database seeding...');

  try {
    // Seed users (if needed)
    const { data: usersExist, error: userCheckError } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    if (userCheckError) throw userCheckError;

    if (!usersExist.length) {
      console.log('Seeding users table...');
      const { error: userError } = await supabase
        .from('users')
        .insert([
          { 
            id: '00000000-0000-0000-0000-000000000001', 
            email: '<EMAIL>', 
            username: 'admin' 
          },
          { 
            id: '00000000-0000-0000-0000-000000000002', 
            email: '<EMAIL>', 
            username: 'demo' 
          }
        ]);
      
      if (userError) throw userError;
      console.log('✅ Users seeded successfully!');
    } else {
      console.log('Users table already has data, skipping...');
    }

    // Seed categories
    const { data: categoriesExist, error: categoryCheckError } = await supabase
      .from('categories')
      .select('id')
      .limit(1);
    
    if (categoryCheckError) throw categoryCheckError;

    if (!categoriesExist.length) {
      console.log('Seeding categories table...');
      const { error: categoryError } = await supabase
        .from('categories')
        .insert([
          { id: '00000000-0000-0000-0000-000000000001', name: 'Action', description: 'Action movies and shows' },
          { id: '00000000-0000-0000-0000-000000000002', name: 'Comedy', description: 'Comedy movies and shows' },
          { id: '00000000-0000-0000-0000-000000000003', name: 'Drama', description: 'Drama movies and shows' }
        ]);
      
      if (categoryError) throw categoryError;
      console.log('✅ Categories seeded successfully!');
    } else {
      console.log('Categories table already has data, skipping...');
    }

    // Add more seeding operations for other tables as needed

    console.log('🎉 Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

// Run the seeding function
seedData(); 