#!/usr/bin/env python
import os
import sys
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Initialize Supabase client
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_KEY")

if not supabase_url or not supabase_key:
    print("Missing SUPABASE_URL or SUPABASE_KEY environment variables")
    sys.exit(1)

supabase: Client = create_client(supabase_url, supabase_key)

def seed_data():
    print("🌱 Starting database seeding...")
    
    try:
        # Seed users (if needed)
        users_exist = supabase.table("users").select("id").limit(1).execute()
        
        if not users_exist.data:
            print("Seeding users table...")
            users_data = [
                {
                    "id": "00000000-0000-0000-0000-000000000001",
                    "email": "<EMAIL>",
                    "username": "admin"
                },
                {
                    "id": "00000000-0000-0000-0000-000000000002",
                    "email": "<EMAIL>",
                    "username": "demo"
                }
            ]
            user_result = supabase.table("users").insert(users_data).execute()
            if hasattr(user_result, 'error') and user_result.error:
                raise Exception(f"Error seeding users: {user_result.error}")
            print("✅ Users seeded successfully!")
        else:
            print("Users table already has data, skipping...")
        
        # Seed categories
        categories_exist = supabase.table("categories").select("id").limit(1).execute()
        
        if not categories_exist.data:
            print("Seeding categories table...")
            categories_data = [
                {"id": "00000000-0000-0000-0000-000000000001", "name": "Action", "description": "Action movies and shows"},
                {"id": "00000000-0000-0000-0000-000000000002", "name": "Comedy", "description": "Comedy movies and shows"},
                {"id": "00000000-0000-0000-0000-000000000003", "name": "Drama", "description": "Drama movies and shows"}
            ]
            category_result = supabase.table("categories").insert(categories_data).execute()
            if hasattr(category_result, 'error') and category_result.error:
                raise Exception(f"Error seeding categories: {category_result.error}")
            print("✅ Categories seeded successfully!")
        else:
            print("Categories table already has data, skipping...")
        
        # Add more seeding operations for other tables as needed
        
        print("🎉 Database seeding completed successfully!")
    except Exception as e:
        print(f"❌ Error seeding database: {e}")
        sys.exit(1)

if __name__ == "__main__":
    seed_data() 