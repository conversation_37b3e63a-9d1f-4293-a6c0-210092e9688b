#!/usr/bin/env python3
"""
Test script for character prompt generation.
Generates a sample character prompt and prints the result.
"""
import sys
import os
import json
from pprint import pprint

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.character_services import CharacterDetails, generate_character_prompt, get_available_styles, get_style_details

def main():
    """Main function to test character prompt generation"""
    print("Available Image Styles:")
    styles = get_available_styles()
    for style in styles:
        print(f"- {style['id']}: {style['name']} - {style['description']}")
    
    print("\nExample Character Prompts:")
    
    test_characters = [
        CharacterDetails(
            gender="female", 
            age="25", 
            appearance="confident with long dark hair and green eyes", 
            style="realistic",
            additional_details="professional attire, office setting"
        ),
        CharacterDetails(
            gender="male", 
            age="elderly", 
            appearance="wise with a long white beard and wrinkled face", 
            style="fantasy"
        ),
        CharacterDetails(
            gender="non-binary", 
            age="teenage", 
            appearance="with short blue hair and a playful expression", 
            style="anime"
        ),
        CharacterDetails(
            gender="male", 
            age="30s", 
            appearance="athletic with a determined expression", 
            style="cyberpunk",
            additional_details="futuristic background, cybernetic implants"
        )
    ]
    
    for i, character in enumerate(test_characters, 1):
        print(f"\nCharacter #{i}:")
        print(f"  Details: {character.gender}, {character.age}, {character.appearance}")
        print(f"  Style: {character.style}")
        
        try:
            result = generate_character_prompt(character)
            
            print("\n  Generated Prompt:")
            print(f"  Character Summary: {result.character_summary}")
            print(f"  Style: {result.style_name}")
            print(f"  Positive Prompt: {result.positive_prompt}")
            print(f"  Negative Prompt: {result.negative_prompt}")
            
            # Show how to use with image generation
            print("\n  For use with image_generation.py:")
            config_example = {
                "prompt": result.positive_prompt,
                "negative_prompt": result.negative_prompt,
                "model_id": "fal-ai/fast-sdxl",
                # Other parameters would go here
            }
            print(json.dumps(config_example, indent=2))
            
        except Exception as e:
            print(f"  Error: {str(e)}")
        
        print("-" * 80)

if __name__ == "__main__":
    main() 