#!/usr/bin/env python
"""
Simple script to test LLM templates without starting the full API server.
Usage: python scripts/test_template.py
"""

import sys
import os
import json
import asyncio
from openai import AsyncOpenAI

# Add project root to path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.prompt_service import get_prompt_service
from app.core.config import get_settings
from app.utils.llm_logger import log_openai_request, log_openai_response

# Get settings to access API key
settings = get_settings()

# Initialize OpenAI client
openai_client = AsyncOpenAI(api_key=settings.openai_api_key)

async def test_template(template_id, variables, model="gpt-4o-mini", temperature=0.7):
    """Test a template with the given variables"""
    print(f"\n===== Testing template: {template_id} =====\n")
    
    # Get the prompt service
    prompt_service = get_prompt_service()
    
    # Render the template
    try:
        print(f"Rendering template with variables: {json.dumps(variables, indent=2)}\n")
        rendered = prompt_service.render_template(
            template_id=template_id,
            variables=variables
        )
        
        # Print rendered template
        print("===== SYSTEM PROMPT =====")
        print(rendered["system_prompt"])
        print("\n===== USER PROMPT =====")
        print(rendered["user_prompt"])
        print("\n===== FUNCTIONS =====")
        if rendered["functions"]:
            print(json.dumps(rendered["functions"], indent=2))
        else:
            print("No functions defined")
            
        # Call OpenAI API
        print("\n===== CALLING OPENAI API =====")
        print(f"Using model: {model}, temperature: {temperature}")
        
        # Prepare messages
        messages = []
        if rendered["system_prompt"]:
            messages.append({"role": "system", "content": rendered["system_prompt"]})
        messages.append({"role": "user", "content": rendered["user_prompt"]})
        
        # Call API
        completion_params = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
        }
        
        # Add functions if present
        if rendered["functions"]:
            completion_params["functions"] = rendered["functions"]
            completion_params["function_call"] = "auto"
        
        # Log the OpenAI request
        request_metadata = log_openai_request(
            model=model,
            messages=messages,
            functions=rendered["functions"] if rendered["functions"] else None,
            temperature=temperature,
            streaming=False,
            user_id="test_script"
        )
        
        # Make API call
        try:
            response = await openai_client.chat.completions.create(**completion_params)
            
            # Log the successful response
            log_openai_response(
                response=response,
                request_metadata=request_metadata
            )
        except Exception as e:
            # Log the error
            log_openai_response(
                response=str(e),
                request_metadata=request_metadata,
                is_error=True
            )
            raise
        
        # Print response
        print("\n===== API RESPONSE =====")
        
        # Check if we got a function call
        if response.choices[0].message.function_call:
            function_name = response.choices[0].message.function_call.name
            function_args = response.choices[0].message.function_call.arguments
            
            print(f"Function called: {function_name}")
            print("Function arguments:")
            parsed_args = json.loads(function_args)
            print(json.dumps(parsed_args, indent=2))
        else:
            # Print regular text response
            print(response.choices[0].message.content)
            
        return True
        
    except Exception as e:
        print(f"ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main entry point"""
    # Define test case for episode_generator
    template_id = "episode_generator"
    variables = {
        "user_story": """
        "Starlight Pioneers" follows a team of diverse scientists who establish humanity's first deep space research station in the year 2150. 
        The station orbits a mysterious exoplanet that exhibits signs of ancient intelligent life. Led by Commander Elena Reyes, 
        a disciplined former astronaut haunted by a failed mission, the team includes Dr. Marcus Chen, a brilliant xenobiologist with 
        unconventional theories, and Security Chief Aiden Wolfe, an ex-military officer hiding a connection to a shadowy organization 
        interested in weaponizing alien technology. As they explore ruins on the planet's surface, they begin experiencing strange 
        phenomena that challenge their understanding of reality. Meanwhile, tensions rise when communication with Earth becomes 
        increasingly delayed, leaving them isolated with their discoveries and growing suspicions of each other.
        """
    }
    
    # You can change the model here if needed
    await test_template(template_id, variables, model="gpt-4o-mini", temperature=0.7)

if __name__ == "__main__":
    asyncio.run(main()) 