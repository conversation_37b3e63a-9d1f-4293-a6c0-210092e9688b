#!/usr/bin/env python3
import sys
import os
import time
import json
import gradio as gr
import requests
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import pandas as pd
from PIL import Image
from io import BytesIO
import concurrent.futures
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import services from the app
from app.services.image_generation import FalImageGenerationService, ImageGenerationConfig

# Load environment variables
load_dotenv()

# =============================================
# MODEL DEFINITIONS - ADD NEW MODELS HERE
# =============================================
@dataclass
class FalModel:
    id: str
    name: str
    description: str
    price_per_image: float  # Approximate price in USD
    width_options: List[int]
    height_options: List[int]
    default_negative_prompt: str
    supports_guidance_scale: bool = True
    supports_lora: bool = False  # Whether the model supports LoRA models

# List of shortlisted FAL.ai models for testing
# You can easily add new models to this list
FAL_MODELS = [
    FalModel(
        id="fast-sdxl",  # Corrected format without fal-ai/ prefix
        name="Fast SDXL",
        description="Fast version of Stable Diffusion XL",
        price_per_image=0.015,
        width_options=[512, 768, 1024, 1280, 1536],
        height_options=[512, 768, 1024, 1280, 1536],
        default_negative_prompt="ugly, blurry, low quality, distorted",
        supports_lora=True,
    ),
    FalModel(
        id="magnetique-v3.5",  # Corrected format without fal-ai/ prefix
        name="Magnetique v3.5",
        description="High resolution SDXL-based model",
        price_per_image=0.02,
        width_options=[512, 768, 1024, 1280, 1536],
        height_options=[512, 768, 1024, 1280, 1536],
        default_negative_prompt="bad anatomy, bad hands, three hands, three legs, bad arms",
        supports_lora=True,
    ),
    FalModel(
        id="hidream-i1-dev",  # Corrected format without fal-ai/ prefix
        name="HiDream Dev",
        description="HiDream model for highly detailed images",
        price_per_image=0.025,
        width_options=[512, 768, 1024, 1280],
        height_options=[512, 768, 1024, 1280],
        default_negative_prompt="ugly, blurry, low quality, distorted, bad anatomy",
        supports_guidance_scale=False,
        supports_lora=False,
    ),
    FalModel(
        id="hidream-i1-fast",  # Corrected format without fal-ai/ prefix
        name="HiDream Fast",
        description="Fast version of HiDream model",
        price_per_image=0.02,
        width_options=[512, 768, 1024],
        height_options=[512, 768, 1024],
        default_negative_prompt="ugly, blurry, low quality, distorted, bad anatomy",
        supports_guidance_scale=False,
        supports_lora=False,
    ),
    FalModel(
        id="flux/dev",  # Corrected format without fal-ai/ prefix
        name="Flux Dev",
        description="Flux developer model for creative image generation",
        price_per_image=0.03,
        width_options=[512, 768, 1024],
        height_options=[512, 768, 1024],
        default_negative_prompt="ugly, blurry, low quality, distorted, bad composition",
        supports_lora=True,
    ),
    FalModel(
        id="fast-lightning-sdxl",  # Corrected ID without fal-ai/ prefix
        name="SDXL Lightning",
        description="2-step SDXL inference with high quality",
        price_per_image=0.01,
        width_options=[512, 768, 1024],
        height_options=[512, 768, 1024],
        default_negative_prompt="ugly, blurry, low quality, distorted",
        supports_lora=False,
    ),
    # Added new models
    FalModel(
        id="recraft/text-to-image",  # Corrected ID format
        name="Recraft v3",
        description="High quality text-to-image for illustration and design",
        price_per_image=0.02,
        width_options=[512, 768, 1024],
        height_options=[512, 768, 1024],
        default_negative_prompt="low quality, blurry, distorted",
        supports_lora=False,
    ),
    FalModel(
        id="flux-lora/stream",  # Corrected ID format
        name="Flux LoRA Stream",
        description="Flux with LoRA support (streaming version)",
        price_per_image=0.03,
        width_options=[512, 768, 1024],
        height_options=[512, 768, 1024],
        default_negative_prompt="low quality, blurry, distorted, bad composition",
        supports_lora=True,
    ),
    FalModel(
        id="flux-lora",  # Corrected ID format
        name="Flux LoRA",
        description="Flux with LoRA support",
        price_per_image=0.03,
        width_options=[512, 768, 1024],
        height_options=[512, 768, 1024],
        default_negative_prompt="low quality, blurry, distorted, bad composition",
        supports_lora=True,
    ),
    FalModel(
        id="ideogram/v2",  # Corrected ID format
        name="Ideogram v2",
        description="Ideogram's text-to-image model",
        price_per_image=0.025,
        width_options=[1024],
        height_options=[1024],
        default_negative_prompt="low quality, blurry, distorted, bad composition",
        supports_lora=False,
    ),
    FalModel(
        id="imagen3",  # Corrected ID format
        name="Imagen 3",
        description="Google's Imagen 3 text-to-image model",
        price_per_image=0.035,
        width_options=[1024, 1536, 2048],
        height_options=[1024, 1536, 2048],
        default_negative_prompt="low quality, blurry, distorted, bad composition",
        supports_lora=False,
    ),
    FalModel(
        id="sana",  # Corrected ID format
        name="Sana",
        description="Sana text-to-image model",
        price_per_image=0.02,
        width_options=[1024],
        height_options=[1024],
        default_negative_prompt="low quality, blurry, distorted, bad composition",
        supports_lora=False,
    ),
]

# Initialize image generation service
def get_image_service():
    """Initialize the FAL image generation service with API key from environment variables"""
    fal_key = os.getenv("FAL_KEY")
    if not fal_key:
        print("WARNING: FAL_KEY environment variable not set")
        return None
    
    return FalImageGenerationService(api_key=fal_key, max_concurrent=3)

# Function to prepare LoRA parameters
def prepare_lora_params(
    lora_file: Optional[str], 
    lora_scale: float
) -> Optional[Dict[str, Any]]:
    """Prepare LoRA parameters for models that support it"""
    if not lora_file:
        return None
    
    # Check if the file exists
    if not os.path.exists(lora_file):
        print(f"LoRA file not found: {lora_file}")
        return None
    
    # For FAL.ai API, we need to encode the file to base64
    try:
        import base64
        with open(lora_file, "rb") as f:
            lora_content = f.read()
            lora_base64 = base64.b64encode(lora_content).decode("utf-8")
        
        # Return LoRA parameters
        return {
            "lora_base64": lora_base64,
            "lora_scale": lora_scale
        }
    except Exception as e:
        print(f"Error preparing LoRA file: {e}")
        return None

# Function to generate image with a specific model
def generate_with_model(
    model: FalModel, 
    prompt: str, 
    negative_prompt: Optional[str] = None,
    width: int = 1024,
    height: int = 1024,
    seed: Optional[int] = None,
    guidance_scale: float = 7.5,
    lora_file: Optional[str] = None,
    lora_scale: float = 0.8,
    service: Optional[FalImageGenerationService] = None,
) -> Dict[str, Any]:
    """Generate an image with the specified model and return results with timing data"""
    try:
        if not service:
            service = get_image_service()
            if not service:
                return {
                    "success": False,
                    "model_id": model.id,
                    "model_name": model.name,
                    "description": model.description,
                    "price": model.price_per_image,
                    "actual_time": 0.0,
                    "error": "FAL API key not set",
                    "used_lora": False,
                }
        
        # Use model's default negative prompt if none provided
        if negative_prompt is None:
            negative_prompt = model.default_negative_prompt
        
        # Prepare configuration
        config_kwargs = {
            "prompt": prompt,
            "negative_prompt": negative_prompt,
            "model_id": model.id,
            "width": width,
            "height": height,
            "seed": seed,
        }
        
        # Add guidance scale if model supports it
        if model.supports_guidance_scale:
            config_kwargs["guidance_scale"] = guidance_scale
        
        # Add LoRA parameters if model supports it and file is provided
        if model.supports_lora and lora_file:
            lora_params = prepare_lora_params(lora_file, lora_scale)
            if lora_params:
                # Add LoRA parameters to the API request
                config_kwargs["lora_params"] = lora_params
                print(f"Adding LoRA to {model.name} with scale {lora_scale}")
        
        # Create the configuration
        config = ImageGenerationConfig(**config_kwargs)
        
        # Generate image and track time
        start_time = time.time()
        result = service.generate_image(config)
        generation_time = time.time() - start_time
        
        # Return detailed results
        return {
            "success": result.success,
            "image_url": result.image_url,
            "model_id": model.id,
            "model_name": model.name,
            "description": model.description,
            "price": model.price_per_image,
            "actual_time": generation_time,
            "error": result.error if not result.success else None,
            "used_lora": lora_file is not None and model.supports_lora,
        }
    except Exception as e:
        print(f"Error generating image with {model.name}: {str(e)}")
        # Return a fallback result with error information
        return {
            "success": False,
            "model_id": model.id,
            "model_name": model.name,
            "description": model.description,
            "price": model.price_per_image,
            "actual_time": 0.0,
            "error": f"Generation error: {str(e)}",
            "used_lora": False,
        }

# Function to download an image from URL
def download_image(url: str) -> Optional[Image.Image]:
    """Download image from URL and return as PIL Image"""
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        return Image.open(BytesIO(response.content))
    except Exception as e:
        print(f"Error downloading image: {e}")
        return None

# Main function for Gradio interface to generate images with multiple models
def generate_comparison(
    prompt: str,
    selected_models: List[str],
    negative_prompt: str,
    width: int,
    height: int,
    guidance_scale: float,
    seed: int,
    use_same_seed: bool,
    use_lora: bool,
    lora_file: Optional[str],
    lora_scale: float,
):
    """Generate images with all selected models and return for comparison"""
    if not prompt.strip():
        return "Please enter a prompt", [], [], None
    
    if not selected_models:
        return "Please select at least one model", [], [], None
    
    # Initialize service
    service = get_image_service()
    if not service:
        return "FAL API key not set in environment variables", [], [], None
    
    # Filter models based on selection
    models_to_use = [m for m in FAL_MODELS if m.id in selected_models]
    
    # Check LoRA settings
    lora_path = None
    if use_lora and lora_file:
        # Check if any selected model supports LoRA
        if not any(m.supports_lora for m in models_to_use):
            return "None of the selected models support LoRA", [], [], None
        lora_path = lora_file
    
    # Generate images concurrently
    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=min(len(models_to_use), 3)) as executor:
        futures = []
        for model in models_to_use:
            # Generate a unique seed for each model if not using the same seed
            model_seed = seed if use_same_seed else seed + hash(model.id) % 1000000
            
            # Only use LoRA for models that support it
            model_lora_path = lora_path if use_lora and model.supports_lora else None
            
            future = executor.submit(
                generate_with_model,
                model=model,
                prompt=prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                seed=model_seed,
                guidance_scale=guidance_scale,
                lora_file=model_lora_path,
                lora_scale=lora_scale,
                service=service,
            )
            futures.append(future)
        
        # Collect results as they complete
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                results.append({
                    "success": False,
                    "model_name": "Error",
                    "error": str(e),
                })
    
    # Sort results by model name
    results.sort(key=lambda x: x.get("model_name", ""))
    
    # Create gallery items with labels
    gallery_images = []
    gallery_labels = []
    
    for result in results:
        # Ensure result is a dictionary with expected keys
        if not isinstance(result, dict):
            print(f"Skipping invalid result format")
            continue
            
        # Get model name with a default
        model_name = result.get("model_name", "Unknown Model")
        
        # Check if the generation was successful
        if result.get("success") and result.get("image_url"):
            try:
                # Download the image
                image = download_image(result.get("image_url"))
                if image:
                    gallery_images.append(image)
                    
                    # Safely format price and time
                    try:
                        price_str = f"${result.get('price', 0.0):.4f}"
                    except (TypeError, ValueError):
                        price_str = "Price: N/A"
                        
                    try:
                        time_str = f"{result.get('actual_time', 0.0):.2f}s"
                    except (TypeError, ValueError):
                        time_str = "Time: N/A"
                    
                    # Create a descriptive label
                    label = (
                        f"Model: {model_name}\n"
                        f"Price: {price_str}\n"
                        f"Time: {time_str}"
                    )
                    
                    # Add LoRA info if used
                    if result.get("used_lora"):
                        label += f"\nLoRA: ✅ ({lora_scale:.1f})"
                    
                    gallery_labels.append(label)
                else:
                    print(f"Skipping null image for {model_name}")
            except Exception as e:
                print(f"Error processing image for {model_name}: {e}")
        else:
            # Log errors but don't add to gallery
            error_msg = result.get("error", "Unknown error")
            print(f"Generation failed for {model_name}: {error_msg}")
    
    # Create detailed results table
    table_data = []
    for result in results:
        # Make sure we have all required keys with default values
        if not isinstance(result, dict):
            # Skip if result is not a dictionary
            continue
            
        # Get values with defaults for missing keys
        model_name = result.get("model_name", "Unknown Model")
        success = result.get("success", False)
        description = result.get("description", "")
        
        # Safely format numeric values
        try:
            actual_time = f"{result.get('actual_time', 0.0):.2f}" if 'actual_time' in result else "N/A"
        except (TypeError, ValueError):
            actual_time = "N/A"
            
        try:
            price = f"${result.get('price', 0.0):.4f}" if 'price' in result else "N/A"
        except (TypeError, ValueError):
            price = "N/A"
        
        # Create the row with safe values
        row = {
            "Model": model_name,
            "Success": "✓" if success else "✗",
            "Time (s)": actual_time,
            "Price ($)": price,
            "LoRA Used": "✅" if result.get("used_lora") else "❌",
            "Description": description,
        }
        
        # Add error info if available
        if not success and result.get("error"):
            row["Error"] = result.get("error")
            
        table_data.append(row)
    
    # Create DataFrame
    results_df = pd.DataFrame(table_data)
    
    # Summary text
    successful_generations = len([r for r in results if r.get("success", False) and r.get("image_url")])
    summary = f"Generated {successful_generations} of {len(results)} images"
    if use_lora and lora_path:
        summary += f" with LoRA (scale: {lora_scale:.1f})"
    if successful_generations < len(results):
        summary += " (some models failed, see table for details)"
    
    # Check if we have any images to display
    if not gallery_images:
        # Return empty lists instead of None to avoid Gradio errors
        return summary, [], [], results_df

    return summary, gallery_images, gallery_labels, results_df

# Build the Gradio interface
with gr.Blocks(title="FAL Model Comparison") as app:
    gr.Markdown("# 🖼️ FAL.ai Model Comparison Tool")
    gr.Markdown("Compare multiple FAL.ai image generation models with the same prompt.")
    
    with gr.Row():
        with gr.Column(scale=3):
            prompt_input = gr.Textbox(
                label="Prompt",
                placeholder="Enter a detailed image generation prompt...",
                lines=3,
            )
            
            negative_prompt_input = gr.Textbox(
                label="Negative Prompt",
                placeholder="Specify what to avoid in the image...",
                lines=2,
                value="ugly, blurry, low quality, distorted, bad anatomy, bad hands"
            )
            
            with gr.Row():
                with gr.Column(scale=1):
                    width_input = gr.Dropdown(
                        label="Width",
                        choices=[512, 768, 1024, 1280, 1536],
                        value=1024,
                    )
                    
                    guidance_scale_input = gr.Slider(
                        label="Guidance Scale",
                        minimum=1.0,
                        maximum=20.0,
                        value=7.5,
                        step=0.5,
                    )
                    
                    seed_input = gr.Number(
                        label="Seed",
                        value=42,
                        precision=0,
                    )
                
                with gr.Column(scale=1):
                    height_input = gr.Dropdown(
                        label="Height",
                        choices=[512, 768, 1024, 1280, 1536],
                        value=1024,
                    )
                    
                    same_seed_checkbox = gr.Checkbox(
                        label="Use Same Seed for All Models",
                        value=True,
                    )
            
            # LoRA controls
            with gr.Accordion("LoRA Settings", open=False):
                gr.Markdown("LoRA will only be applied to models that support it (marked with ✅)")
                
                with gr.Row():
                    use_lora_checkbox = gr.Checkbox(
                        label="Use LoRA",
                        value=False,
                    )
                    
                    lora_scale_slider = gr.Slider(
                        label="LoRA Scale",
                        minimum=0.1,
                        maximum=1.0,
                        value=0.8,
                        step=0.05,
                    )
                
                lora_file_input = gr.File(
                    label="Upload LoRA File",
                    file_types=[".safetensors", ".pt", ".bin", ".ckpt"],
                    file_count="single",
                )
            
            model_checkboxes = gr.CheckboxGroup(
                label="Select Models to Compare",
                choices=[model.id for model in FAL_MODELS],
                value=[model.id for model in FAL_MODELS[:3]],  # Default: select first 3 models
            )
            
            # Add a helper function to filter models by LoRA support
            def filter_lora_models(show_only_lora_models):
                if show_only_lora_models:
                    return gr.update(choices=[m.id for m in FAL_MODELS if m.supports_lora])
                else:
                    return gr.update(choices=[m.id for m in FAL_MODELS])
            
            # Add a checkbox to filter models by LoRA support
            with gr.Row():
                show_lora_models_checkbox = gr.Checkbox(
                    label="Show only LoRA-compatible models",
                    value=False,
                )
                show_lora_models_checkbox.change(
                    fn=filter_lora_models,
                    inputs=[show_lora_models_checkbox],
                    outputs=[model_checkboxes],
                )
            
            with gr.Row():
                generate_button = gr.Button("Generate Comparison", variant="primary")
                clear_button = gr.Button("Clear Results")
        
        with gr.Column(scale=2):
            gr.Markdown("### Available Models")
            
            # Show model info table
            models_df = pd.DataFrame([{
                "Model": model.name,
                "Description": model.description,
                "Price": f"${model.price_per_image:.4f}",
                "LoRA Support": "✅" if model.supports_lora else "❌",
                "ID": model.id,
            } for model in FAL_MODELS])
            
            models_table = gr.Dataframe(
                value=models_df,
                interactive=False,
            )
            
            # Add select all and clear buttons for model selection
            with gr.Row():
                select_all_btn = gr.Button("Select All Models")
                clear_selection_btn = gr.Button("Clear Selection")
    
    # Add a separator
    gr.Markdown("---")
    
    # Results Section
    with gr.Row():
        with gr.Column():
            status_text = gr.Textbox(label="Status", interactive=False)
    
    # Gallery for generated images
    with gr.Row():
        gallery = gr.Gallery(
            label="Generated Images",
            columns=3,
            rows=2,
            object_fit="contain",
            height="auto",
            show_label=True,
            elem_id="gallery_output"
        )
    
    # Results table for comparison
    with gr.Row():
        results_table = gr.Dataframe(label="Results Comparison")
    
    # Custom function to update the UI based on generation results
    def update_ui_with_results(summary, images, labels, results_df):
        # Check if images are available
        if images is None or len(images) == 0:
            return (
                summary, 
                gr.update(value=None, visible=False),  # Hide gallery if no images
                results_df
            )
        else:
            return (
                summary,
                gr.update(value=images, visible=True),  # Show gallery with images
                results_df
            )
    
    # Connect the buttons
    generate_button.click(
        fn=generate_comparison,
        inputs=[
            prompt_input,
            model_checkboxes,
            negative_prompt_input,
            width_input,
            height_input,
            guidance_scale_input,
            seed_input,
            same_seed_checkbox,
            use_lora_checkbox,
            lora_file_input,
            lora_scale_slider,
        ],
        outputs=[
            status_text,
            gallery,
            gallery,  # Using the same gallery for both images and labels
            results_table,
        ]
    )
    
    # Select all models button
    select_all_btn.click(
        fn=lambda: [model.id for model in FAL_MODELS],
        outputs=[model_checkboxes],
    )
    
    # Clear model selection button
    clear_selection_btn.click(
        fn=lambda: [],
        outputs=[model_checkboxes],
    )
    
    # Clear results button
    clear_button.click(
        fn=lambda: ("", [], [], None),
        outputs=[status_text, gallery, gallery, results_table],
    )

# Run the app
if __name__ == "__main__":
    print("Starting FAL Model Comparison Tool...")
    print(f"Using FAL API key: {'YES' if os.getenv('FAL_KEY') else 'NO - Models will not work'}")
    print(f"Models available: {len(FAL_MODELS)}")
    
    # Launch the app
    app.launch(server_name="127.0.0.1", server_port=7861) 