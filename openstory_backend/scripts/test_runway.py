import os
import time
import base64
from runwayml import RunwayML
from dotenv import load_dotenv

load_dotenv()
client = RunwayML(api_key=os.getenv("RUNWAYML_API_SECRET"))

# get local image data url
filepath = "/Users/<USER>/Downloads/dog.jpg"
ext = filepath.split(".")[-1]
binary = open(filepath, "rb").read()
base64_utf8_str = base64.b64encode(binary).decode("utf-8")
dataurl = f"data:image/{ext};base64,{base64_utf8_str}"

# Create a new image-to-video task using the "gen4_turbo" model
task = client.image_to_video.create(
    model="gen4_turbo",
    # Point this at your own image file
    prompt_image=dataurl,
    prompt_text="Generate a video",
    ratio="1280:720",
    duration=5,
)
task_id = task.id

# Poll the task until it's complete
time.sleep(10)  # Wait for a second before polling
task = client.tasks.retrieve(task_id)
while task.status not in ["SUCCEEDED", "FAILED"]:
    time.sleep(10)  # Wait for ten seconds before polling
    task = client.tasks.retrieve(task_id)

print("Task complete:", task)
