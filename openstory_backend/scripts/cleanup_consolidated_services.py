#!/usr/bin/env python3
"""
Cleanup script to remove the original service files that were consolidated.
This script is meant to be run after verifying that the consolidated services work correctly.
"""
import os
import sys

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Files consolidated into video_production.py
VIDEO_PRODUCTION_FILES = [
    "app/services/show.py",
    "app/services/episode.py",
    "app/services/shot.py"
]

# Files consolidated into character_services.py
CHARACTER_SERVICES_FILES = [
    "app/services/character.py",
    "app/services/character_prompt.py",
    "app/services/progression.py",
    "app/services/image_styles.py"
]

def remove_files(file_list):
    """Remove the files in the given list."""
    for file_path in file_list:
        try:
            full_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), file_path)
            if os.path.exists(full_path):
                print(f"Removing {file_path}...")
                os.remove(full_path)
            else:
                print(f"File not found: {file_path}")
        except Exception as e:
            print(f"Error removing {file_path}: {e}")

def main():
    """Run the cleanup script."""
    print("\n=== CLEANING UP CONSOLIDATED SERVICES ===\n")
    
    # Ask for confirmation
    confirmation = input("This will remove original service files that were consolidated. Continue? [y/N]: ")
    if confirmation.lower() != 'y':
        print("Operation canceled.")
        return
    
    # Remove files
    print("\nRemoving video production files...")
    remove_files(VIDEO_PRODUCTION_FILES)
    
    print("\nRemoving character services files...")
    remove_files(CHARACTER_SERVICES_FILES)
    
    print("\n=== CLEANUP COMPLETE ===\n")

if __name__ == "__main__":
    main() 