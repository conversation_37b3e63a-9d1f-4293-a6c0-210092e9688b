#!/usr/bin/env python3
"""
Test script for the LLM API endpoint.
Run this script directly to test the LLM API endpoint with proper authentication.
"""

import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
SUPABASE_URL = os.getenv("SUPABASE_URL", "http://127.0.0.1:54321")
SUPABASE_KEY = os.getenv("SUPABASE_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0")


def get_auth_token(email="<EMAIL>", password="testing"):
    """Get an authentication token from Supabase"""
    print(f"Getting auth token for {email} from {SUPABASE_URL}")
    
    auth_url = f"{SUPABASE_URL}/auth/v1/token?grant_type=password"
    headers = {
        "apikey": SUPABASE_KEY,
        "Content-Type": "application/json"
    }
    data = {
        "email": email,
        "password": password
    }
    
    try:
        response = requests.post(auth_url, headers=headers, json=data)
        response.raise_for_status()
        token_data = response.json()
        access_token = token_data.get("access_token")
        print(f"Successfully got auth token: {access_token[:20]}...")
        return access_token
    except Exception as e:
        print(f"Error getting auth token: {str(e)}")
        return None


def test_episode_generator(token, user_story="A detective solves a mysterious case in a small town."):
    """Test the episode generator endpoint"""
    print("\n=== Testing Episode Generator ===")
    endpoint = f"{API_BASE_URL}/api/v1/llm/prompt"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "template_id": "episode_generator",
        "variables": {
            "user_story": user_story
        },
        "model": "gpt-4o-mini",
        "temperature": 0.7
    }
    
    print(f"Making request to {endpoint}")
    print(f"Headers: {json.dumps(headers, default=str)}")
    print(f"Data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(endpoint, headers=headers, json=data)
        print(f"Response status code: {response.status_code}")
        
        try:
            result = response.json()
            print(f"Response body: {json.dumps(result, indent=2)[:500]}...")
            return result
        except:
            print(f"Failed to parse response as JSON: {response.text}")
            return {"error": "Failed to parse response as JSON"}
    except Exception as e:
        print(f"Error making request: {str(e)}")
        return {"error": str(e)}


def test_storyboard_generator(token, storyline="A detective investigates a mysterious murder in a small town.", 
                                 characters="Detective John Smith, male, 45, hardened veteran with a troubled past."):
    """Test the storyboard generator endpoint"""
    print("\n=== Testing Storyboard Generator ===")
    endpoint = f"{API_BASE_URL}/api/v1/llm/prompt"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "template_id": "storyboard_generator",
        "variables": {
            "storyline": storyline,
            "characters": characters
        },
        "model": "gpt-4o-mini",
        "temperature": 0.7
    }
    
    print(f"Making request to {endpoint}")
    print(f"Headers: {json.dumps(headers, default=str)}")
    print(f"Data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(endpoint, headers=headers, json=data)
        print(f"Response status code: {response.status_code}")
        
        try:
            result = response.json()
            print(f"Response body: {json.dumps(result, indent=2)[:500]}...")
            return result
        except:
            print(f"Failed to parse response as JSON: {response.text}")
            return {"error": "Failed to parse response as JSON"}
    except Exception as e:
        print(f"Error making request: {str(e)}")
        return {"error": str(e)}


def main():
    """Main function to run the tests"""
    token = get_auth_token()
    if not token:
        print("Failed to get auth token. Exiting.")
        return
    
    # Test episode generator
    episode_result = test_episode_generator(token)
    print(f"Episode generator test result: {'SUCCESS' if 'error' not in episode_result else 'FAILED'}")
    
    # Test storyboard generator
    storyboard_result = test_storyboard_generator(token)
    print(f"Storyboard generator test result: {'SUCCESS' if 'error' not in storyboard_result else 'FAILED'}")


if __name__ == "__main__":
    main() 