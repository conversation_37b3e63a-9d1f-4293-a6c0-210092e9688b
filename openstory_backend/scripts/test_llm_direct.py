#!/usr/bin/env python3
import json
import os
import asyncio
from dotenv import load_dotenv
from openai import AsyncOpenAI
import sys
from pathlib import Path
from typing import Optional

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# Import our logging utilities
from app.utils.llm_logger import log_openai_request, log_openai_response, log_openai_stream_complete

# Add the app directory to the path so we can import modules from it
sys.path.append(str(Path(__file__).parent))

# Try to import directly from the app
try:
    from app.api.v1.llm import load_prompt, format_prompt, stream_openai_response
except ImportError:
    print("Could not import from app, using local implementations")
    # Local implementation of functions from llm.py
    def load_prompt(prompt_id: str):
        """Loads prompt data from a JSON file."""
        prompts_dir = os.path.join(Path(__file__).parent, "app", "prompts")
        file_path = os.path.join(prompts_dir, f"{prompt_id}.json")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Prompt file not found: {file_path}")

        try:
            with open(file_path, "r") as f:
                data = json.load(f)
            return data
        except json.JSONDecodeError:
            raise ValueError(f"Error decoding JSON from prompt file: {file_path}")

    def format_prompt(template, variables):
        """Formats a prompt template string with given variables."""
        if template is None:
            return None
        return template.format(**variables)

    async def stream_openai_response(messages, model, temperature, max_tokens, functions):
        """Simplified version of the streaming function for testing."""
        openai_client = AsyncOpenAI(api_key=os.getenv("openai_api_key"))
        
        stream_params = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "stream": True,
        }

        if max_tokens is not None:
            stream_params["max_tokens"] = max_tokens

        if functions:
            stream_params["functions"] = functions
            stream_params["function_call"] = "auto"

        try:
            # Log the OpenAI request
            request_metadata = log_openai_request(
                model=model,
                messages=messages,
                functions=functions,
                temperature=temperature,
                max_tokens=max_tokens,
                streaming=True
            )
            
            stream = await openai_client.chat.completions.create(**stream_params)
            
            # Log that streaming has started
            log_openai_response(
                response="Streaming started",
                request_metadata=request_metadata,
                is_streaming=True
            )

            async for chunk in stream:
                delta = chunk.choices[0].delta

                # Stream content
                if delta and delta.content:
                    yield f"data: {json.dumps({'type': 'content', 'data': delta.content})}\n\n"

                # Stream function call
                if delta and delta.function_call:
                    name = delta.function_call.name
                    args = delta.function_call.arguments

                    if name:
                        yield f"data: {json.dumps({'type': 'function_start', 'name': name})}\n\n"

                    if args:
                        yield f"data: {json.dumps({'type': 'function_args', 'args_chunk': args})}\n\n"

                # Check finish reason
                finish_reason = chunk.choices[0].finish_reason
                if finish_reason:
                    yield f"data: {json.dumps({'type': 'finish', 'reason': finish_reason})}\n\n"
                    
                    # Log completion
                    log_openai_stream_complete(request_metadata)
                    break

        except Exception as e:
            # Log error
            log_openai_response(
                response=str(e),
                request_metadata=request_metadata if 'request_metadata' in locals() else {"request_id": "unknown"},
                is_error=True
            )
            yield f"data: {json.dumps({'type': 'error', 'detail': f'Error: {e}'})}\n\n"

# Load environment variables
load_dotenv()

# Test function
async def test_llm_direct():
    print("Testing LLM functionality directly...")
    
    # 1. Test loading a prompt
    prompt_id = "simple_greeting"  # Use one of your existing prompts
    try:
        prompt_data = load_prompt(prompt_id)
        print(f"✅ Loaded prompt: {prompt_id}")
        print(f"System prompt: {prompt_data.get('system_prompt_template')}")
        print(f"User prompt: {prompt_data.get('user_prompt_template')}")
    except Exception as e:
        print(f"❌ Error loading prompt: {e}")
        # Use a fallback for testing
        prompt_data = {
            "system_prompt_template": "You are a friendly assistant.",
            "user_prompt_template": "Say hello."
        }
        print("Using fallback prompt data")
    
    # 2. Test formatting the prompt
    variables = {}  # Add test variables if your prompt needs them
    try:
        system_prompt = format_prompt(prompt_data.get("system_prompt_template"), variables)
        user_prompt = format_prompt(prompt_data.get("user_prompt_template"), variables)
        print("✅ Formatted prompts successfully")
    except Exception as e:
        print(f"❌ Error formatting prompts: {e}")
        system_prompt = "You are a friendly assistant."
        user_prompt = "Say hello."
        print("Using fallback prompts")
    
    # 3. Prepare messages for OpenAI
    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": user_prompt})
    
    # 4. Test streaming response from OpenAI
    print("\nStreaming response from OpenAI:\n")
    functions = prompt_data.get("functions")
    
    try:
        async for chunk in stream_openai_response(
            messages=messages,
            model="gpt-4o-mini",  # Use a smaller model for testing
            temperature=0.7,
            max_tokens=None,
            functions=functions,
        ):
            if chunk.startswith("data: "):
                data = json.loads(chunk[6:])
                
                if data["type"] == "content":
                    print(f"Content: {data['data']}")
                elif data["type"] == "function_start":
                    print(f"Function called: {data['name']}")
                elif data["type"] == "function_args":
                    print(f"Function args: {data['args_chunk']}")
                elif data["type"] == "finish":
                    print(f"\nStream finished. Reason: {data['reason']}")
                elif data["type"] == "error":
                    print(f"Error: {data['detail']}")
        
        print("\n✅ OpenAI streaming completed")
    except Exception as e:
        print(f"\n❌ Error with OpenAI streaming: {e}")
    
    print("\nTest completed!")

if __name__ == "__main__":
    asyncio.run(test_llm_direct()) 