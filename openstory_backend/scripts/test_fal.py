import asyncio
import fal_client


async def subscribe():
    handler = await fal_client.submit_async(
        "fal-ai/kling-video/v1/standard/image-to-video",
        arguments={
            "prompt": "Snowflakes fall as a car moves forward along the road.",
            "image_url": "https://storage.googleapis.com/falserverless/kling/kling_input.jpeg",
        },
    )

    async for event in handler.iter_events(with_logs=True):
        print(event)

    result = await handler.get()
    print(result)


if __name__ == "__main__":
    asyncio.run(subscribe())
