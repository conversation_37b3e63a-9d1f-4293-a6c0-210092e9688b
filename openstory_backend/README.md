# Drama Backend

This is the backend service for the Drama application, built with FastAPI and Supabase.

## Authentication Flow

This backend uses Supabase for user authentication, but the primary interaction (signup, signin) happens on the **frontend client** (e.g., mobile app using `@supabase/supabase-js`).

1.  **Frontend:** Handles user signup/signin directly with Supabase Auth.
2.  **Frontend:** Obtains JWT (access token) from Supabase upon successful login.
3.  **Frontend:** Sends this JWT in the `Authorization: Bearer <token>` header when making requests to protected routes on this backend API.
4.  **Backend:** Verifies the received JWT using the `JWT_SECRET` environment variable.
5.  **Backend:** Uses the `sub` (user ID) claim from the *validated* JWT to identify the user.
6.  **Backend:** On the user's first API call, it automatically creates a corresponding user profile record in its own database if one doesn't exist.

## Setup

1.  Create a virtual environment:
    ```bash
    python -m venv venv
    source venv/bin/activate  # On Windows: venv\Scripts\activate
    ```


2.  Install dependencies:
    ```bash
    pip install -r requirements.txt
    ```


    Ngrok ngrok start --all --config=./ngrok.yml

3.  Create a `.env` file in the project root with the following variables:
    ```dotenv
    # Your Supabase project URL
    SUPABASE_URL=your_supabase_url 
    # Your Supabase project anon key (used by backend Supabase client)
    SUPABASE_KEY=your_supabase_key 
    # Your Supabase project JWT Secret (!!! IMPORTANT for verifying tokens from frontend)
    JWT_SECRET=your_supabase_jwt_secret 
    # Your PostgreSQL connection string (if using a separate DB)
    DATABASE_URL=postgresql://user:password@host:port/dbname 
    # Set to "production" or "development"
    ENVIRONMENT=development 
    ```
    *   Obtain the `JWT_SECRET` from your Supabase project dashboard: Project Settings > API > JWT Settings.

4.  Run the development server:
    ```bash
    uvicorn app.main:app --reload
    ```

## API Endpoints

Key authentication-related endpoints:

*   **`GET /api/v1/auth/profile`**: Returns the backend profile data for the authenticated user (requires valid `Authorization: Bearer` token). Creates the profile on first access.
*   **`GET /api/v1/auth/me`**: Returns the raw, validated JWT payload for the authenticated user (requires valid `Authorization: Bearer` token).
*   **`POST /api/v1/auth/refresh`**: (If used) Allows exchanging a valid refresh token for a new access token.

Other endpoints (e.g., `/api/v1/shows`) are protected and require a valid `Authorization: Bearer` token.

## Project Structure

```
app/
├── api/            # API routes (v1/deps.py is key for auth)
├── core/           # Core functionality (config.py, security.py)
├── db/             # Database models and operations
├── schemas/        # Pydantic models
├── services/       # Business logic (UserService, etc.)
└── main.py         # Application entry point
```

## API Documentation

Once the server is running, access the interactive API documentation:

*   Swagger UI: `http://localhost:8000/docs`
*   ReDoc: `http://localhost:8000/redoc` 