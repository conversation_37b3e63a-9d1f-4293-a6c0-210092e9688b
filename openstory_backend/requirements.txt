fastapi==0.109.2
uvicorn==0.27.1
pydantic>=2.0.0
pydantic-settings>=2.0.0
python-dotenv==1.0.1
supabase>=2.15.1
gotrue>=2.12.0
postgrest>=1.0.1
realtime>=2.4.3
storage3>=0.11.3
supafunc>=0.9.4
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.9
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.9  # PostgreSQL adapter for SQLAlchemy
openai>=1.0.0 # Add OpenAI library
alembic>=1.13.0 # Add Alembic for database migrations
httpx>=0.28.1
httpcore>=1.0.9
pyjwt>=2.10.1
uuid==1.30
jinja2==3.1.2
pyyaml==6.0.1
jsonschema==4.17.3
email-validator>=2.2.0
fal-client>=0.6.0
httpx-sse>=0.4.0
elevenlabs>=2.0.0
runwayml>=3.1.0
llama-index==0.12.40
llama-index-agent-openai>=0.4.0
llama-index-llms-openai>=0.3.0

# Development dependencies
watchdog>=2.3.1
gradio>=4.0.0 
pytest-asyncio>=0.26.0
