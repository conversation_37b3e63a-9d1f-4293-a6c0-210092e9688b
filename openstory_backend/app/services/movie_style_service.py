from uuid import UUID
from typing import List, Optional

from app.utils.logger import logger
from app.db.supabase import supabase_client
from app.schemas.movie_style import MovieStyleCreate, MovieStyleUpdate, MovieStyle


class MovieStyleService:
    def __init__(self):
        self.supabase = supabase_client
        self.table = "movie_styles"
        logger.debug("MovieStyleService initialized")

    def get_movie_styles(self, skip: int = 0, limit: int = 100) -> List[MovieStyle]:
        """Get all movie styles with pagination."""
        try:
            logger.error(f"test supabase {self.supabase}")

            response = (
                self.supabase.table(self.table)
                .select("*")
                .limit(limit)
                .offset(skip)
                .execute()
            )

            logger.error(f"Raw response data: {response}")

            if not response.data:
                logger.warning("No movie styles found in the database")
                return []

            styles = []
            for item in response.data:
                try:
                    style = MovieStyle.model_validate(item)
                    styles.append(style)
                except Exception as e:
                    logger.error(f"Error converting item to MovieStyle: {str(e)}")
                    logger.error(f"Problematic item: {item}")
                    continue

            logger.info(f"Successfully retrieved {len(styles)} movie styles")
            return styles
        except Exception as e:
            logger.exception(f"Error fetching movie styles: {str(e)}")
            return []

    def get_movie_style(self, movie_style_id: UUID) -> Optional[MovieStyle]:
        response = (
            self.supabase.table(self.table)
            .select("*")
            .eq("id", str(movie_style_id))
            .execute()
        )
        if response.data:
            return MovieStyle(**response.data[0])
        return None

    def create_movie_style(self, movie_style: MovieStyleCreate) -> MovieStyle:
        movie_style_data = movie_style.model_dump()
        response = self.supabase.table(self.table).insert(movie_style_data).execute()
        return MovieStyle(**response.data[0])

    def update_movie_style(
        self, movie_style_id: UUID, movie_style: MovieStyleUpdate
    ) -> Optional[MovieStyle]:
        update_data = {
            k: v for k, v in movie_style.model_dump().items() if v is not None
        }
        if not update_data:
            return self.get_movie_style(movie_style_id)

        response = (
            self.supabase.table(self.table)
            .update(update_data)
            .eq("id", str(movie_style_id))
            .execute()
        )
        if response.data:
            return MovieStyle(**response.data[0])
        return None

    def delete_movie_style(self, movie_style_id: UUID) -> bool:
        response = (
            self.supabase.table(self.table)
            .delete()
            .eq("id", str(movie_style_id))
            .execute()
        )
        return bool(response.data)
