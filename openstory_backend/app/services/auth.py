from typing import Optional, <PERSON><PERSON>
from app.db.supabase import supabase_client
from app.schemas.user import User
from app.services.user import UserService
import logging


class AuthService:
    def __init__(self):
        self.supabase = supabase_client
        # No longer need UserService directly here if sign_up/sign_in are removed
        # self.user_service = UserService()

    # Method likely no longer needed as frontend handles signup
    # async def sign_up(...)

    # Method likely no longer needed as frontend handles signin
    # async def sign_in(...)

    async def sign_out(self, access_token: str) -> bool:
        """Signs out the user from Supabase session.
        Note: Client-side token removal is usually sufficient.
        This invalidates the specific session on Supabase side.
        """
        try:
            # The sign_out method might require the JWT or be parameterless
            # depending on the Supabase client version. Check documentation.
            # Assuming parameterless for now based on previous code.
            self.supabase.auth.sign_out()
            logging.info("User signed out from Supabase session.")
            return True
        except Exception as e:
            logging.exception(f"Error during Supabase sign_out call: {e}")
            return False

    # Method likely redundant now that deps.get_current_user_from_supabase handles it
    # async def get_current_user(...)

    async def refresh_token(
        self, refresh_token: str
    ) -> Tuple[Optional[str], Optional[str]]:
        """Refreshes Supabase session using a refresh token.
        Note: Frontend Supabase client often handles this automatically.
        """
        try:
            # Ensure refresh_token is passed correctly based on Supabase client version
            response = self.supabase.auth.refresh_session(refresh_token)

            # Handle both dict and object responses
            access_token = None
            if isinstance(response, dict):
                session_data = response.get("session")
                if not session_data:
                    logging.error(
                        f"Failed to refresh token, no session data in response: {response}"
                    )
                    return None, "Failed to refresh token (no session)"
                access_token = session_data.get("access_token")
            else:
                session_obj = getattr(response, "session", None)
                if not session_obj:
                    logging.error(
                        f"Failed to refresh token, no session object in response: {response}"
                    )
                    return None, "Failed to refresh token (no session obj)"
                access_token = getattr(session_obj, "access_token", None)

            if not access_token:
                logging.error(f"Missing access token in refresh response: {response}")
                return None, "Missing access token in refresh response"

            logging.info("Successfully refreshed Supabase token.")
            return access_token, None
        except Exception as e:
            logging.exception(f"Exception during Supabase refresh_token call: {e}")
            # Check for specific Supabase errors if possible
            error_detail = str(e)
            if hasattr(e, "message"):  # Handle potential Supabase error objects
                error_detail = e.message
            return None, f"Exception during token refresh: {error_detail}"

