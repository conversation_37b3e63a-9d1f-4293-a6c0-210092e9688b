import fal_client
import asyncio
import os

from app.utils.logger import logger
from app.core.config import get_settings


settings = get_settings()
fal_client.api_key = os.getenv("FAL_KEY")


async def lip_sync_generate(video_url: str, audio_url: str) -> dict:
    try:
        def on_queue_update(update):
            if isinstance(update, fal_client.InProgress):
                for log in update.logs:
                    logger.error(f"Lip sync progress: {log['message']}")

        logger.error(f"Starting lip sync with video_url: {video_url} and audio_url: {audio_url}")
        
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            None,
            lambda: fal_client.subscribe(
                "fal-ai/kling-video/lipsync/audio-to-video",
                arguments={
                    "video_url": video_url,
                    "audio_url": audio_url,
                },
                with_logs=True,
                on_queue_update=on_queue_update,
            ),
        )
        logger.error(f"Lip sync completed with result: {result}")
        
        if not result:
            logger.error("Lip sync returned empty result")
            return None
            
        if not isinstance(result, dict):
            logger.error(f"Lip sync returned unexpected result type: {type(result)}")
            return None
            
        return result
    except Exception as e:
        logger.error(f"Error in lip_sync_generate: {str(e)}")
        return None
