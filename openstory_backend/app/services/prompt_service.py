"""
Prompt Template Service - Handles loading, validating, and rendering prompt templates
"""

import os
import yaml
import jsonschema
from datetime import datetime, date
from typing import Any, Dict, List, Optional, TypedDict
from jinja2 import Template, Environment, FileSystemLoader, select_autoescape
from app.utils.logger import logger


# Define the base directory for prompt templates
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
PROMPT_TEMPLATES_DIR = os.path.join(BASE_DIR, "prompt_templates")

# Create Jinja environment
env = Environment(
    loader=FileSystemLoader(PROMPT_TEMPLATES_DIR),
    autoescape=select_autoescape(["html", "xml"]),
    trim_blocks=True,
    lstrip_blocks=True,
)


# Custom filters for Jinja
def format_date(value, format="%Y-%m-%d"):
    """Format a date object or ISO date string"""
    if isinstance(value, str):
        try:
            value = datetime.fromisoformat(value)
        except ValueError:
            try:
                value = datetime.strptime(value, format)
            except ValueError:
                return value
    if isinstance(value, (datetime, date)):
        return value.strftime(format)
    return value


# Add filters to Jinja environment
env.filters["format_date"] = format_date


class PromptVariable(TypedDict):
    """Type definition for a prompt variable configuration"""

    name: str
    type: str
    required: bool
    default: Optional[Any]
    description: Optional[str]


class PromptTemplate(TypedDict):
    """Type definition for a prompt template configuration"""

    id: str
    description: str
    variables: List[PromptVariable]
    system_template: Optional[str]
    user_template: str
    functions: Optional[List[Dict[str, Any]]]


class PromptService:
    """Service for managing prompt templates"""

    def __init__(self):
        self.templates: Dict[str, PromptTemplate] = {}
        self.load_all_templates()

    def load_all_templates(self) -> None:
        """Load all template files from the templates directory"""
        if not os.path.exists(PROMPT_TEMPLATES_DIR):
            os.makedirs(PROMPT_TEMPLATES_DIR)
            logger.warning(
                f"Created prompt templates directory: {PROMPT_TEMPLATES_DIR}"
            )
            return

        logger.info(f"Loading prompt templates from {PROMPT_TEMPLATES_DIR}")
        template_files = [
            f
            for f in os.listdir(PROMPT_TEMPLATES_DIR)
            if f.endswith(".yaml") or f.endswith(".yml")
        ]

        for filename in template_files:
            try:
                template_id = os.path.splitext(filename)[0]
                filepath = os.path.join(PROMPT_TEMPLATES_DIR, filename)
                with open(filepath, "r", encoding="utf-8") as f:
                    template_config = yaml.safe_load(f)

                # Verify the template has the required fields
                if not template_config.get("id"):
                    template_config["id"] = template_id

                # Store the template
                self.templates[template_id] = template_config
                logger.info(f"Loaded template: {template_id}")
            except Exception as e:
                logger.error(f"Error loading template {filename}: {str(e)}")

    def reload_templates(self) -> None:
        """Reload all templates (useful for development)"""
        self.templates = {}
        self.load_all_templates()

    def get_template(self, template_id: str) -> PromptTemplate:
        """Get a template by ID"""
        if template_id not in self.templates:
            raise ValueError(f"Template not found: {template_id}")
        return self.templates[template_id]

    def list_templates(self) -> List[Dict[str, str]]:
        """List all available templates with their descriptions"""
        return [
            {"id": t_id, "description": t.get("description", "")}
            for t_id, t in self.templates.items()
        ]

    def build_schema(self, template: PromptTemplate) -> Dict[str, Any]:
        """Build a JSON schema from the template variables definition"""
        properties = {}
        required = []

        for var in template.get("variables", []):
            var_name = var["name"]
            var_type = var["type"].lower()
            var_required = var.get("required", False)

            # Map template type to JSON schema type
            if var_type == "string":
                properties[var_name] = {"type": "string"}
            elif var_type == "number":
                properties[var_name] = {"type": "number"}
            elif var_type == "integer":
                properties[var_name] = {"type": "integer"}
            elif var_type == "boolean":
                properties[var_name] = {"type": "boolean"}
            elif var_type == "date":
                # Dates are handled as strings in JSON
                properties[var_name] = {"type": "string", "format": "date"}
            elif var_type == "array":
                properties[var_name] = {"type": "array"}
            elif var_type == "object":
                properties[var_name] = {"type": "object"}
            else:
                properties[var_name] = {}

            # Add required variables to the required list
            if var_required:
                required.append(var_name)

        return {"type": "object", "properties": properties, "required": required}

    def prepare_variables(
        self, template: PromptTemplate, input_vars: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate input variables against the template schema and apply defaults"""
        variables = template.get("variables", [])

        # Apply defaults for missing variables
        result = input_vars.copy()
        for var in variables:
            var_name = var["name"]
            if var_name not in result and "default" in var:
                result[var_name] = var["default"]

        # Validate against the schema
        schema = self.build_schema(template)
        try:
            jsonschema.validate(result, schema)
        except jsonschema.exceptions.ValidationError as e:
            logger.error(f"Variable validation error: {str(e)}")
            raise ValueError(f"Invalid variables: {str(e)}")

        return result

    def render_template(
        self, template_id: str, variables: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Render a template with the provided variables"""
        template = self.get_template(template_id)

        # Validate and prepare variables
        prepared_vars = self.prepare_variables(template, variables)

        # Render system prompt if present
        system_prompt = None
        if template.get("system_template"):
            system_template = Template(template["system_template"])
            system_prompt = system_template.render(**prepared_vars)

        # Render user prompt
        user_template = Template(template["user_template"])
        user_prompt = user_template.render(**prepared_vars)

        # Return the rendered prompts and any functions
        result = {
            "system_prompt": system_prompt,
            "user_prompt": user_prompt,
            "functions": template.get("functions"),
        }

        return result


# Singleton instance
_instance = None


def get_prompt_service() -> PromptService:
    """Get or create the PromptService singleton"""
    global _instance
    if _instance is None:
        _instance = PromptService()
    return _instance

