from typing import List, Optional
from uuid import UUID
from app.db.supabase import supabase_client
from app.schemas.session import Session, SessionCreate, Message, MessageCreate
from app.services.character_services import CharacterProgressionService


class SessionService:
    def __init__(self):
        self.supabase = supabase_client
        self.table = "sessions"
        self.messages_table = "messages"
        self.progression_service = CharacterProgressionService()

    def get_by_id(self, session_id: UUID) -> Optional[Session]:
        response = (
            self.supabase.table(self.table)
            .select("*")
            .eq("id", str(session_id))
            .execute()
        )
        if not response.data:
            return None

        session = Session(**response.data[0])

        # Get session messages
        messages_response = (
            self.supabase.table(self.messages_table)
            .select("*")
            .eq("session_id", str(session_id))
            .order("timestamp")
            .execute()
        )
        session.messages = [Message(**item) for item in messages_response.data]

        return session

    def get_by_user_id(self, user_id: UUID) -> List[Session]:
        response = (
            self.supabase.table(self.table)
            .select("*")
            .eq("user_id", str(user_id))
            .order("started_at", desc=True)
            .execute()
        )
        sessions = [Session(**item) for item in response.data]

        # We could load messages for all sessions, but that might be expensive
        # Instead, we'll return sessions without messages loaded

        return sessions

    def create(self, session: SessionCreate) -> Optional[Session]:
        # Verify character progression exists
        progression = self.progression_service.get_by_id(
            session.character_progression_id
        )
        if not progression:
            return None

        session_data = session.model_dump()

        response = self.supabase.table(self.table).insert(session_data).execute()
        return Session(**response.data[0])

    def delete(self, session_id: UUID) -> bool:
        # Delete messages first (should cascade, but being explicit)
        self.supabase.table(self.messages_table).delete().eq(
            "session_id", str(session_id)
        ).execute()

        # Then delete the session
        response = (
            self.supabase.table(self.table).delete().eq("id", str(session_id)).execute()
        )
        return bool(response.data)


class MessageService:
    def __init__(self):
        self.supabase = supabase_client
        self.table = "messages"

    def get_by_id(self, message_id: UUID) -> Optional[Message]:
        response = (
            self.supabase.table(self.table)
            .select("*")
            .eq("id", str(message_id))
            .execute()
        )
        if response.data:
            return Message(**response.data[0])
        return None

    def get_by_session_id(self, session_id: UUID) -> List[Message]:
        response = (
            self.supabase.table(self.table)
            .select("*")
            .eq("session_id", str(session_id))
            .order("timestamp")
            .execute()
        )
        return [Message(**item) for item in response.data]

    def create(self, message: MessageCreate) -> Message:
        message_data = message.model_dump()

        response = self.supabase.table(self.table).insert(message_data).execute()
        return Message(**response.data[0])

    def delete(self, message_id: UUID) -> bool:
        response = (
            self.supabase.table(self.table).delete().eq("id", str(message_id)).execute()
        )
        return bool(response.data)

