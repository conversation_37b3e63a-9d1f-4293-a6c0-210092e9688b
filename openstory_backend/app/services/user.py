from typing import Optional, List, Dict, Any
from datetime import datetime

from app.utils.logger import logger
from app.db.supabase import supabase_client
from app.schemas.user import User


class UserService:
    def __init__(self):
        self.table = "users"
        self.supabase = supabase_client
        logger.debug("UserService initialized")

    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get a user by their ID."""
        logger.info(f"Looking up user by ID: {user_id[:8]}...")
        try:
            response = (
                self.supabase.table(self.table).select("*").eq("id", user_id).execute()
            )

            logger.debug(f"User lookup response data: {response.data}")
            
            if response.data and len(response.data) > 0:
                logger.info(f"User found: {user_id[:8]}...")
                return User.model_validate(response.data[0])
            else:
                logger.info(f"No user found with ID: {user_id[:8]}...")
                return None
        except Exception as e:
            logger.exception(f"Error looking up user {user_id[:8]}...: {str(e)}")
            return None

    def get_user_by_email(self, email: str) -> Optional[User]:
        response = (
            self.supabase.table(self.table).select("*").eq("email", email).execute()
        )
        if response.data:
            return User(**response.data[0])
        return None

    def create_user(self, user_data: Dict[str, Any]) -> Optional[User]:
        """Create a new user in the database."""
        logger.info(f"Creating user with ID: {user_data.get('id', 'unknown')[:8]}...")
        logger.debug(f"User creation data: {user_data}")

        try:
            # First check if user already exists
            existing_user = self.get_user_by_id(user_data["id"])
            if existing_user:
                logger.info(f"User already exists: {user_data['id']}")
                return existing_user

            # Convert datetime objects to ISO format strings
            created_at = user_data.get("created_at")
            if isinstance(created_at, datetime):
                created_at = created_at.isoformat()
            elif not created_at:
                created_at = datetime.utcnow().isoformat()

            updated_at = user_data.get("updated_at")
            if isinstance(updated_at, datetime):
                updated_at = updated_at.isoformat()
            elif not updated_at:
                updated_at = datetime.utcnow().isoformat()

            # Ensure all required fields are present
            user_data_to_insert = {
                "id": user_data["id"],
                "email": user_data["email"],
                "username": user_data.get("username") or f"user_{user_data['id'][:8]}",
                "first_name": user_data.get("first_name"),
                "display_name": user_data.get("display_name"),
                "is_active": True,
                "is_superuser": False,
                "created_at": created_at,
                "updated_at": updated_at,
            }

            logger.debug(f"User data to insert: {user_data_to_insert}")

            # Try to insert the user
            response = (
                self.supabase.table(self.table).insert(user_data_to_insert).execute()
            )

            response = self.supabase.table(self.table).insert(user_data).execute()

            logger.debug(f"User creation response: {response.data}")
            logger.debug(
                f"User creation status: {getattr(response, 'status_code', 'N/A')}"
            )

            if response.data and len(response.data) > 0:
                logger.info(
                    f"User created successfully: {user_data.get('id', 'unknown')[:8]}..."
                )
                return User(**response.data[0])
            else:
                error_message = (
                    getattr(response, "error", {}).get("message", "Unknown error")
                    if hasattr(response, "error")
                    else "Unknown error"
                )
                logger.error(f"Failed to create user - {error_message}")
                return None
        except Exception as e:
            logger.exception(f"Error creating user: {str(e)}")
            return None

    def update_user(self, user_id: str, user_data: Dict[str, Any]) -> Optional[User]:
        """Update user information."""
        logger.info(f"Updating user with ID: {user_id[:8]}...")
        logger.debug(f"User update data: {user_data}")

        try:
            response = (
                self.supabase.table(self.table)
                .update(user_data)
                .eq("id", user_id)
                .execute()
            )

            logger.debug(f"User update response: {response.data}")
            logger.debug(
                f"User update status: {getattr(response, 'status_code', 'N/A')}"
            )

            if response.data and len(response.data) > 0:
                logger.info(f"User updated successfully: {user_id[:8]}...")
                return User(**response.data[0])
            else:
                logger.error(f"Failed to update user - empty response data")
                return None
        except Exception as e:
            logger.exception(f"Error updating user: {str(e)}")
            return None

    def delete_user(self, user_id: str) -> bool:
        user_id_str = str(user_id)
        response = (
            self.supabase.table(self.table).delete().eq("id", user_id_str).execute()
        )
        success = (
            getattr(response, "error", None) is None
            and getattr(response, "status_code", 500) < 300
        )
        return success

    def get_all_users(self) -> List[User]:
        """Get all users (admin only)."""
        logger.info("Retrieving all users")
        try:
            response = self.supabase.table(self.table).select("*").execute()

            logger.debug(
                f"Get all users response status: {getattr(response, 'status_code', 'N/A')}"
            )
            logger.info(f"Retrieved {len(response.data)} users")

            return [User(**user) for user in response.data]
        except Exception as e:
            logger.exception(f"Error retrieving all users: {str(e)}")
            return []
