import json
import asyncio
import os
import tempfile
from typing import Dict, Any, List, Optional
from uuid import uuid4

from llama_index.core.tools import FunctionTool
from llama_index.agent.openai import OpenAIAgent
from llama_index.llms.openai import OpenAI

from app.core.config import get_settings
from app.utils.logger import logger
from app.services.prompt_service import PromptService
from app.api.v1.llm import get_openai_response


async def get_llm_content(
    template_id: str,
    variables: Dict[str, Any],
    model: str,
    temperature: float,
    max_tokens: Optional[int] = None,
) -> Dict[str, Any]:
    """Centralized helper to get LLM content from a template"""
    prompt_service = PromptService()

    # Render the template
    rendered = prompt_service.render_template(
        template_id=template_id, variables=variables
    )

    # Prepare the messages
    messages = []
    if rendered["system_prompt"]:
        messages.append({"role": "system", "content": rendered["system_prompt"]})
    messages.append({"role": "user", "content": rendered["user_prompt"]})

    # Call OpenAI
    response = await get_openai_response(
        messages=messages,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        functions=rendered.get("functions"),
    )

    # Handle function call
    if "function_call" not in response:
        raise Exception("LLM did not return structured data")

    # Parse arguments
    fc = response["function_call"]
    if not isinstance(fc, dict) or "arguments" not in fc:
        raise Exception("Invalid function call format")

    # Return the parsed arguments
    return fc["arguments"]


class StoryAgentService:
    """LlamaIndex Agent service for story creation and video generation"""

    def __init__(self):
        self.settings = get_settings()
        self.llm = OpenAI(
            api_key=self.settings.OPENAI_API_KEY, model="gpt-4o-mini", temperature=0.7
        )
        self.agent = None
        self._initialize_agent()

    def _initialize_agent(self):
        """Initialize the agent with tools"""
        tools = [
            self._create_drama_content_tool(),
            self._create_storyboard_tool(),
            self._create_story_tool(),
        ]

        system_prompt = """
        You are OpenStory AI assistant, specialized in helping users create story content and videos. You have the following capabilities:

        1. generate_drama_content: Generate complete drama content including episodes and characters based on user's story concept
        2. generate_storyboard: Generate detailed storyboard based on storyline and character descriptions
        3. create_story: Create video based on story data

        Please intelligently select and combine these tools based on user needs to help complete story creation.
        When users request to create a complete story, you should:
        1. First use generate_drama_content to generate drama content
        2. Then use generate_storyboard to generate storyboard
        3. Finally use create_story to create video if needed
        """

        self.agent = OpenAIAgent.from_tools(
            tools=tools, llm=self.llm, system_prompt=system_prompt, verbose=True
        )

    def _create_drama_content_tool(self) -> FunctionTool:
        """Create tool for generating drama content"""

        async def generate_drama_content(
            user_story: str,
            story_type: str = "drama",
            model: str = "gpt-4o-mini",
            temperature: float = 0.7,
            max_tokens: Optional[int] = None,
        ) -> Dict[str, Any]:
            """
            Generate drama content including episodes and character information

            Args:
                user_story: User's story concept description
                story_type: Type of story, default is drama
                model: AI model to use
                temperature: Generation temperature
                max_tokens: Maximum number of tokens

            Returns:
                Drama content containing show, episodes, characters
            """
            try:
                logger.info(f"Generating drama content for: {user_story}")

                # Call existing LLM service
                data = await get_llm_content(
                    template_id="story_generator",
                    variables={
                        "user_story": user_story,
                        "story_type": story_type,
                    },
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )

                result = {
                    "show": data["story"],
                    "episodes": data.get("episodes", []),
                    "characters": data.get("characters", []),
                }

                logger.info("Drama content generated successfully")
                return result

            except Exception as e:
                logger.error(f"Error generating drama content: {str(e)}")
                return {"error": f"Failed to generate drama content: {str(e)}"}

        return FunctionTool.from_defaults(
            fn=generate_drama_content,
            name="generate_drama_content",
            description="Generate complete drama content including episodes and character information based on user story concept",
        )

    def _create_storyboard_tool(self) -> FunctionTool:
        """Create tool for generating storyboard"""

        async def generate_storyboard(
            storyline: str,
            characters: str = "[]",
            story_type: str = "drama",
            model: str = "gpt-4o",
            temperature: float = 0.7,
            max_tokens: Optional[int] = None,
        ) -> Dict[str, Any]:
            """
            Generate storyboard based on storyline and characters

            Args:
                storyline: Main storyline
                characters: JSON string of character descriptions
                story_type: Type of story
                model: AI model to use
                temperature: Generation temperature
                max_tokens: Maximum number of tokens

            Returns:
                Storyboard content containing shots
            """
            try:
                logger.info(
                    f"Generating storyboard for storyline: {storyline[:100]}..."
                )

                # Parse characters data
                try:
                    characters_data = json.loads(characters) if characters else []
                except json.JSONDecodeError:
                    characters_data = []

                # Call existing LLM service
                data = await get_llm_content(
                    template_id="storyboard_generator",
                    variables={
                        "storyline": storyline,
                        "characters": json.dumps(characters_data),
                        "story_type": story_type,
                    },
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )

                # Process shots data
                processed_shots = []
                for shot in data.get("shots", []):
                    # Process character dialogue
                    character_dialogue = []
                    if "character_dialogue" in shot:
                        for dialogue in shot["character_dialogue"]:
                            if isinstance(dialogue, dict):
                                character_dialogue.append(dialogue)

                    # Create processed shot
                    processed_shot = {
                        "id": str(uuid4()),
                        "shot_number": shot["shot_number"],
                        "shot_name": shot["shot_name"],
                        "shot_type": shot["shot_type"],
                        "shot_setting": shot["shot_setting"],
                        "shot_framing": shot["shot_framing"],
                        "shot_angle": shot["shot_angle"],
                        "shot_movement": shot["shot_movement"],
                        "character_composition": shot.get("character_composition", {}),
                        "character_dialogue": character_dialogue,
                        "characters": shot.get("characters", []),
                    }
                    processed_shots.append(processed_shot)

                result = {"shots": processed_shots}
                logger.info(
                    f"Storyboard generated successfully with {len(processed_shots)} shots"
                )
                return result

            except Exception as e:
                logger.error(f"Error generating storyboard: {str(e)}")
                return {"error": f"Failed to generate storyboard: {str(e)}"}

        return FunctionTool.from_defaults(
            fn=generate_storyboard,
            name="generate_storyboard",
            description="Generate detailed storyboard with shot information based on storyline and character descriptions",
        )

    def _create_story_tool(self) -> FunctionTool:
        """Create tool for creating story video"""

        async def create_story(
            title: str = "Generated Story", frames: str = "[]"
        ) -> Dict[str, Any]:
            """
            Create video based on story data

            Args:
                title: Story title
                frames: JSON format frames data

            Returns:
                Video generation result
            """
            try:
                logger.info(f"Creating story video with title: {title}")

                # Parse frames data
                try:
                    frames_data = json.loads(frames) if frames else []
                except json.JSONDecodeError:
                    frames_data = []

                # Should call actual video generation service here
                # Return mock result since video_service needs specific data structure
                result = {
                    "success": True,
                    "task_status": "pending",
                    "request_id": str(uuid4()),
                    "message": f"Story video '{title}' creation task submitted, processing...",
                    "frames_count": len(frames_data),
                }

                logger.info("Story video creation task submitted")
                return result

            except Exception as e:
                logger.error(f"Error creating story: {str(e)}")
                return {"error": f"Failed to create story video: {str(e)}"}

        return FunctionTool.from_defaults(
            fn=create_story,
            name="create_story",
            description="Create video based on story data, requires complete information including frames",
        )

    async def chat(self, message: str) -> str:
        """Chat with the agent"""
        try:
            logger.info(f"Agent received message: {message}")
            response = await self.agent.achat(message)
            return str(response)
        except Exception as e:
            logger.error(f"Agent chat error: {str(e)}")
            return f"Sorry, an error occurred while processing your request: {str(e)}"

    async def interactive_chat(self, message: str, session_id: str = "default"):
        """Interactive chat with tool confirmation"""
        try:
            logger.info(f"Agent received interactive message: {message}")

            # For story creation requests, return confirmation request
            if "story" in message.lower() and "create" in message.lower():
                return {
                    "type": "confirmation_request",
                    "message": f"I can help you create a complete story! This will involve 3 steps:\n\n1. **Generate Drama Content** - Create the story plot, characters, and synopsis\n2. **Generate Storyboard** - Create detailed shot-by-shot storyboard\n3. **Create Video** - Generate the final video\n\nWould you like me to start with step 1: Generate Drama Content?\n\n*Reply with 'yes' to proceed or 'no' to cancel.*",
                    "confirmation_id": f"{session_id}_step1",
                }

            # Check if this is a confirmation response
            elif message.lower() in ["yes", "y", "confirm", "proceed", "ok"]:
                # Execute the story creation workflow directly
                return await self._execute_story_workflow(session_id)
            elif message.lower() in ["no", "n", "cancel", "skip"]:
                return {
                    "type": "response",
                    "message": "Operation cancelled. How else can I help you?",
                }

            # For other messages, use regular chat
            else:
                response = await self.agent.achat(message)
                return {"type": "response", "message": str(response)}

        except Exception as e:
            logger.error(f"Agent interactive chat error: {str(e)}")
            return {
                "type": "response",
                "message": f"Sorry, an error occurred while processing your request: {str(e)}",
            }

    async def _execute_story_workflow(self, session_id: str):
        """Execute the complete story creation workflow"""
        try:
            # Step 1: Generate Drama Content
            story_concept = "A brave knight on an adventure"  # Default concept

            data = await get_llm_content(
                template_id="story_generator",
                variables={
                    "user_story": story_concept,
                    "story_type": "adventure",
                },
                model="gpt-4o-mini",
                temperature=0.7,
                max_tokens=None,
            )

            drama_result = {
                "show": data["story"],
                "episodes": data.get("episodes", []),
                "characters": data.get("characters", []),
            }

            # Step 2: Generate Storyboard
            storyline = drama_result.get("show", {}).get("synopsis", "")
            characters = drama_result.get("characters", [])

            storyboard_data = await get_llm_content(
                template_id="storyboard_generator",
                variables={
                    "storyline": storyline,
                    "characters": json.dumps(characters),
                    "story_type": "adventure",
                },
                model="gpt-4o",
                temperature=0.7,
                max_tokens=None,
            )

            # Process shots data
            processed_shots = []
            for shot in storyboard_data.get("shots", []):
                processed_shot = {
                    "id": str(uuid4()),
                    "shot_number": shot["shot_number"],
                    "shot_name": shot["shot_name"],
                    "shot_type": shot["shot_type"],
                    "shot_setting": shot["shot_setting"],
                    "shot_framing": shot["shot_framing"],
                    "shot_angle": shot["shot_angle"],
                    "shot_movement": shot["shot_movement"],
                    "character_composition": shot.get("character_composition", {}),
                    "character_dialogue": shot.get("character_dialogue", []),
                    "characters": shot.get("characters", []),
                }
                processed_shots.append(processed_shot)

            storyboard_result = {"shots": processed_shots}

            # Step 3: Create Video (simulated)
            title = drama_result.get("show", {}).get("title", "Generated Story")

            final_result = {
                "success": True,
                "task_status": "completed",
                "request_id": str(uuid4()),
                "title": title,
                "drama_content": drama_result,
                "storyboard": storyboard_result,
                "frames_count": len(processed_shots),
            }

            response_message = f"""✅ **Complete Story Created Successfully!**

**📖 Story Details:**
- **Title:** {title}
- **Characters:** {len(drama_result['characters'])} main characters
- **Synopsis:** {drama_result['show']['synopsis'][:200]}...

**🎬 Storyboard:**
- **Total Shots:** {len(processed_shots)} detailed scenes
- **Shot Examples:**
{chr(10).join([f"  - {shot['shot_name']}: {shot['shot_setting'][:80]}..." for shot in processed_shots[:3]])}

**🎥 Video Creation:**
- **Status:** Ready for production
- **Request ID:** {final_result['request_id']}

Your complete story has been generated with all components ready!"""

            return {"type": "response", "message": response_message}

        except Exception as e:
            logger.error(f"Error executing story workflow: {str(e)}")
            return {"type": "response", "message": f"Error creating story: {str(e)}"}

    async def stream_chat(self, message: str):
        """Stream chat with the agent"""
        try:
            logger.info(f"Agent received streaming message: {message}")

            # For now, use regular chat and simulate streaming
            # LlamaIndex OpenAIAgent streaming might need different setup
            response = await self.agent.achat(message)
            response_text = str(response)

            # Simulate streaming by yielding chunks
            chunk_size = 50
            for i in range(0, len(response_text), chunk_size):
                chunk = response_text[i : i + chunk_size]
                yield chunk
                # Small delay to simulate streaming
                await asyncio.sleep(0.1)

        except Exception as e:
            logger.error(f"Agent stream chat error: {str(e)}")
            yield f"Sorry, an error occurred while processing your request: {str(e)}"


# Global agent instance
story_agent_service = StoryAgentService()
