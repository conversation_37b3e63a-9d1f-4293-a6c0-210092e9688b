import json
import async<PERSON>
from typing import Dict, Any, List, Optional
from uuid import uuid4

from llama_index.core.tools import FunctionTool
from llama_index.agent.openai import OpenAIAgent
from llama_index.llms.openai import OpenAI

from app.core.config import get_settings
from app.utils.logger import logger
from app.services.prompt_service import PromptService
from app.api.v1.llm import get_openai_response


async def get_llm_content(
    template_id: str,
    variables: Dict[str, Any],
    model: str,
    temperature: float,
    max_tokens: Optional[int] = None,
) -> Dict[str, Any]:
    """Centralized helper to get LLM content from a template"""
    prompt_service = PromptService()

    # Render the template
    rendered = prompt_service.render_template(
        template_id=template_id, variables=variables
    )

    # Prepare the messages
    messages = []
    if rendered["system_prompt"]:
        messages.append({"role": "system", "content": rendered["system_prompt"]})
    messages.append({"role": "user", "content": rendered["user_prompt"]})

    # Call OpenAI
    response = await get_openai_response(
        messages=messages,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        functions=rendered.get("functions"),
    )

    # Handle function call
    if "function_call" not in response:
        raise Exception("LLM did not return structured data")

    # Parse arguments
    fc = response["function_call"]
    if not isinstance(fc, dict) or "arguments" not in fc:
        raise Exception("Invalid function call format")

    # Return the parsed arguments
    return fc["arguments"]


class StoryAgentService:
    """LlamaIndex Agent service for story creation and video generation"""

    def __init__(self):
        self.settings = get_settings()
        self.llm = OpenAI(
            api_key=self.settings.OPENAI_API_KEY, model="gpt-4o-mini", temperature=0.7
        )
        self.agent = None
        self._initialize_agent()

    def _initialize_agent(self):
        """Initialize the agent with tools"""
        tools = [
            self._create_drama_content_tool(),
            self._create_storyboard_tool(),
            self._create_story_tool(),
        ]

        system_prompt = """
        你是OpenStory AI助手，专门帮助用户创建故事内容和视频。你有以下能力：

        1. generate_drama_content: 根据用户的故事概念生成完整的剧本内容，包括剧集、角色等
        2. generate_storyboard: 根据故事线和角色描述生成详细的故事板
        3. create_story: 根据故事数据创建视频

        请根据用户的需求，智能地选择和组合使用这些工具来帮助用户完成故事创作。
        当用户要求创建完整的故事时，你应该：
        1. 首先使用generate_drama_content生成剧本内容
        2. 然后使用generate_storyboard生成故事板
        3. 最后可以使用create_story创建视频

        请用中文回复用户。
        """

        self.agent = OpenAIAgent.from_tools(
            tools=tools, llm=self.llm, system_prompt=system_prompt, verbose=True
        )

    def _create_drama_content_tool(self) -> FunctionTool:
        """Create tool for generating drama content"""

        async def generate_drama_content(
            user_story: str,
            story_type: str = "drama",
            model: str = "gpt-4o-mini",
            temperature: float = 0.7,
            max_tokens: Optional[int] = None,
        ) -> Dict[str, Any]:
            """
            生成剧本内容，包括剧集、角色等信息

            Args:
                user_story: 用户的故事概念描述
                story_type: 故事类型，默认为drama
                model: 使用的AI模型
                temperature: 生成温度
                max_tokens: 最大token数

            Returns:
                包含show、episodes、characters的剧本内容
            """
            try:
                logger.info(f"Generating drama content for: {user_story}")

                # 调用现有的LLM服务
                data = await get_llm_content(
                    template_id="story_generator",
                    variables={
                        "user_story": user_story,
                        "story_type": story_type,
                    },
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )

                result = {
                    "show": data["story"],
                    "episodes": data.get("episodes", []),
                    "characters": data.get("characters", []),
                }

                logger.info("Drama content generated successfully")
                return result

            except Exception as e:
                logger.error(f"Error generating drama content: {str(e)}")
                return {"error": f"生成剧本内容失败: {str(e)}"}

        return FunctionTool.from_defaults(
            fn=generate_drama_content,
            name="generate_drama_content",
            description="根据用户故事概念生成完整的剧本内容，包括剧集和角色信息",
        )

    def _create_storyboard_tool(self) -> FunctionTool:
        """Create tool for generating storyboard"""

        async def generate_storyboard(
            storyline: str,
            characters: List[Dict[str, Any]],
            story_type: str = "drama",
            model: str = "gpt-4o",
            temperature: float = 0.7,
            max_tokens: Optional[int] = None,
        ) -> Dict[str, Any]:
            """
            根据故事线和角色生成故事板

            Args:
                storyline: 主要故事线
                characters: 角色描述列表
                story_type: 故事类型
                model: 使用的AI模型
                temperature: 生成温度
                max_tokens: 最大token数

            Returns:
                包含shots的故事板内容
            """
            try:
                logger.info(
                    f"Generating storyboard for storyline: {storyline[:100]}..."
                )

                # 调用现有的LLM服务
                data = await get_llm_content(
                    template_id="storyboard_generator",
                    variables={
                        "storyline": storyline,
                        "characters": json.dumps(characters),
                        "story_type": story_type,
                    },
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens,
                )

                # 处理shots数据
                processed_shots = []
                for shot in data.get("shots", []):
                    # 处理角色对话
                    character_dialogue = []
                    if "character_dialogue" in shot:
                        for dialogue in shot["character_dialogue"]:
                            if isinstance(dialogue, dict):
                                character_dialogue.append(dialogue)

                    # 创建处理后的shot
                    processed_shot = {
                        "id": str(uuid4()),
                        "shot_number": shot["shot_number"],
                        "shot_name": shot["shot_name"],
                        "shot_type": shot["shot_type"],
                        "shot_setting": shot["shot_setting"],
                        "shot_framing": shot["shot_framing"],
                        "shot_angle": shot["shot_angle"],
                        "shot_movement": shot["shot_movement"],
                        "character_composition": shot.get("character_composition", {}),
                        "character_dialogue": character_dialogue,
                        "characters": shot.get("characters", []),
                    }
                    processed_shots.append(processed_shot)

                result = {"shots": processed_shots}
                logger.info(
                    f"Storyboard generated successfully with {len(processed_shots)} shots"
                )
                return result

            except Exception as e:
                logger.error(f"Error generating storyboard: {str(e)}")
                return {"error": f"生成故事板失败: {str(e)}"}

        return FunctionTool.from_defaults(
            fn=generate_storyboard,
            name="generate_storyboard",
            description="根据故事线和角色描述生成详细的故事板，包含镜头信息",
        )

    def _create_story_tool(self) -> FunctionTool:
        """Create tool for creating story video"""

        async def create_story(
            title: str = "Generated Story", frames: str = "[]"
        ) -> Dict[str, Any]:
            """
            根据故事数据创建视频

            Args:
                title: 故事标题
                frames: JSON格式的frames数据

            Returns:
                视频生成结果
            """
            try:
                logger.info(f"Creating story video with title: {title}")

                # 解析frames数据
                try:
                    frames_data = json.loads(frames) if frames else []
                except json.JSONDecodeError:
                    frames_data = []

                # 这里应该调用实际的视频生成服务
                # 由于video_service需要特定的数据结构，这里返回模拟结果
                result = {
                    "success": True,
                    "task_status": "pending",
                    "request_id": str(uuid4()),
                    "message": f"故事视频 '{title}' 创建任务已提交，正在处理中...",
                    "frames_count": len(frames_data),
                }

                logger.info("Story video creation task submitted")
                return result

            except Exception as e:
                logger.error(f"Error creating story: {str(e)}")
                return {"error": f"创建故事视频失败: {str(e)}"}

        return FunctionTool.from_defaults(
            fn=create_story,
            name="create_story",
            description="根据故事数据创建视频，需要包含frames等完整信息",
        )

    async def chat(self, message: str) -> str:
        """Chat with the agent"""
        try:
            logger.info(f"Agent received message: {message}")
            response = await self.agent.achat(message)
            return str(response)
        except Exception as e:
            logger.error(f"Agent chat error: {str(e)}")
            return f"抱歉，处理您的请求时出现错误: {str(e)}"

    async def stream_chat(self, message: str):
        """Stream chat with the agent"""
        try:
            logger.info(f"Agent received streaming message: {message}")
            response = await self.agent.astream_chat(message)
            async for token in response.response_gen:
                yield token
        except Exception as e:
            logger.error(f"Agent stream chat error: {str(e)}")
            yield f"抱歉，处理您的请求时出现错误: {str(e)}"


# 全局agent实例
story_agent_service = StoryAgentService()
