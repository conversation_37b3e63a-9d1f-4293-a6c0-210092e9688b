# Services Module Organization

This directory contains service modules that implement business logic and database operations for the Drama Backend application.

## Service Structure

Services are organized into logical groupings based on their functionality:

### Video Production Services (`video_production.py`)
- **ShotService**: Manages individual shots within episodes
- **ShotComponentService**: Handles components within shots

### Character Services (`character_services.py`)
- **CharacterService**: Manages character entities
- **CharacterProgressionService**: Handles character development through episodes
- **Character Prompt Generation**: Functions for generating prompts for character visualization
- **Image Style Definitions**: Style templates for character visualization

### Other Services
- **Authentication Services**: User authentication and authorization
- **LLM Services**: Large Language Model integration
- **Image Generation Services**: AI image generation services
- **Job Services**: Background job processing
- **Session Services**: User session and messaging management

## Usage Guidelines

1. Use the appropriate consolidated service file when importing services
2. Keep related business logic within the same service file
3. When adding new functionality, add it to the appropriate service file based on its domain

## Import Examples

```python

# Import character services
from app.services.character_services import CharacterService, CharacterProgressionService
``` 