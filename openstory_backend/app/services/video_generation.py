import os
import time
import tempfile
import aiohttp
import asyncio
import fal_client

from datetime import datetime
from pathlib import Path
from pydantic import BaseModel
from runwayml import RunwayML
from typing import List, Dict, Any, Optional
from asyncio.subprocess import PIPE

from app.utils.logger import logger
from app.core.config import get_settings
from app.db.supabase import BucketEnum, StorageService
from app.services.media_service import (
    TextToImageRequest,
    ImageToVideoRequest,
    runway_service,
    fal_service,
)
from app.services.story_service import StoryService


settings = get_settings()
BGM_WITH_AUDIO_TRACK = 'ffmpeg -i {input} -i {bgm} -filter_complex "[1:a]volume=0.1[a1];[0:a][a1]amix=inputs=2:duration=first[a]" -map 0:v -map "[a]" -c:v copy -c:a aac -shortest {output} -y'
BGM_WITHOUT_AUDIO_TRACK = 'ffmpeg -i {input} -i {bgm} -filter_complex "[1:a]volume=0.1[a]" -map 0:v -map "[a]" -c:v copy -c:a aac -shortest {output} -y'


class StoryConfig(BaseModel):
    """Configuration for story video generation"""

    concept: str
    movie_style: Dict[str, Any]
    storyLine: str
    characters: List[Dict[str, Any]]
    frames: List[Dict[str, Any]]
    user_id: str


class VideoGenerationConfig(BaseModel):
    """Configuration for video generation"""

    shot_name: str
    shot_type: str
    shot_setting: str
    shot_framing: str
    shot_angle: str
    shot_movement: str
    character_composition: str
    character_dialogue: str
    characters: List[Dict[str, Any]]
    request_id: str | None = None
    prompt_image: str | None = None
    user_id: str
    movie_style: str


class VideoGenerationResult(BaseModel):
    """Result of video generation"""

    success: bool
    task_status: Optional[str] = None
    request_id: Optional[str] = None
    video_url: Optional[str] = None
    error_message: Optional[str] = None

    @staticmethod
    def fail(error_message: str):
        return VideoGenerationResult(error_message=error_message, success=False)


class FalVideoGenerationService:
    def __init__(self):
        self.fal_client = fal_client
        self.audio_storage = StorageService(BucketEnum.AUDIO)
        self.storage_service = StorageService(BucketEnum.VIDEO)
        self.runway_client = RunwayML(api_key=os.getenv("RUNWAYML_API_SECRET"))
        self.image_model_id = "fal-ai/fast-sdxl"
        self.video_model_id = "fal-ai/hidream-i1-fast"
        self.temp_dir = Path(tempfile.gettempdir()) / "video_generation"
        self.temp_dir.mkdir(exist_ok=True)
        self.story_service = StoryService()

    async def _call_runway_api(
        self, endpoint: str, payload: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Make API call to RunwayML"""
        headers = {
            "Authorization": f"Bearer {self.runway_api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "X-API-Version": "1.0",
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.runway_api_url}/{endpoint}", headers=headers, json=payload
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"RunwayML API error: {error_text}")
                return await response.json()

    async def text_to_video(
        self,
        prompt: str,
        negative_prompt: str,
        num_frames: int,
        num_inference_steps: int = 50,
        guidance_scale: float = 7.5,
        width: int = 1024,
        height: int = 576,
        fps: int = 24,
    ) -> str:
        """Generate video from text using RunwayML API"""
        payload = {
            "model": "runwayml/text-to-video",
            "prompt": prompt,
            "negative_prompt": negative_prompt,
            "num_frames": num_frames,
            "num_inference_steps": num_inference_steps,
            "guidance_scale": guidance_scale,
            "width": width,
            "height": height,
            "fps": fps,
            "seed": None,  # 让 API 自动生成随机种子
        }

        try:
            # 提交任务
            result = await self._call_runway_api("text-to-video", payload)
            task_id = result.get("id")

            if not task_id:
                raise Exception("No task ID returned from RunwayML API")

            # 轮询任务状态
            while True:
                status_result = await self._call_runway_api(
                    f"text-to-video/{task_id}", {}
                )
                status = status_result.get("status")

                if status == "completed":
                    return status_result.get("video_url")
                elif status == "failed":
                    raise Exception(f"Task failed: {status_result.get('error')}")

                await asyncio.sleep(1)  # 等待1秒后再次检查

        except Exception as e:
            logger.error(f"Error in text-to-video generation: {str(e)}")
            raise

    def _build_prompt(self, config: VideoGenerationConfig) -> str:
        """Build the prompt for video generation based on shot parameters"""
        # add shot type
        base_prompt = f"A {config.shot_type} shot of {config.shot_setting}. "

        # add framing
        framing_prompt = f"The shot is {config.shot_framing}, "
        angle_prompt = f"from {config.shot_angle} angle, "
        movement_prompt = f"with {config.shot_movement} movement. "

        # add character description
        character_prompts = []
        for character in config.characters:
            name = character.get("name", "Unknown character")
            descriptor = character.get("descriptor", "")
            char_prompt = f"{name}"
            if descriptor:
                char_prompt += f" ({descriptor})"
            character_prompts.append(char_prompt)

        characters_prompt = " and ".join(character_prompts)

        # add all prompts
        final_prompt = (
            f"{base_prompt}{framing_prompt}{angle_prompt}{movement_prompt}"
            f"Characters in the scene: {characters_prompt}. "
            f"Scene composition: {config.character_composition}. "
            f"Dialogue: {config.character_dialogue}. "
            f"Shot style: {config.movie_style}. "
        )

        return final_prompt

    def _build_negative_prompt(self) -> str:
        """Build negative prompt to avoid unwanted elements"""
        return "blurry, low quality, distorted, unrealistic, cartoon, animation, poor lighting, bad composition"

    def generate_frame(self, config: VideoGenerationConfig) -> VideoGenerationResult:
        """
        Generate a frame based on the provided configuration.
        If shot_type is 'setting_only', use image model, otherwise use video model.
        """
        logger.error(f"config: {config}")
        try:
            # create prompt
            prompt = self._build_prompt(config)
            negative_prompt = self._build_negative_prompt()

            # prepare character images
            character_images = []
            for character in config.characters:
                if "avatar_urls" in character and "url" in character["avatar_urls"]:
                    character_images.append(character["avatar_urls"]["url"])

            # choose model
            model_id = (
                self.image_model_id
                if config.shot_type == "setting_only"
                else self.video_model_id
            )

            # call fal.ai API
            response = self.fal_client.run(
                model_id,
                {
                    "prompt": prompt,
                    "negative_prompt": negative_prompt,
                    "character_images": character_images,
                    "num_frames": 24,
                    "fps": 24,
                    "width": 1024,
                    "height": 576,
                },
            )
            logger.error("response: %s", response)

            # deal with response
            if response and "images" in response and response["images"]:
                return VideoGenerationResult(
                    success=True, video_url=response["images"][0]["url"], type="image"
                )
            else:
                return VideoGenerationResult(
                    success=False, error_message="Failed to generate frame"
                )

        except Exception as e:
            logger.error(f"Error generating frame: {str(e)}")
            return VideoGenerationResult(success=False, error_message=str(e))

    async def generate_frame_image(
        self, config: VideoGenerationConfig
    ) -> VideoGenerationResult:
        try:
            # create prompt
            prompt = self._build_prompt(config)

            # prepare character images
            character_images = []
            for character in config.characters:
                if "avatar_urls" in character and "url" in character["avatar_urls"]:
                    character_images.append(
                        {
                            "tag": character["tag"],
                            "uri": character["avatar_urls"]["url"],
                        }
                    )

            # text to image
            image_url = await runway_service.text_to_image(
                TextToImageRequest(
                    prompt_text=prompt,
                    reference_images=character_images,
                )
            )
            return VideoGenerationResult(success=True, video_url=image_url)
        except Exception as e:
            logger.error(f"Error generating frame image: {str(e)}")
            return VideoGenerationResult(
                success=False, error_message="Failed to generate frame image"
            )

    async def generate_frame_video(
        self, config: VideoGenerationConfig
    ) -> VideoGenerationResult:
        try:
            # check task status if request_id exists
            if config.request_id is not None:
                status = await fal_service.get_task_status(config.request_id)
                if status != "Completed":
                    return VideoGenerationResult(
                        success=True,
                        task_status=status,
                        request_id=config.request_id,
                    )

                # get task result
                result = await fal_service.get_task_result(config.request_id)
                return VideoGenerationResult(
                    success=True,
                    task_status=status,
                    request_id=config.request_id,
                    video_url=result.video_url,
                )

            assert config.prompt_image is not None
            request_id = await fal_service.submit_task(
                ImageToVideoRequest(
                    model="fal-ai/kling-video/v1/standard/image-to-video",
                    prompt_text=self._build_prompt(config),
                    prompt_image=config.prompt_image,
                )
            )
            return VideoGenerationResult(
                success=True,
                task_status="InProgress",
                request_id=request_id,
            )

        except Exception as e:
            logger.error(f"Error generating frame video: {str(e)}")
            return VideoGenerationResult(
                success=False, error_message="Failed to generate frame video"
            )

    def _build_character_prompt(self, character: Dict[str, Any]) -> str:
        """Build detailed character description for the prompt"""
        prompts = []

        # Basic info
        prompts.append(f"{character['name']}")

        # Physical appearance
        if character.get("appearance"):
            prompts.append(f"appearance: {character['appearance']}")
        if character.get("gender"):
            prompts.append(f"gender: {character['gender']}")
        if character.get("age"):
            prompts.append(f"age: {character['age']}")

        # Personality and background
        if character.get("personality"):
            prompts.append(f"personality: {character['personality']}")
        if character.get("backstory"):
            prompts.append(f"background: {character['backstory']}")
        if character.get("oneliner"):
            prompts.append(f"description: {character['oneliner']}")

        return ", ".join(prompts)

    def _build_frame_prompt(self, frame: Dict[str, Any]) -> str:
        """Build detailed prompt for a single frame"""
        prompts = []

        # Shot type and setting
        prompts.append(f"A {frame['shot_type']} shot of {frame['shot_setting']}")

        # MoiveStyle and setting
        prompts.append(f"the frame movie style is {frame['movie_style']}")

        # Camera details
        prompts.append(
            f"Camera: {frame['shot_framing']} framing from {frame['shot_angle']} angle with {frame['shot_movement']} movement"
        )

        # Character descriptions
        if frame.get("characters"):
            character_prompts = [
                self._build_character_prompt(char) for char in frame["characters"]
            ]
            prompts.append(f"Characters in scene: {', '.join(character_prompts)}")

        return ". ".join(prompts)

    def _build_story_prompt(self, story: Dict[str, Any]) -> str:
        """Build a concise story prompt that fits within character limit"""
        # make sure concept and story_line are not empty
        concept = story.get("concept", "")
        story_line = story.get("storyLine", "")

        # get current frame information
        current_frame = story.get("frames", [{}])[0]  # use the first frame
        shot_type = current_frame.get("shot_type", "")
        shot_setting = current_frame.get("shot_setting", "")

        # build concise prompt
        prompt_parts = []

        # add scene description
        if shot_type and shot_setting:
            prompt_parts.append(f"A {shot_type} shot of {shot_setting}")

        # add story concept (if short)
        if concept and len(concept) < 100:
            prompt_parts.append(f"Story: {concept}")

        # add story line (if short)
        if story_line and len(story_line) < 200:
            prompt_parts.append(f"Scene: {story_line}")

        # combine prompts, ensure not longer than 1000 characters
        final_prompt = ". ".join(prompt_parts)
        if len(final_prompt) > 1000:
            # if still too long, only keep scene description
            final_prompt = f"A {shot_type} shot of {shot_setting}"

        return final_prompt

    async def generate_story_video(
        self, story: Dict[str, Any]
    ) -> VideoGenerationResult:
        """Generate a complete story video using fal.ai kling model"""
        try:
            start_time = time.time()
            frames = story.get("frames", [])
            if not frames:
                return VideoGenerationResult.fail("No frames found in story")

            # Save story first
            try:
                # Extract user_id from story and create the correct data structure
                user_id = story.get("user_id")
                story_data = {
                    "story": story,
                    "user_id": user_id
                }
                logger.error(f"story_data-------: {story}")
                story_id = self.story_service.save_story(story_data)
                if not story_id:
                    return VideoGenerationResult.fail("Failed to save story")
            except Exception as e:
                logger.error(f"Error saving story: {str(e)}")
                return VideoGenerationResult.fail(f"Failed to save story: {str(e)}")

            video_tasks = []
            for frame in frames:
                # prepare image url
                image_url = frame.get("image_url")
                character_dialogue = frame.get("character_dialogue")
                if not image_url or not image_url.startswith("https://"):
                    logger.error(f"Invalid image URL for frame {frame.get('id')}")
                    continue

                logger.debug(f"Frame data: {frame}")
                logger.debug(f"Frame movie_style: {frame.get('movie_style')}")
                logger.debug(f"Story movie_style: {story.get('movie_style')}")

                # construct generation config
                config = VideoGenerationConfig(
                    shot_name=frame.get("shot_name", ""),
                    shot_type=frame.get("shot_type", ""),
                    shot_setting=frame.get("shot_setting", ""),
                    shot_framing=frame.get("shot_framing", ""),
                    shot_angle=frame.get("shot_angle", ""),
                    shot_movement=frame.get("shot_movement", ""),
                    character_composition=frame.get("character_composition", ""),
                    character_dialogue=frame.get("character_dialogue", ""),
                    characters=frame.get("characters", []),
                    prompt_image=image_url,
                    user_id=story.get("user_id", ""),
                    movie_style=frame.get(
                        "movie_style", "Realistic"
                    ),  # Default to Realistic if not specified
                )

                # prepare tasks
                video_tasks.append(
                    fal_service.run_task(
                        ImageToVideoRequest(
                            model="fal-ai/kling-video/v1/standard/image-to-video",
                            prompt_text=self._build_prompt(config),
                            prompt_image=image_url,
                            dialogue=character_dialogue,
                        )
                    )
                )

            # gather task result
            video_urls = []
            task_results = await asyncio.gather(*video_tasks, return_exceptions=True)
            for task_result in task_results:
                if isinstance(task_result, BaseException):
                    logger.error(f"task failed with error: {task_result}")
                    return VideoGenerationResult.fail("generation failed")
                video_urls.append(task_result.video_url)

            # if there is only one video, return directly
            if len(video_urls) == 1:
                return VideoGenerationResult(
                    success=True,
                    video_url=video_urls[0],
                )
            # download all videos
            video_paths = []
            for i, url in enumerate(video_urls):
                video_path = self.temp_dir / f"video_{i}.mp4"
                if await self.download_file(url, video_path):
                    video_paths.append(video_path)
                    logger.info(
                        f"Successfully downloaded video {i + 1}/{len(video_urls)}"
                    )

            # concatenate videos and add bgm
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            final_video = self.temp_dir / f"{timestamp}_final_video.mp4"

            if not video_paths:
                return VideoGenerationResult.fail("Failed to download any videos")
            if not await self.concatenate_videos(video_paths, final_video):
                return VideoGenerationResult.fail("Failed to concatenate videos")

            # download bgm
            bgm_name = story["bgm"]
            if bgm_name is not None:
                bgm_name = bgm_name.replace(" ", "_")
                bgm_url = self.audio_storage.get_signed_url(f"bgm/{bgm_name}.mp3")
                bgm = self.temp_dir / f"{timestamp}_bgm.mp3"
                await self.download_file(bgm_url, bgm)

                temp_video = self.temp_dir / f"{timestamp}_temp_video.mp4"
                os.rename(final_video, temp_video)
                if not await self.add_video_bgm(bgm, temp_video, final_video):
                    return VideoGenerationResult.fail("Failed to add video bgm")

            # upload file to storage
            logger.info("Successfully concatenated all videos")
            file_content = None
            with open(final_video, "rb") as file:
                file_content = file.read()

            final_video_url = await self.storage_service.upload_file(
                final_video.name,
                file_content,
                "video/mp4",
            )
            time_cost = time.time() - start_time
            result = VideoGenerationResult(success=True, video_url=final_video_url)
            logger.info(f"generate story done, result={result} cost {time_cost}s")
            return result

        except Exception as e:
            logger.error(f"Error generating story video: {str(e)}")
            return VideoGenerationResult.fail(f"Failed to generate story video: {e}")

    async def download_file(self, url: str, output_path: Path) -> bool:
        """Download image from URL"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        logger.error(
                            f"download file fail, url: {url}, response: {response}"
                        )
                    if response.status == 200:
                        with open(output_path, "wb") as f:
                            f.write(await response.read())
                        return True
            return False
        except Exception as e:
            logger.error(f"Error downloading file: {str(e)}")
            return False

    async def convert_image_to_video(
        self, image_path: Path, output_path: Path, duration: int = 3
    ) -> bool:
        """Convert single image to video using FFmpeg"""
        try:
            cmd = [
                "ffmpeg",
                "-y",
                "-loop",
                "1",
                "-i",
                str(image_path),
                "-c:v",
                "libx264",
                "-t",
                str(duration),
                "-pix_fmt",
                "yuv420p",
                str(output_path),
            ]
            process = await asyncio.create_subprocess_exec(
                *cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            return process.returncode == 0
        except Exception as e:
            logger.error(f"Error converting image to video: {str(e)}")
            return False

    async def concatenate_videos(
        self, video_paths: List[Path], output_path: Path
    ) -> bool:
        """Concatenate multiple videos using FFmpeg"""
        try:
            # Create a file list for FFmpeg
            list_path = self.temp_dir / "video_list.txt"
            with open(list_path, "w") as f:
                for video_path in video_paths:
                    f.write(f"file '{video_path}'\n")

            # Concatenate videos
            cmd = [
                "ffmpeg",
                "-y",
                "-f",
                "concat",
                "-safe",
                "0",
                "-i",
                str(list_path),
                "-c",
                "copy",
                str(output_path),
            ]
            process = await asyncio.create_subprocess_exec(
                *cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE
            )
            await process.communicate()
            return process.returncode == 0
        except Exception as e:
            logger.error(f"Error concatenating videos: {str(e)}")
            return False

    async def has_audio_track(self, video_path: str) -> bool:
        """check if video has audio track

        Args:
            video_path: path to video

        Returns:
            has audio track or not
        """
        cmd = f"ffprobe -show_streams {video_path}"
        logger.debug(f"has_audio_track, cmd: {cmd}")
        process = await asyncio.create_subprocess_shell(cmd, stdout=PIPE, stderr=PIPE)
        stdout, stderr = await process.communicate()
        if process.returncode != 0:
            logger.error(f"ffprobe, stdout: {stdout}, stderr = {stderr}")
            raise Exception("check audio track failed")
        return "codec_type=audio" in str(stdout)

    async def add_video_bgm(
        self,
        bgm: Path,
        input_video: Path,
        output_video: Path,
    ) -> bool:
        """add bgm to video

        Args:
            bgm: background music path
            input_video: input video path
            output_video: output video path

        Returns:
            success/fail
        """
        with_audio_track = await self.has_audio_track(str(input_video))
        cmd = BGM_WITH_AUDIO_TRACK if with_audio_track else BGM_WITHOUT_AUDIO_TRACK
        cmd = cmd.format(bgm=str(bgm), input=str(input_video), output=str(output_video))
        logger.debug(f"add_video_bgm, cmd: {cmd}")

        process = await asyncio.create_subprocess_shell(cmd, stdout=PIPE, stderr=PIPE)
        stdout, stderr = await process.communicate()
        if process.returncode != 0:
            logger.error(f"ffmpeg stdout: {stdout}, stderr: {stderr}")
            raise Exception("add video bgm failed")
        return process.returncode == 0


video_service = FalVideoGenerationService()
