from typing import List, Optional, Any
from uuid import UUID
from fastapi import HTT<PERSON>Exception
from datetime import datetime

from app.utils.logger import logger
from app.db.supabase import supabase_client

from pydantic import BaseModel, Field, validator
from typing import Dict, Any, List, Optional, Union
import json


class AvatarUrls(BaseModel):
    url: str


class Character(BaseModel):
    name: str
    gender: Optional[str] = None
    age: Optional[Union[int, str]] = None
    appearance: Optional[str] = None
    personality: Optional[str] = None
    backstory: Optional[str] = None
    oneliner: Optional[str] = None
    voice_id: Optional[str] = None
    id: Optional[str] = None
    creator_user_id: Optional[str] = None
    avatar_urls: Optional[AvatarUrls] = None
    preview_img: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    is_public: Optional[bool] = None
    voice: Optional[Any] = None
    voice_name: Optional[str] = None

    class Config:
        extra = "allow"


class Frame(BaseModel):
    id: str
    shot_name: str
    shot_type: str
    shot_setting: str
    shot_framing: str
    shot_angle: str
    shot_movement: str
    characters: Optional[List[Dict[str, Any]]] = []
    character_composition: Optional[str] = "{}"
    character_dialogue: Optional[str] = ""
    image_url: Optional[str] = None
    status: str = "pending"
    shot_number: Optional[int] = None

    @validator("character_composition", pre=True)
    def parse_character_composition(cls, v):
        if isinstance(v, dict):
            return json.dumps(v)
        if isinstance(v, str):
            try:
                # 验证是否是有效的 JSON
                json.loads(v)
                return v
            except json.JSONDecodeError:
                return "{}"
        return "{}"

    @validator("character_dialogue", pre=True)
    def parse_character_dialogue(cls, v):
        if isinstance(v, dict):
            return json.dumps(v)
        if isinstance(v, str):
            if not v:
                return ""
            try:
                # 验证是否是有效的 JSON
                json.loads(v)
                return v
            except json.JSONDecodeError:
                return v
        return ""

    class Config:
        from_attributes = True


class StoryCreate(BaseModel):
    user_id: UUID
    title: Optional[str] = None
    movie_style: Optional[Any] = None
    story_line: str = ""
    frames: List[Frame]
    bgm: Optional[str] = None
    result: Optional[str] = None
    result_time: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    @validator("user_id", pre=True)
    def convert_user_id_to_string(cls, v):
        if isinstance(v, UUID):
            return str(v)
        return v

    def dict(self, *args, **kwargs):
        d = super().dict(*args, **kwargs)
        d["user_id"] = str(self.user_id)  # 确保 user_id 被转换为字符串
        return d

    class Config:
        from_attributes = True


class StoryResponse(StoryCreate):
    id: UUID
    user_id: UUID
    status: str = "pending"
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        json_encoders = {UUID: str}
        populate_by_name = True
        extra = "allow"

    def dict(self, *args, **kwargs):
        d = super().dict(*args, **kwargs)
        d["id"] = str(self.id)  # 确保 id 被转换为字符串
        return d

    def json(self, *args, **kwargs):
        return super().json(*args, **kwargs)


class StoryService:
    def __init__(self):
        self.supabase = supabase_client
        self.table = "stories"
        logger.debug("StoryService initialized")

    def get_stories_by_user_id(
        self, user_id: UUID, skip: int = 0, limit: int = 100
    ) -> List[StoryResponse]:
        try:
            response = (
                self.supabase.table(self.table)
                .select("*")
                .eq("user_id", str(user_id))
                .limit(limit)
                .offset(skip)
                .execute()
            )

            stories = []
            for item in response.data:
                try:
                    logger.info(f"Processing story item with id: {item.get('id')}")
                    
                    # Fetch frames for this story
                    frames_response = (
                        self.supabase.table("frames")
                        .select("*")
                        .eq("story_id", item["id"])
                        .execute()
                    )
                    frames = frames_response.data if frames_response.data else []

                    # Fetch characters for this story
                    characters_response = (
                        self.supabase.table("characters")
                        .select("*")
                        .eq("story_id", item["id"])
                        .execute()
                    )
                    characters = characters_response.data if characters_response.data else []
                    
                    story_data = {
                        "id": UUID(item["id"]),  
                        "user_id": UUID(item["user_id"]),  
                        "title": item.get("title", ""),
                        "movie_style": item.get("movie_style", {}),
                        "story_line": item.get("story_line", ""),
                        "frames": frames,
                        "characters": characters,
                        "result": item.get("result"),
                        "result_time": item.get("result_time", "0:00"),
                        "status": item.get("status", "pending"),
                        "created_at": item.get("created_at"),
                        "updated_at": item.get("updated_at"),
                    }
                    logger.info(f"Converted story data with id: {story_data['id']}")
                    story = StoryResponse(**story_data)
                    logger.info(f"Created StoryResponse object with id: {story.id}")
                    stories.append(story)
                except Exception as e:
                    logger.error(f"Error processing story item: {str(e)}")
                    continue

            logger.info(
                f"Returning {len(stories)} stories with ids: {[story.id for story in stories]}"
            )
            return stories
        except Exception as e:
            logger.error(f"Error fetching stories: {str(e)}")
            raise HTTPException(
                status_code=500, detail=f"Failed to fetch stories: {str(e)}"
            )

    def save_story(self, data: dict):
        try:
            # Get story data from nested structure
            story_data = data.get("story", {})
            logger.error(f"----------{story_data.get('frames')}")
            
            # Extract frames and characters before modifying story_data
            frames = story_data.get("frames", [])
            characters = story_data.get("characters", [])

            # Validate user_id is a valid UUID
            try:
                user_id = UUID(str(data.get("user_id", "")))
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid user_id format. Must be a valid UUID.",
                )

            # Convert story to dictionary and add user_id
            story_data = {
                "title": story_data.get("title", ""),
                "movie_style": story_data.get("movie_style", {}),
                "story_line": story_data.get("story_line", ""),
                "result": story_data.get("result"),
                "result_time": story_data.get("result_time", "0:00"),
                "user_id": str(user_id),
                "status": "pending",
            }
            logger.error(f"Attempting to insert story with data: {story_data}")

            # Insert story into database
            response = self.supabase.table(self.table).insert(story_data).execute()

            if not response.data:
                error_msg = "No data returned from database insert"
                logger.error(error_msg)
                raise HTTPException(status_code=500, detail=error_msg)

            story_id = response.data[0].get("id")
            if not story_id:
                error_msg = "No ID returned from database insert"
                logger.error(error_msg)
                raise HTTPException(status_code=500, detail=error_msg)

            # Save frames
            for frame in frames:
                frame_data = frame.copy()  # Create a copy to avoid modifying the original
                frame_data["story_id"] = story_id
                # Remove characters field as it's not in the database schema
                if "characters" in frame_data:
                    del frame_data["characters"]
                
                # Check if frame already exists
                if frame.get("id"):
                    try:
                        # Try to update existing frame
                        logger.error(f"Updating existing frame: {frame_data}")
                        self.supabase.table("frames").update(frame_data).eq("id", frame["id"]).execute()
                    except Exception as e:
                        logger.error(f"Error updating frame: {str(e)}")
                        # If update fails, try to insert
                        logger.error(f"Attempting to insert frame: {frame_data}")
                        self.supabase.table("frames").insert(frame_data).execute()
                else:
                    # Insert new frame
                    logger.error(f"Inserting new frame: {frame_data}")
                    self.supabase.table("frames").insert(frame_data).execute()

            # Save characters
            for character in characters:
                character_data = character.copy()  # Create a copy to avoid modifying the original
                character_data["story_id"] = story_id
                
                # Check if character already exists
                if character.get("id"):
                    try:
                        # Try to update existing character
                        logger.error(f"Updating existing character: {character_data}")
                        self.supabase.table("characters").update(character_data).eq("id", character["id"]).execute()
                    except Exception as e:
                        logger.error(f"Error updating character: {str(e)}")
                        # If update fails, try to insert
                        logger.error(f"Attempting to insert character: {character_data}")
                        self.supabase.table("characters").insert(character_data).execute()
                else:
                    # Insert new character
                    logger.error(f"Inserting new character: {character_data}")
                    self.supabase.table("characters").insert(character_data).execute()

            logger.error(f"Successfully inserted story with ID: {story_id}")
            return story_id

        except HTTPException as he:
            logger.error(f"HTTP Exception in save_story: {str(he)}")
            raise he
        except Exception as e:
            error_msg = f"Error saving story: {str(e)}"
            logger.error(error_msg)
            logger.error(f"Full error details: {type(e).__name__}: {str(e)}")
            raise HTTPException(status_code=500, detail=error_msg)

    def update_story(self, data: dict):
        try:
            # Get story data from nested structure
            story_data = data.get("story", {})
            
            # Extract frames and characters before modifying story_data
            frames = story_data.get("frames", [])
            characters = story_data.get("characters", [])

            # Validate story id
            story_id = story_data.get("id")
            if not story_id:
                raise HTTPException(status_code=400, detail="story id is required")

            # Validate user_id is a valid UUID
            try:
                user_id = UUID(str(data.get("user_id", "")))
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid user_id format. Must be a valid UUID.",
                )

            # Convert story to dictionary and add user_id
            story_data = {
                "id": story_id,
                "title": story_data.get("title", ""),
                "movie_style": story_data.get("movie_style", {}),
                "story_line": story_data.get("story_line", ""),
                "result": story_data.get("result"),
                "result_time": story_data.get("result_time", "0:00"),
                "user_id": str(user_id),
                "status": story_data.get("status", "pending"),
            }

            # Update story in database
            response = (
                self.supabase.table(self.table)
                .update(story_data)
                .eq("id", story_id)
                .execute()
            )

            if not response.data:
                error_msg = "No data returned from database update"
                logger.error(error_msg)
                raise HTTPException(status_code=500, detail=error_msg)

            # Update frames
            for frame in frames:
                frame_data = frame.copy()  # Create a copy to avoid modifying the original
                frame_data["story_id"] = story_id
                # Remove characters field as it's not in the database schema
                if "characters" in frame_data:
                    del frame_data["characters"]
                if frame.get("id"):
                    # Update existing frame
                    logger.error(f"Updating frame: {frame_data}")
                    self.supabase.table("frames").update(frame_data).eq("id", frame["id"]).execute()
                else:
                    # Insert new frame
                    logger.error(f"Inserting new frame: {frame_data}")
                    self.supabase.table("frames").insert(frame_data).execute()

            # Update characters
            for character in characters:
                character_data = character.copy()  # Create a copy to avoid modifying the original
                character_data["story_id"] = story_id
                if character.get("id"):
                    # Update existing character
                    logger.error(f"Updating character: {character_data}")
                    self.supabase.table("characters").update(character_data).eq("id", character["id"]).execute()
                else:
                    # Insert new character
                    logger.error(f"Inserting new character: {character_data}")
                    self.supabase.table("characters").insert(character_data).execute()

            logger.error(f"Successfully updated story with ID: {story_id}")
            return story_id

        except HTTPException as he:
            logger.error(f"HTTP Exception in save_story: {str(he)}")
            raise he
        except Exception as e:
            error_msg = f"Error saving story: {str(e)}"
            logger.error(error_msg)
            logger.error(f"Full error details: {type(e).__name__}: {str(e)}")
            raise HTTPException(status_code=500, detail=error_msg)