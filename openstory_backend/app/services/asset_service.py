from typing import List, Optional, Union, Dict
from sqlalchemy.orm import Session
from app.models.asset import Asset, AssetType
from app.schemas.asset import AssetCreate, AssetUpdate

class AssetService:
    def __init__(self, db: Session):
        self.db = db

    def get_asset(self, asset_id: int) -> Optional[Asset]:
        return self.db.query(Asset).filter(Asset.id == asset_id).first()

    def get_assets_by_type(
        self,
        asset_type: AssetType,
        skip: int = 0,
        limit: int = 100
    ) -> List[Asset]:
        """
        Get all assets of a specific type.
        """
        return self.db.query(Asset)\
            .filter(Asset.type == asset_type)\
            .offset(skip)\
            .limit(limit)\
            .all()

    def get_assets(
        self,
        skip: int = 0,
        limit: int = 100,
        asset_type: Optional[AssetType] = None,
        character_id: Optional[int] = None,
        voice_id: Optional[int] = None
    ) -> Union[List[Asset], Dict[str, List[Asset]]]:
        query = self.db.query(Asset)
        if asset_type:
            query = query.filter(Asset.type == asset_type)
        if character_id:
            query = query.filter(Asset.character_id == character_id)
        if voice_id:
            query = query.filter(Asset.voice_id == voice_id)
            
        # If no filters are applied, return assets grouped by type
        if not any([asset_type, character_id, voice_id]):
            assets = query.all()
            grouped_assets = {
                "bgm": [],
                "voice": [],
                "character": [],
                "other": []
            }
            
            for asset in assets:
                if asset.type == AssetType.BGM:
                    grouped_assets["bgm"].append(asset)
                elif asset.type == AssetType.VOICE:
                    grouped_assets["voice"].append(asset)
                elif asset.type == AssetType.CHARACTER:
                    grouped_assets["character"].append(asset)
                else:
                    grouped_assets["other"].append(asset)
                    
            return grouped_assets
            
        return query.offset(skip).limit(limit).all()

    def create_asset(self, asset: AssetCreate) -> Asset:
        db_asset = Asset(
            type=asset.type,
            url=asset.url,
            character_id=asset.character_id,
            voice_id=asset.voice_id
        )
        self.db.add(db_asset)
        self.db.commit()
        self.db.refresh(db_asset)
        return db_asset

    def update_asset(self, asset_id: int, asset: AssetUpdate) -> Optional[Asset]:
        db_asset = self.get_asset(asset_id)
        if not db_asset:
            return None

        update_data = asset.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_asset, field, value)

        self.db.commit()
        self.db.refresh(db_asset)
        return db_asset

    def delete_asset(self, asset_id: int) -> bool:
        db_asset = self.get_asset(asset_id)
        if not db_asset:
            return False

        self.db.delete(db_asset)
        self.db.commit()
        return True 