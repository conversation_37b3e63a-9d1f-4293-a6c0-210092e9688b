from typing import Dict, Optional, Any
import os
import json
import time
import asyncio

from pydantic import BaseModel
from dotenv import load_dotenv
import concurrent.futures
import uuid
import fal_client
import requests
import hashlib
import io
from PIL import Image

# Assuming supabase client is accessible, e.g., from a common db module
from app.utils.logger import logger
from app.db.supabase import BucketEnum, supabase_client, StorageService


# Load environment variables
load_dotenv()


class ImageGenerationConfig(BaseModel):
    """Configuration for image generation"""

    prompt: str
    negative_prompt: Optional[str] = None
    width: int = 1024
    height: int = 1024
    num_inference_steps: int = 30
    seed: Optional[int] = None
    guidance_scale: float = 7.5
    model_id: str = "fal-ai/fast-sdxl"
    request_id: Optional[str] = None
    character_id: Optional[Any] = None


class ImageGenerationResult(BaseModel):
    """Result of image generation"""

    success: bool
    image_url: Optional[str] = None
    request_id: str
    model_id: str
    error: Optional[str] = None
    error_detail: Optional[Dict[str, Any]] = None
    created_at: Optional[str] = None

    @classmethod
    def from_error(
        cls,
        error: str,
        config: ImageGenerationConfig,
        detail: Optional[Dict[str, Any]] = None,
    ) -> "ImageGenerationResult":
        """Create a result object from an error"""
        return cls(
            success=False,
            request_id=config.request_id or str(uuid.uuid4()),
            model_id=config.model_id,
            error=error,
            error_detail=detail,
        )

    @classmethod
    def from_success(
        cls, image_url: str, config: ImageGenerationConfig
    ) -> "ImageGenerationResult":
        """Create a result object from a successful generation"""
        return cls(
            success=True,
            image_url=image_url,
            request_id=config.request_id or str(uuid.uuid4()),
            model_id=config.model_id,
            created_at=time.strftime("%Y-%m-%d %H:%M:%S"),
        )


class FalImageGenerationService:
    """Service to generate images using FAL.ai models"""

    # Available models mapped to their configuration
    AVAILABLE_MODELS = {
        "fal-ai/fast-sdxl": {
            "description": "Fast version of Stable Diffusion XL",
            "default_negative_prompt": "ugly, blurry, low quality, distorted",
            "required_params": [
                "prompt",
                "negative_prompt",
                "width",
                "height",
                "num_inference_steps",
                "guidance_scale",
            ],
        },
        "fal-ai/magnetique-v3.5": {
            "description": "High resolution SDXL-based model",
            "default_negative_prompt": "bad anatomy, bad hands, three hands, three legs, bad arms",
            "required_params": [
                "prompt",
                "negative_prompt",
                "width",
                "height",
                "num_inference_steps",
                "guidance_scale",
            ],
        },
        "fal-ai/hidream-i1-dev": {
            "description": "HiDream model for highly detailed images",
            "default_negative_prompt": "ugly, blurry, low quality, distorted, bad anatomy",
            "required_params": [
                "prompt",
                "negative_prompt",
                "width",
                "height",
                "num_inference_steps",
            ],
        },
        "fal-ai/hidream-i1-fast": {
            "description": "Fast version of HiDream model",
            "default_negative_prompt": "ugly, blurry, low quality, distorted, bad anatomy",
            "required_params": [
                "prompt",
                "negative_prompt",
                "width",
                "height",
                "num_inference_steps",
            ],
        },
        "fal-ai/flux/dev": {
            "description": "Flux developer model for creative image generation",
            "default_negative_prompt": "ugly, blurry, low quality, distorted, bad composition",
            "required_params": [
                "prompt",
                "negative_prompt",
                "width",
                "height",
                "num_inference_steps",
                "guidance_scale",
            ],
        },
    }

    def __init__(self, api_key: Optional[str] = None, max_concurrent: int = 2):
        """Initialize the FAL image generation service

        Args:
            api_key: FAL API key (defaults to FAL_KEY environment variable)
            max_concurrent: Maximum number of concurrent requests
        """
        self.api_key = api_key or os.getenv("FAL_KEY")
        if not self.api_key:
            logger.warning(
                "No FAL API key provided. Set FAL_KEY environment variable or pass api_key parameter."
            )
        else:
            # Configure the fal_client with the API key
            fal_client.api_key = self.api_key

        # Setup thread pool for controlling concurrency
        self.storage_service = StorageService(BucketEnum.CHARACTERS)
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=max_concurrent
        )
        logger.info(
            f"Initialized FalImageGenerationService with max_concurrent={max_concurrent}"
        )

    def get_available_models(self) -> Dict[str, Dict]:
        """Get list of available models with their configurations"""
        return self.AVAILABLE_MODELS

    def _call_fal_api(self, config: ImageGenerationConfig) -> Dict[str, Any]:
        """Make a request to the FAL API using the client library"""
        if not self.api_key:
            raise ValueError("No FAL API key provided")

        # Use model default negative prompt if none provided
        if config.negative_prompt is None and config.model_id in self.AVAILABLE_MODELS:
            config.negative_prompt = self.AVAILABLE_MODELS[config.model_id].get(
                "default_negative_prompt", ""
            )

        # Get model-specific required parameters
        model_config = self.AVAILABLE_MODELS.get(config.model_id, {})
        required_params = model_config.get(
            "required_params", ["prompt", "negative_prompt", "width", "height"]
        )

        # Prepare arguments for the FAL client
        arguments = {
            "prompt": config.prompt,
            "negative_prompt": config.negative_prompt,
            "width": config.width,
            "height": config.height,
            "num_inference_steps": config.num_inference_steps,
        }

        # Only add guidance_scale if it's in the required parameters
        if "guidance_scale" in required_params:
            arguments["guidance_scale"] = config.guidance_scale

        # Add seed if provided
        if config.seed is not None:
            arguments["seed"] = config.seed

        logger.info(
            f"Requesting image generation: model={config.model_id}, prompt='{config.prompt[:50]}...'"
        )
        logger.info(f"Using parameters: {json.dumps(arguments, indent=2)}")
        start_time = time.time()

        try:
            # Use the subscribe method to wait for the result
            result = fal_client.subscribe(
                config.model_id, arguments=arguments, with_logs=True
            )

            elapsed = time.time() - start_time
            logger.info(
                f"Image generation successful in {elapsed:.2f}s: model={config.model_id}"
            )

            return result
        except Exception as e:
            elapsed = time.time() - start_time
            logger.error(f"API error after {elapsed:.2f}s: {str(e)}")
            raise

    async def generate_image(
        self, config: ImageGenerationConfig
    ) -> ImageGenerationResult:
        """Generate an image with the specified configuration

        Runs in the current thread. For parallel processing, use generate_image_async.

        Args:
            config: Configuration for the image generation

        Returns:
            Result containing success status and image URL or error details
        """
        if not config.request_id:
            config.request_id = str(uuid.uuid4())

        try:
            fal_result = self._call_fal_api(config)

            image_url_from_fal = None
            if "images" in fal_result and len(fal_result["images"]) > 0:
                image_url_from_fal = fal_result["images"][0].get("url")
            elif "image" in fal_result:
                image_url_from_fal = fal_result["image"].get("url")

            if not image_url_from_fal:
                return ImageGenerationResult.from_error(
                    error="No image URL found in the FAL response",
                    config=config,
                    detail=fal_result,
                )

            # If character_id is provided, download, store in Supabase, and get signed URL
            if config.character_id:
                try:
                    logger.info(
                        f"Processing image for character_id: {config.character_id}"
                    )
                    # 1. Download image from fal-ai
                    dl_response = requests.get(
                        image_url_from_fal, stream=True, timeout=30
                    )
                    dl_response.raise_for_status()
                    image_data = dl_response.content
                    content_type = dl_response.headers.get("content-type", "image/jpeg")

                    # 2. Prepare Metadata
                    pil_image = Image.open(io.BytesIO(image_data))
                    width, height = pil_image.size
                    image_format = pil_image.format or "JPEG"  # Pillow format
                    extension = image_format.lower()
                    if extension == "jpeg":
                        extension = "jpg"

                    # Ensure content_type matches determined format if possible
                    # For example, if PIL says PNG, content_type should reflect that
                    if f"image/{extension}" != content_type.lower() and extension in [
                        "png",
                        "jpg",
                        "webp",
                        "gif",
                    ]:
                        content_type = f"image/{extension}"

                    file_size = len(image_data)
                    sha256_hash = hashlib.sha256(image_data).hexdigest()

                    # Use str(config.character_id) in case it's a UUID object
                    path_in_bucket = (
                        f"{str(config.character_id)}/{uuid.uuid4()}.{extension}"
                    )

                    # 3. Upload to Supabase Storage (Private Bucket 'characters')
                    logger.info(
                        f"Uploading to Supabase storage: bucket=characters, path={path_in_bucket}, content_type={content_type}"
                    )
                    signed_url = await self.storage_service.upload_file(
                        path_in_bucket,
                        image_data,
                        content_type,
                    )

                    logger.info(
                        f"Successfully uploaded to Supabase storage: {path_in_bucket}"
                    )
                    # 4. Insert into character_images table
                    character_image_data = {
                        "character_id": str(config.character_id),
                        "bucket": "characters",
                        "path": path_in_bucket,
                        "width": width,
                        "height": height,
                        "mime_type": content_type,
                        "file_size": file_size,
                        "sha256": sha256_hash,
                    }
                    logger.info(
                        f"Inserting into character_images: {character_image_data}"
                    )
                    insert_response = (
                        supabase_client.table("character_images")
                        .insert(character_image_data)
                        .execute()
                    )

                    if hasattr(insert_response, "error") and insert_response.error:
                        logger.error(
                            f"Supabase character_images insert failed for {path_in_bucket}: {insert_response.error.message if hasattr(insert_response.error, 'message') else insert_response.error}"
                        )
                        # Potentially still use the fal_url or stored image if DB insert fails
                    else:
                        logger.info(
                            f"Successfully inserted into character_images for path: {path_in_bucket}"
                        )

                    final_image_url_for_response = signed_url

                except Exception as e_supabase_flow:
                    logger.exception(
                        f"Error during Supabase storage/DB operations for character_id {config.character_id}: {str(e_supabase_flow)}"
                    )
                    # Fallback to the original fal_url if any step in the Supabase flow fails
                    final_image_url_for_response = image_url_from_fal
            else:
                # No character_id provided, so just return the original fal_url
                final_image_url_for_response = image_url_from_fal

            return ImageGenerationResult.from_success(
                image_url=final_image_url_for_response, config=config
            )
        except Exception as e:
            logger.exception(f"Error generating image: {str(e)}")

            error_detail = {}
            # Check if it's a FAL client error with details
            if hasattr(e, "error_detail"):
                error_detail = e.error_detail

            return ImageGenerationResult.from_error(
                error=str(e), config=config, detail=error_detail
            )

    async def generate_image_async(
        self, config: ImageGenerationConfig
    ) -> ImageGenerationResult:
        """Generate an image asynchronously

        Uses the thread pool to limit concurrency.

        Args:
            config: Configuration for the image generation

        Returns:
            Result containing success status and image URL or error details
        """
        if not config.request_id:
            config.request_id = str(uuid.uuid4())

        loop = asyncio.get_event_loop()

        try:
            # Run the synchronous method in the thread pool
            result = await loop.run_in_executor(
                self.executor, lambda cfg=config: self.generate_image(cfg)
            )
            return result
        except Exception as e:
            logger.exception(f"Async wrapper error: {str(e)}")
            return ImageGenerationResult.from_error(
                error=f"Async execution error: {str(e)}", config=config
            )

    def shutdown(self):
        """Shutdown the executor"""
        self.executor.shutdown(wait=True)
        logger.info("FalImageGenerationService shutdown complete")
