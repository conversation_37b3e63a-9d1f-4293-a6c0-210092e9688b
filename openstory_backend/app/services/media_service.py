import os
import io
import time
import httpx
import hashlib
import fal_client
import subprocess
from urllib.parse import urlparse
from pydub import AudioSegment
import asyncio

from pydantic import BaseModel
from runwayml import AsyncRunwayML
from elevenlabs.client import AsyncElevenLabs

from app.utils.logger import logger
from app.core.config import get_settings
from app.db.supabase import BucketEnum, StorageService
from app.services.lip_sync_service import lip_sync_generate


settings = get_settings()


class TTSRequest(BaseModel):
    text: str
    voice_id: str = "JBFqnCBsd6RMkjVDRZzb"
    model_id: str = "eleven_multilingual_v2"
    output_format: str = "mp3_44100_128"

    def get_file_name(self) -> str:
        text_md5 = hashlib.md5(self.text.encode("utf-8")).hexdigest()
        return f"{text_md5}.{self.get_ext()}"

    def get_ext(self) -> str:
        return self.output_format.split("_")[0]


class ElevenLabsService:
    def __init__(self) -> None:
        self.storage_service = StorageService(BucketEnum.AUDIO)
        self.elevenlabs = AsyncElevenLabs(api_key=settings.ELEVENLABS_API_KEY)

    async def text_to_speech(self, request: TTSRequest) -> str:
        temp_file = None
        try:
            audio_data = io.BytesIO()
            async for chunk in self.elevenlabs.text_to_speech.convert(**request.__dict__):
                audio_data.write(chunk)

            # Trim audio if needed
            audio_data.seek(0)
            logger.error(f"audio_data size: {len(audio_data.getvalue())} bytes")
            
            try:
                # Create a unique temporary file name
                temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
                os.makedirs(temp_dir, exist_ok=True)
                temp_file = os.path.join(temp_dir, f"temp_{int(time.time())}_{request.get_file_name()}")
                
                # Save to temporary file first
                with open(temp_file, "wb") as f:
                    f.write(audio_data.getvalue())
                
                # Wait a bit to ensure file is fully written
                await asyncio.sleep(1)  # Increased wait time
                
                # Add retry mechanism for file operations
                max_retries = 3
                retry_delay = 1
                for attempt in range(max_retries):
                    try:
                        # Verify file exists and has content
                        if not os.path.exists(temp_file):
                            raise FileNotFoundError(f"Temporary file {temp_file} was not created")
                        
                        file_size = os.path.getsize(temp_file)
                        if file_size == 0:
                            raise ValueError(f"Temporary file {temp_file} is empty")
                        
                        logger.error(f"Processing audio file: {temp_file} (size: {file_size} bytes)")
                        
                        # Explicitly close any existing file handles
                        try:
                            import gc
                            gc.collect()
                        except:
                            pass
                            
                        # Use context manager for AudioSegment operations
                        with open(temp_file, 'rb') as audio_file:
                            audio = AudioSegment.from_file(audio_file, format=request.get_ext())
                            during = 5
                            logger.error(f"audio duration: {len(audio)} ms")
                            
                            # If audio is longer than requested duration, trim it
                            if len(audio) > during * 1000:  # pydub works in milliseconds
                                audio = audio[:during * 1000]
                                logger.error(f"trimmed audio duration: {len(audio)} ms")

                                # Export trimmed audio to bytes
                                trimmed_audio = io.BytesIO()
                                audio.export(trimmed_audio, format=request.get_ext())
                                trimmed_audio.seek(0)
                                audio_data = trimmed_audio
                        
                        # If we get here, the operation was successful
                        break
                        
                    except (FileNotFoundError, ValueError, IOError) as e:
                        if attempt == max_retries - 1:  # Last attempt
                            raise
                        logger.error(f"Attempt {attempt + 1} failed: {str(e)}. Retrying...")
                        await asyncio.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                
            except Exception as e:
                logger.error(f"Error processing audio: {str(e)}")
                raise
            finally:
                # Clean up temp file with retries
                if temp_file and os.path.exists(temp_file):
                    # Force garbage collection before trying to delete
                    try:
                        import gc
                        gc.collect()
                    except:
                        pass
                        
                    for attempt in range(max_retries):
                        try:
                            # Wait a bit before trying to delete
                            await asyncio.sleep(1)
                            os.remove(temp_file)
                            logger.error(f"Successfully removed temp file: {temp_file}")
                            break
                        except Exception as e:
                            if attempt == max_retries - 1:  # Last attempt
                                logger.error(f"Error removing temp file after {max_retries} attempts: {str(e)}")
                            else:
                                logger.error(f"Error removing temp file (attempt {attempt + 1}): {str(e)}. Retrying...")
                                await asyncio.sleep(retry_delay)
                                retry_delay *= 2  # Exponential backoff

            # upload to supabase
            try:
                return await self.storage_service.upload_file(
                    request.get_file_name(),
                    audio_data.getvalue(),
                    "audio/mpeg",
                )
            except Exception as e:
                logger.error(f"Error uploading to storage: {str(e)}")
                raise

        except Exception as e:
            logger.error(f"Text to speech conversion failed: {str(e)}")
            raise


class ImageToVideoRequest(BaseModel):
    prompt_image: str
    prompt_text: str = "Generate a video"
    model: str = "gen4_turbo"
    ratio: str = "1280:720"
    duration: int = 10
    dialogue: str = ""


class TextToImageRequest(BaseModel):
    prompt_text: str
    ratio: str = "1920:1080"
    model: str = "gen4_image"
    reference_images: list = []


class RunWayService:
    def __init__(self) -> None:
        self.storage_service = StorageService(BucketEnum.VIDEO)
        self.runway_client = AsyncRunwayML(api_key=settings.RUNWAYML_API_SECRET)

    async def _retrieve_result(self, task_id: str) -> str | None:
        """retrieve runway task result

        Args:
            task_id: runway task id

        Returns:
            task result
        """
        task = await self.runway_client.tasks.retrieve(task_id)
        while task.status not in ["SUCCEEDED", "FAILED"]:
            task = await self.runway_client.tasks.retrieve(task_id)

        if task.status != "SUCCEEDED" or task.output is None:
            logger.error(f"Video generation failed, task: {task}")
            return None

        return task.output[0]

    async def _upload_file(
        self,
        url: str,
        content_type: str,
        other_bucket: BucketEnum,
    ) -> str:
        """get file content and upload to storage

        Args:
            url: video/image url
            content_type: content type
            other_bucket: bucket enum

        Returns:
            storage file url
        """
        async with httpx.AsyncClient() as client:
            response = await client.get(url)
            response.raise_for_status()

            parsded_url = urlparse(url)
            file_name = os.path.basename(parsded_url.path)
            return await self.storage_service.upload_file(
                file_name, response.content, content_type, other_bucket
            )

    async def image_to_video(self, request: ImageToVideoRequest) -> str | None:
        """generate video based on prompt image

        Args:
            request: generation request

        Returns:
            runway generation task id
        """
        response = await self.runway_client.image_to_video.create(**request.__dict__)
        video_url = await self._retrieve_result(response.id)
        assert video_url is not None
        return await self._upload_file(
            video_url,
            "video/mp4",
            BucketEnum.VIDEO,
        )

    async def text_to_image(self, request: TextToImageRequest):
        """generation image based on text with reference image

        Args:
            request: generation request

        Returns:
            generation task id
        """
        response = await self.runway_client.text_to_image.create(**request.__dict__)
        image_url = await self._retrieve_result(response.id)
        assert image_url is not None
        return await self._upload_file(
            image_url,
            "image/png",
            BucketEnum.VIDEO,
        )


class FalTaskResult(BaseModel):
    video_url: str
    content_type: str
    file_name: str
    file_size: int


async def trim_video_to_audio(video_url: str, audio_url: str) -> str:
    """Trim video to match audio duration using ffmpeg
    
    Args:
        video_url: video url
        audio_url: audio url
        
    Returns:
        trimmed video url
    """
    try:
        # Download video and audio
        async with httpx.AsyncClient() as client:
            video_response = await client.get(video_url)
            audio_response = await client.get(audio_url)
            
            # Save to temporary files
            video_path = "temp_video.mp4"
            audio_path = "temp_audio.mp3"
            
            with open(video_path, "wb") as f:
                f.write(video_response.content)
            with open(audio_path, "wb") as f:
                f.write(audio_response.content)
            
            # Get audio duration using ffprobe
            ffprobe_cmd = [
                'ffprobe', 
                '-v', 'error',
                '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                audio_path
            ]
            audio_duration = float(subprocess.check_output(ffprobe_cmd).decode().strip())
            
            # Trim video using ffmpeg
            output_path = "trimmed_video.mp4"
            ffmpeg_cmd = [
                'ffmpeg',
                '-i', video_path,
                '-t', str(audio_duration),
                '-c', 'copy',
                output_path
            ]
            subprocess.run(ffmpeg_cmd, check=True)
            
            # Upload trimmed video
            storage_service = StorageService(BucketEnum.VIDEO)
            with open(output_path, "rb") as f:
                trimmed_video_url = await storage_service.upload_file(
                    f"trimmed_{os.path.basename(video_url)}",
                    f.read(),
                    "video/mp4"
                )
            
            # Clean up temporary files
            os.remove(video_path)
            os.remove(audio_path)
            os.remove(output_path)
            
            return trimmed_video_url
            
    except Exception as e:
        logger.error(f"Error trimming video: {str(e)}")
        return video_url


class FalService:
    def __init__(self) -> None:
        self.fal_client = fal_client

    async def run_task(self, request: ImageToVideoRequest) -> FalTaskResult:
        """run task and get result

        Args:
            request: image to video request

        Returns:
            fal task result
        """
        start = time.time()
        handler = await self.fal_client.submit_async(
            request.model,
            arguments={
                "prompt": request.prompt_text,
                "image_url": request.prompt_image,
            },
        )
        async for event in handler.iter_events(with_logs=True, interval=5):
            logger.info(f"receive task event: {event}")

        result = await handler.get()
        logger.error(f"get task result: {result} cost {time.time() - start}s")
        video_info = result
#         video_info = {'video': {'url': 'https://v3.fal.media/files/elephant/9HMyf2Fw_PJmFnBf1LpiU_output.mp4', 'content_type': 
# 'video/mp4', 'file_name': 'output.mp4', 'file_size': 6016471}}
        logger.error(f"video_info: {video_info}")
        logger.error(f"request.dialogue: {request.dialogue}")

        # generate video with dialogue
        if request.dialogue:
            logger.error(f"trigger request.dialogue: {request.dialogue}")
            tts_req = TTSRequest(text=request.dialogue)
            audio_url = await elevenlabs_service.text_to_speech(tts_req)
            # audio_url = "https://storage.googleapis.com/falserverless/kling/kling-audio.mp3"
            logger.error(f"audio_url: {audio_url}")
            
            try:
                video_url = video_info["video"]["url"]
                lip_sync_result = await lip_sync_generate(video_url, audio_url)
                logger.error(f"lip_sync_result: {lip_sync_result}")
                
                if lip_sync_result and isinstance(lip_sync_result, dict):
                    if "video" in lip_sync_result and isinstance(lip_sync_result["video"], dict):
                        video_info["video"]["url"] = lip_sync_result["video"]["url"]
                        # Trim video to match audio duration
                        video_info["video"]["url"] = await trim_video_to_audio(video_info["video"]["url"], audio_url)
                    else:
                        logger.error(f"Invalid lip sync result format: {lip_sync_result}")
                else:
                    logger.error("Lip sync failed or returned invalid result")
            except Exception as e:
                logger.error(f"Error during lip sync: {str(e)}")

        logger.error(f"task run end: {video_info}")
        return FalTaskResult(
            video_url=video_info["video"]["url"],
            content_type=video_info["video"]["content_type"],
            file_name=video_info["video"]["file_name"],
            file_size=video_info["video"]["file_size"],
        )

    async def submit_task(self, request: ImageToVideoRequest) -> str:
        """sumbit task and return request_id

        Args:
            request: image to video request

        Returns:
            task request_id
        """
        handler = await self.fal_client.submit_async(
            request.model,
            arguments={
                "prompt": request.prompt_text,
                "image_url": request.prompt_image,
            },
        )
        return handler.request_id

    async def get_task_status(
        self,
        request_id: str,
        model: str = "fal-ai/kling-video/v1/standard/image-to-video",
    ) -> str:
        """query task status

        Args:
            request_id: task request_id
            model: model for generation

        Returns:
            task status
        """
        status = await self.fal_client.status_async(model, request_id)
        return type(status).__name__

    async def get_task_result(
        self,
        request_id: str,
        model: str = "fal-ai/kling-video/v1/standard/image-to-video",
    ) -> FalTaskResult:
        """query task result when completed

        Args:
            request_id: task request_id
            model: model for generation

        Returns:
            task result
        """
        result = await self.fal_client.result_async(model, request_id)
        video_info = result["video"]
        return FalTaskResult(
            video_url=video_info["url"],
            content_type=video_info["content_type"],
            file_name=video_info["file_name"],
            file_size=video_info["file_size"],
        )


elevenlabs_service = ElevenLabsService()
runway_service = RunWayService()
fal_service = FalService()