import os
import requests
from typing import Optional
from pydantic import BaseModel
from app.utils.logger import logger


class VoiceGenerationConfig(BaseModel):
    text: str
    voice_description: str
    model_id: str = "eleven_multilingual_v2"
    stability: float = 0.5
    similarity_boost: float = 0.75


class VoiceGenerationResult(BaseModel):
    preview_url: str
    voice_id: str
    success: bool
    error_message: Optional[str] = None


class ElevenLabsVoiceService:
    def __init__(self):
        self.api_key = os.getenv("ELEVENLABS_API_KEY")
        self.api_host = os.getenv("ELEVENLABS_API_HOST", "https://api.elevenlabs.io/v1")
        if not self.api_key:
            raise ValueError("ELEVENLABS_API_KEY environment variable is not set")

    def generate_voice(self, config: VoiceGenerationConfig) -> VoiceGenerationResult:
        try:
            # Step 1: Create voice preview
            preview_url = self._create_voice_preview(config)
            if not preview_url:
                return VoiceGenerationResult(
                    preview_url="",
                    voice_id="",
                    success=False,
                    error_message="Failed to create voice preview",
                )

            # Step 2: Create voice from preview
            voice_id = self._create_voice_from_preview(preview_url, config)
            if not voice_id:
                return VoiceGenerationResult(
                    preview_url=preview_url,
                    voice_id="",
                    success=False,
                    error_message="Failed to create voice from preview",
                )

            return VoiceGenerationResult(
                preview_url=preview_url, voice_id=voice_id, success=True
            )

        except Exception as e:
            logger.error(f"Error generating voice: {str(e)}")
            return VoiceGenerationResult(
                preview_url="", voice_id="", success=False, error_message=str(e)
            )

    def _create_voice_preview(self, config: VoiceGenerationConfig) -> Optional[str]:
        try:
            url = f"{self.api_host}/text-to-voice/create-previews"
            headers = {"xi-api-key": self.api_key, "Content-Type": "application/json"}
            data = {
                "text": config.text,
                "voice_description": config.voice_description,
                "model_id": config.model_id,
                "stability": config.stability,
                "similarity_boost": config.similarity_boost,
            }

            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            return result.get("preview_url")

        except Exception as e:
            logger.error(f"Error creating voice preview: {str(e)}")
            return None

    def _create_voice_from_preview(
        self, preview_url: str, config: VoiceGenerationConfig
    ) -> Optional[str]:
        try:
            url = f"{self.api_host}/text-to-voice/create-voice-from-preview"
            headers = {"xi-api-key": self.api_key, "Content-Type": "application/json"}
            data = {
                "preview_url": preview_url,
                "name": f"Voice_{config.voice_description[:20]}",
                "description": config.voice_description,
            }

            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()
            result = response.json()
            return result.get("voice_id")

        except Exception as e:
            logger.error(f"Error creating voice from preview: {str(e)}")
            return None

