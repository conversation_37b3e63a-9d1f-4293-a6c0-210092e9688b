import random

from uuid import UUID
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict, Any

from app.utils.logger import logger
from app.services.image_generation import (
    ImageGenerationConfig,
    FalImageGenerationService,
)
from app.db.supabase import supabase_client
from app.schemas.character import (
    Character,
    CharacterUpdate,
    CharacterCreate,
)
from app.schemas.progression import (
    CharacterProgression,
    CharacterProgressionCreate,
    CharacterProgressionUpdate,
)


# =============================================================================
# IMAGE STYLES SERVICE
# =============================================================================

# Style definitions with positive and negative prompt elements
STYLE_DEFINITIONS: Dict[str, Dict[str, Any]] = {
    "realistic": {
        "name": "Realistic",
        "description": "Photorealistic rendering with natural lighting and details",
        "positive": [
            "high detail photograph",
            "realistic skin texture",
            "professional lighting",
            "4k resolution",
            "realistic features",
        ],
        "negative": [
            "cartoon",
            "illustration",
            "drawing",
            "painting",
            "unrealistic proportions",
            "digital art",
        ],
    },
    "anime": {
        "name": "Anime",
        "description": "Japanese anime style illustration with characteristic features",
        "positive": [
            "anime style",
            "detailed anime illustration",
            "studio ghibli",
            "vibrant colors",
            "expressive eyes",
            "clean lineart",
        ],
        "negative": [
            "realistic",
            "photorealistic",
            "3d rendering",
            "western cartoon",
            "realistic skin texture",
        ],
    },
    "oil_painting": {
        "name": "Oil Painting",
        "description": "Classical oil painting style with rich textures",
        "positive": [
            "oil painting",
            "detailed brushstrokes",
            "classical portrait",
            "canvas texture",
            "rich colors",
            "artistic masterpiece",
            "museum quality",
        ],
        "negative": [
            "digital",
            "photograph",
            "3d",
            "flat colors",
            "computer generated",
            "anime",
        ],
    },
    "cyberpunk": {
        "name": "Cyberpunk",
        "description": "Futuristic cyberpunk aesthetic with neon colors and urban dystopia",
        "positive": [
            "cyberpunk",
            "neon lights",
            "futuristic",
            "urban dystopia",
            "holographic displays",
            "cybernetic details",
            "rainy night streets",
            "high contrast lighting",
        ],
        "negative": [
            "natural setting",
            "rural",
            "historical",
            "daytime",
            "pastoral",
            "plain lighting",
        ],
    },
    "fantasy": {
        "name": "Fantasy",
        "description": "High fantasy style with magical elements and ethereal lighting",
        "positive": [
            "fantasy illustration",
            "magical",
            "ethereal lighting",
            "mystical atmosphere",
            "detailed fantasy artwork",
            "enchanted",
            "dramatic lighting",
        ],
        "negative": [
            "mundane",
            "modern",
            "urban",
            "realistic",
            "everyday setting",
            "contemporary clothing",
        ],
    },
    "pop_art": {
        "name": "Pop Art",
        "description": "Bold, colorful pop art style inspired by comic books and advertising",
        "positive": [
            "pop art style",
            "bold colors",
            "comic book",
            "halftone dots",
            "andy warhol inspired",
            "retro",
            "graphic design",
            "high contrast",
        ],
        "negative": [
            "subtle",
            "realistic",
            "muted colors",
            "detailed shading",
            "photorealistic",
            "3d rendering",
        ],
    },
    "watercolor": {
        "name": "Watercolor",
        "description": "Soft watercolor painting with flowing colors and translucent effects",
        "positive": [
            "watercolor painting",
            "soft colors",
            "flowing pigment",
            "translucent",
            "wet on wet technique",
            "artistic",
            "bleeding colors",
            "paper texture",
        ],
        "negative": [
            "sharp lines",
            "digital",
            "crisp details",
            "3d",
            "photorealistic",
            "hard edges",
        ],
    },
    "polaroid": {
        "name": "Polaroid",
        "description": "Vintage polaroid photo with characteristic film grain and colors",
        "positive": [
            "polaroid photo",
            "vintage photograph",
            "film grain",
            "slightly overexposed",
            "nostalgic colors",
            "instant film",
            "white frame",
        ],
        "negative": [
            "digital",
            "perfect exposure",
            "high resolution",
            "modern photography",
            "crisp details",
            "high dynamic range",
        ],
    },
}


def get_available_styles() -> List[Dict[str, str]]:
    """
    Return a list of available styles with their names and descriptions.

    Returns:
        List of dictionaries containing style id, name and description
    """
    return [
        {"id": style_id, "name": info["name"], "description": info["description"]}
        for style_id, info in STYLE_DEFINITIONS.items()
    ]


def get_style_details(style_id: str) -> Dict[str, Any]:
    """
    Get the complete style details for a given style ID.

    Args:
        style_id: The ID of the style to retrieve

    Returns:
        Complete style definition dictionary or None if not found
    """
    return STYLE_DEFINITIONS.get(style_id, None)


# =============================================================================
# CHARACTER PROMPT SERVICE
# =============================================================================


class CharacterDetails(BaseModel):
    """Details about a character for prompt generation"""

    gender: str = Field(
        ..., description="Character gender (e.g., male, female, non-binary)"
    )
    age: str = Field(..., description="Character age (e.g., 25, teenage, elderly)")
    appearance: str = Field(..., description="Detailed appearance description")
    style: str = Field(..., description="Style ID for the image generation")
    additional_details: Optional[str] = Field(
        None, description="Any additional character details"
    )


class PromptOutput(BaseModel):
    """Output containing formatted prompts for image generation"""

    positive_prompt: str = Field(
        ..., description="Positive prompt for image generation"
    )
    negative_prompt: str = Field(
        ..., description="Negative prompt to avoid unwanted elements"
    )
    style_name: str = Field(..., description="The name of the selected style")
    character_summary: str = Field(..., description="A brief summary of the character")


def generate_character_prompt(character: CharacterDetails) -> PromptOutput:
    """
    Generate formatted positive and negative prompts for character image generation.

    Args:
        character: Character details including gender, age, appearance, and style

    Returns:
        Formatted positive and negative prompts
    """
    # Get style details
    style_details = get_style_details(character.style)
    if not style_details:
        raise ValueError(f"Style '{character.style}' not found")

    style_name = style_details["name"]

    # Generate positive prompt components
    positive_components = []

    # Base prompt format
    base_prompt = f"a {style_name.lower()} image of someone {character.gender}, aged {character.age}, looking {character.appearance}"
    positive_components.append(base_prompt)

    # Add style positive elements (randomly select 3-5 elements if there are enough)
    style_positives = style_details["positive"]
    if len(style_positives) > 3:
        selected_positives = random.sample(
            style_positives, min(len(style_positives), random.randint(3, 5))
        )
    else:
        selected_positives = style_positives

    positive_components.extend(selected_positives)

    # Add additional details if provided
    if character.additional_details:
        positive_components.append(character.additional_details)

    # Generate negative prompt components
    negative_components = ["ugly", "blurry", "low quality", "distorted"]

    # Add style negative elements
    negative_components.extend(style_details["negative"])

    # Format the final prompts
    positive_prompt = ", ".join(positive_components)
    negative_prompt = ", ".join(negative_components)

    # Create a brief character summary
    character_summary = (
        f"{character.gender.capitalize()}, {character.age}, {style_name.lower()} style"
    )

    return PromptOutput(
        positive_prompt=positive_prompt,
        negative_prompt=negative_prompt,
        style_name=style_name,
        character_summary=character_summary,
    )


def generate_character_batch(characters: List[CharacterDetails]) -> List[PromptOutput]:
    """
    Generate prompts for multiple characters in a batch.

    Args:
        characters: List of character details

    Returns:
        List of prompt outputs for each character
    """
    return [generate_character_prompt(character) for character in characters]


# =============================================================================
# CHARACTER SERVICE
# =============================================================================


class CharacterService:
    def __init__(self):
        self.supabase = supabase_client
        self.table = "characters"

    def get_by_id(self, character_id: UUID) -> Optional[Character]:
        response = (
            self.supabase.table(self.table)
            .select("*")
            .eq("id", str(character_id))
            .execute()
        )
        if response.data:
            return Character(**response.data[0])
        return None

    def get_by_user_id(
        self, user_id: UUID, skip: int = 0, limit: int = 100
    ) -> List[Character]:
        """
        Get all characters created by a specific user with pagination.

        Args:
            user_id: UUID of the user
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List of Character objects with voice_name
        """
        try:
            # 先获取角色列表
            response = (
                self.supabase.table(self.table)
                .select("*")
                .eq("creator_user_id", str(user_id))
                .limit(limit)
                .offset(skip)
                .execute()
            )

            if not response.data:
                return []

            characters = []
            for item in response.data:
                try:
                    # 如果角色有关联的 voice_id，则获取对应的 voice_locale
                    if item.get("voice_id"):
                        voice_response = (
                            self.supabase.table("voice_locale")
                            .select("display_name")
                            .eq("voice_id", item["voice_id"])
                            .execute()
                        )
                        logger.error(f"voice_response {voice_response}")
                        if voice_response.data:
                            item["voice_name"] = voice_response.data[0].get(
                                "display_name"
                            )

                    character = Character(**item)
                    characters.append(character)
                except Exception as e:
                    logger.error(f"Error processing character item: {str(e)}")
                    continue

            return characters

        except Exception as e:
            logger.error(f"Error fetching characters from Supabase: {str(e)}")
            raise

    async def create(self, character: CharacterCreate) -> Character:
        """
        Create a new character in Supabase.

        Args:
            character: CharacterCreate object containing the character data

        Returns:
            Created Character object
        """
        try:
            character_dict = character.model_dump()
            # 设置默认值
            character_dict["is_public"] = character_dict.get("is_public", False)
            
            # Build avatar prompt
            try:
                image_service = FalImageGenerationService()

                avatar_prompt = f"a realistic portrait of {character.name}, {character.gender}, {character.age} years old, {character.appearance}, the visual style is {character.visual_style}, create a realistic portrait of the character avatar"

                # --- Preview Image Prompt ---
                preview_prompt = f"a realistic portrait of {character.name}, {character.gender}, {character.age} years old, {character.appearance}, the visual style is is {character.visual_style}, create a realistic portrait of the character preview image"
                negative_prompt = character_dict["appearance"]

                avatar_image_config = ImageGenerationConfig(
                    prompt=avatar_prompt,
                    negative_prompt=negative_prompt,
                    model_id="fal-ai/fast-sdxl",
                    width=480,
                    height=480,
                    num_inference_steps=30,
                    guidance_scale=7.5,
                    character_id=str(character_dict["creator_user_id"]),
                )

                preview_image_config = ImageGenerationConfig(
                    prompt=preview_prompt,
                    negative_prompt=negative_prompt,
                    model_id="fal-ai/fast-sdxl",
                    width=1024,
                    height=1024,
                    num_inference_steps=30,
                    guidance_scale=7.5,
                    character_id=str(character_dict["creator_user_id"]),
                )

                avatar_result = await image_service.generate_image(avatar_image_config)
                preview_img_result = await image_service.generate_image(
                    preview_image_config
                )
                logger.error(f" result generated image: {avatar_result}")
                character_dict["avatar_urls"] = {"url": avatar_result.image_url}
                character_dict["preview_img"] = preview_img_result.image_url

                # Generate voice if voice description is provided
                # if character_dict.get("voice_description"):
                #     try:
                #         voice_service = ElevenLabsVoiceService()
                #         voice_config = VoiceGenerationConfig(
                #             text=character_dict["oneliner"],  # Use oneliner as sample text
                #             voice_description=character_dict["voice_description"]
                #         )
                #         voice_result = voice_service.generate_voice(voice_config)
                #         if voice_result.success:
                #             character_dict["voice_id"] = voice_result.voice_id
                #             logger.info(f"Voice generated successfully: {voice_result.voice_id}")
                #         else:
                #             logger.error(f"Voice generation failed: {voice_result.error_message}")
                #     except Exception as e:
                #         logger.error(f"Error in voice generation: {str(e)}")
                #
            except Exception as e:
                logger.error(f"Error generating character images: {str(e)}")
                # Continue with character creation even if image generation fails

            response = self.supabase.table(self.table).insert(character_dict).execute()

            if not response.data:
                raise Exception("Failed to create character")

            return response.data[0]

        except Exception as e:
            logger.error(f"Error creating character in Supabase: {str(e)}")
            raise

    def update(
        self, character_id: UUID, character: CharacterUpdate
    ) -> Optional[Character]:
        update_data = {k: v for k, v in character.model_dump().items() if v is not None}
        if not update_data:
            return self.get_by_id(character_id)

        response = (
            self.supabase.table(self.table)
            .update(update_data)
            .eq("id", str(character_id))
            .execute()
        )
        if response.data:
            return Character(**response.data[0])
        return None

    def delete(self, character_id: UUID) -> bool:
        response = (
            self.supabase.table(self.table)
            .delete()
            .eq("id", str(character_id))
            .execute()
        )
        return bool(response.data)


# =============================================================================
# CHARACTER PROGRESSION SERVICE
# =============================================================================


class CharacterProgressionService:
    def __init__(self):
        self.supabase = supabase_client
        self.table = "character_progressions"
        self.character_service = CharacterService()

    def get_by_id(self, progression_id: UUID) -> Optional[CharacterProgression]:
        response = (
            self.supabase.table(self.table)
            .select("*")
            .eq("id", str(progression_id))
            .execute()
        )
        if response.data:
            return CharacterProgression(**response.data[0])
        return None

    def get_by_character_id(self, character_id: UUID) -> List[CharacterProgression]:
        response = (
            self.supabase.table(self.table)
            .select("*")
            .eq("character_id", str(character_id))
            .execute()
        )
        return [CharacterProgression(**item) for item in response.data]

    def get_by_episode_id(self, episode_id: UUID) -> List[CharacterProgression]:
        response = (
            self.supabase.table(self.table)
            .select("*")
            .eq("episode_id", str(episode_id))
            .execute()
        )
        return [CharacterProgression(**item) for item in response.data]

    def create(
        self, progression: CharacterProgressionCreate
    ) -> Optional[CharacterProgression]:
        # Verify character exists
        character = self.character_service.get_by_id(progression.character_id)
        if not character:
            return None

        progression_data = progression.model_dump()

        response = self.supabase.table(self.table).insert(progression_data).execute()
        return CharacterProgression(**response.data[0])

    def update(
        self, progression_id: UUID, progression: CharacterProgressionUpdate
    ) -> Optional[CharacterProgression]:
        update_data = {
            k: v for k, v in progression.model_dump().items() if v is not None
        }
        if not update_data:
            return self.get_by_id(progression_id)

        response = (
            self.supabase.table(self.table)
            .update(update_data)
            .eq("id", str(progression_id))
            .execute()
        )
        if response.data:
            return CharacterProgression(**response.data[0])
        return None

    def delete(self, progression_id: UUID) -> bool:
        response = (
            self.supabase.table(self.table)
            .delete()
            .eq("id", str(progression_id))
            .execute()
        )
        return bool(response.data)
