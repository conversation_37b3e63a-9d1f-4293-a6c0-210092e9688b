import asyncio
import supabase
from storage3.types import FileOptions

from enum import Enum
from app.core.config import get_settings

settings = get_settings()
supabase_client = supabase.create_client(
    settings.SUPABASE_URL,
    settings.SUPABASE_KEY,
)

# Signin to get supabase jwt
supabase_email = settings.SUPABASE_EMAIL
supabae_password = settings.SUPABASE_PASSWORD
if supabase_email and supabae_password:
    response = supabase_client.auth.sign_in_with_password(
        {"email": supabase_email, "password": supabae_password}
    )
    if response.user and response.session:
        access_token = response.session.access_token
        refresh_token = response.session.refresh_token
        supabase_client.auth.set_session(access_token, refresh_token)


class BucketEnum(Enum):
    CHARACTERS = "characters"
    IMAGE = "image"
    AUDIO = "audio"
    VIDEO = "video"


class StorageService:
    def __init__(self, bucket: BucketEnum) -> None:
        self.bucket = bucket.value
        self.storage = supabase_client.storage

    async def upload_file(
        self,
        path: str,
        data: bytes,
        content_type: str,
        other_bucket: BucketEnum | None = None,
    ) -> str:
        bucket = other_bucket.value if other_bucket else self.bucket
        options = FileOptions({"content-type": content_type, "upsert": "true"})
        await asyncio.to_thread(
            self.storage.from_(bucket).upload,
            f"{path}",
            data,
            options,
        )
        return self.get_signed_url(path)

    def get_signed_url(self, path: str, expire_in: int = 3600) -> str:
        response = self.storage.from_(self.bucket).create_signed_url(path, expire_in)
        return response["signedUrl"]

    def get_public_url(self, path: str) -> str:
        return self.storage.from_(self.bucket).get_public_url(path)
