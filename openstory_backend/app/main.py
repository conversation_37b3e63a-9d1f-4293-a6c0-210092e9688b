import os
import uvicorn
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from app.utils.logger import logger
from app.api.v1.api import api_router
from app.core.config import get_settings
from app.services.image_generation import FalImageGenerationService


settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Initialize services that need to be available during app lifetime
    app.state.image_service = FalImageGenerationService(max_concurrent=2)
    logger.info("Image generation service initialized")
    yield

    # Clean up resources on shutdown
    logger.info("Shutting down services...")
    if hasattr(app.state, "image_service"):
        app.state.image_service.shutdown()
    logger.info("Cleanup complete")


app = FastAPI(
    title=settings.PROJECT_NAME,
    version="1.0.0",  # Hardcoded version since it's not in settings
    lifespan=lifespan,
)

# Set all CORS enabled origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router, prefix=settings.API_V1_STR)

if __name__ == "__main__":
    port = int(os.getenv("PORT", "8000"))
    uvicorn.run("app.main:app", host="0.0.0.0", port=port, reload=True)

# Note: This API uses Supabase JWT authentication.
# Frontend applications should include the Supabase JWT token in the Authorization header
# for protected routes: Authorization: Bearer <token>

