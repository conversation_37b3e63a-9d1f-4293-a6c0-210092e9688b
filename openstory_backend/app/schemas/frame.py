from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel

class FrameBase(BaseModel):
    shot_name: str
    shot_type: str
    shot_setting: str
    shot_framing: str
    shot_angle: str
    shot_movement: str
    characters: List[Dict[str, Any]]
    character_composition: Optional[str] = None
    character_dialogue: Optional[str] = None
    image_url: Optional[str] = None
    status: str = "pending"


class FrameCreate(FrameBase):
    shot_name: str
    shot_type: str
    shot_setting: str
    shot_framing: str
    shot_angle: str
    shot_movement: str
    character_composition: str
    character_dialogue: str
    characters: List[Dict[str, Any]]
    user_id: Optional[UUID] = None
    request_id: str | None = None
    prompt_image: str | None = None
    movie_style: str


class FrameUpdate(FrameBase):
    shot_name: Optional[str] = None
    shot_type: Optional[str] = None
    shot_setting: Optional[str] = None
    shot_framing: Optional[str] = None
    shot_angle: Optional[str] = None
    shot_movement: Optional[str] = None
    character_composition: Optional[str] = None
    character_dialogue: Optional[str] = None
    characters: Optional[List[Dict[str, Any]]] = None
    story_id: str


class FrameInDBBase(FrameBase):
    id: str

    class Config:
        from_attributes = True


class Frame(FrameInDBBase):
    characters: List[Dict[str, Any]]


class FrameInDB(FrameInDBBase):
    pass


class FrameComponentBase(BaseModel):
    component_type: str
    content: Optional[str] = None
    media_url: Optional[str] = None
    start_time_ms: Optional[int] = None
    duration_ms: Optional[int] = None


class FrameComponentCreate(FrameComponentBase):
    frame_id: UUID


class FrameComponentUpdate(BaseModel):
    component_type: Optional[str] = None
    content: Optional[str] = None
    media_url: Optional[str] = None
    start_time_ms: Optional[int] = None
    duration_ms: Optional[int] = None


class FrameComponentInDBBase(FrameComponentBase):
    id: UUID
    frame_id: UUID
    created_at: datetime

    class Config:
        from_attributes = True


class FrameComponent(FrameComponentInDBBase):
    pass


class FrameComponentInDB(FrameComponentInDBBase):
    pass

