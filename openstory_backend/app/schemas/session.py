from pydantic import BaseModel
from typing import Optional, List, Literal
from datetime import datetime
from uuid import UUID

class SessionBase(BaseModel):
    user_id: UUID
    character_progression_id: U<PERSON>D
    impersonated_character_id: Optional[UUID] = None

class SessionCreate(SessionBase):
    pass

class SessionInDB(SessionBase):
    id: UUID
    started_at: datetime

    class Config:
        from_attributes = True

class Session(SessionInDB):
    messages: List["Message"] = []

class MessageBase(BaseModel):
    session_id: UUID
    sender: Literal["user", "character"]
    content: str

class MessageCreate(MessageBase):
    pass

class MessageInDB(MessageBase):
    id: UUID
    timestamp: datetime

    class Config:
        from_attributes = True

class Message(MessageInDB):
    pass

# Update Session model to include messages
Session.update_forward_refs() 