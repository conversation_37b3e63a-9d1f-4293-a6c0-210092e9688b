from typing import List, Optional
from pydantic import BaseModel
from app.schemas.frame import Frame

class AvatarUrls(BaseModel):
    url: str

class StoryBase(BaseModel):
    title: str
    description: Optional[str] = None
    status: str = "pending"

class StoryCreate(StoryBase):
    frames: List[Frame]

class StoryUpdate(StoryBase):
    title: Optional[str] = None
    frames: Optional[List[Frame]] = None

class StoryInDBBase(StoryBase):
    id: str

    class Config:
        from_attributes = True

class Story(StoryInDBBase):
    frames: List[Frame]

class StoryInDB(StoryInDBBase):
    pass
