from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel


class VoiceBase(BaseModel):
    voice_id: Optional[str] = None
    description: Optional[str] = None
    gender: Optional[str] = None
    age: Optional[int] = None
    voice_provider: Optional[str] = None
    example_clip: Optional[str] = None


class VoiceCreate(VoiceBase):
    pass


class VoiceUpdate(VoiceBase):
    pass


class VoiceInDBBase(VoiceBase):
    id: UUID
    created_at: datetime

    class Config:
        from_attributes = True


class Voice(VoiceInDBBase):
    pass


class VoiceInDB(VoiceInDBBase):
    pass 