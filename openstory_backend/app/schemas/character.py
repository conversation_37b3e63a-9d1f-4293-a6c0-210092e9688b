from typing import Optional, Any, Union
from pydantic import BaseModel

class AvatarUrls(BaseModel):
    url: str

class CharacterBase(BaseModel):
    name: str
    gender: Optional[str] = None
    age: Optional[Union[int, str]] = None
    appearance: Optional[str] = None
    personality: Optional[str] = None
    backstory: Optional[str] = None
    oneliner: Optional[str] = None
    voice_id: Optional[str] = None
    creator_user_id: Optional[str] = None
    avatar_urls: Optional[AvatarUrls] = None
    preview_img: Optional[str] = None
    is_public: bool = False
    voice: Optional[Any] = None
    voice_name: Optional[str] = None

class CharacterCreate(CharacterBase):
    visual_style: Optional[str] = None

class CharacterUpdate(CharacterBase):
    name: Optional[str] = None
    story_id: str
    preview_img: Optional[str] = None
    avatar_urls: Optional[AvatarUrls] = None

class CharacterInDBBase(CharacterBase):
    id: str
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    class Config:
        from_attributes = True

class Character(CharacterInDBBase):
    pass

class CharacterInDB(CharacterInDBBase):
    pass 