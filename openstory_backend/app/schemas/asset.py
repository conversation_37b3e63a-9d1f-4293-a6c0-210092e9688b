from pydantic import BaseModel
from typing import Optional
from app.models.asset import AssetType

class AssetBase(BaseModel):
    type: AssetType
    url: str
    character_id: Optional[int] = None
    voice_id: Optional[int] = None

class AssetCreate(AssetBase):
    pass

class AssetUpdate(AssetBase):
    type: Optional[AssetType] = None
    url: Optional[str] = None

class AssetInDBBase(AssetBase):
    id: int

    class Config:
        from_attributes = True

class Asset(AssetInDBBase):
    pass

class AssetInDB(AssetInDBBase):
    pass 