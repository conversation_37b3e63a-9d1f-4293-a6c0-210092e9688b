from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field


class MovieStyleOption(BaseModel):
    tag: str
    options: List[str]


class MovieStyle(BaseModel):
    id: UUID
    styles: MovieStyleOption
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class MovieStyleCreate(BaseModel):
    styles: MovieStyleOption


class MovieStyleUpdate(BaseModel):
    styles: Optional[MovieStyleOption] = None 