from pydantic import BaseModel
from typing import Optional, Literal, List, Dict, Any
from datetime import datetime
from uuid import UUID

class ShotBase(BaseModel):
    episode_id: UUID
    shot_number: int
    visual_type: Literal["image", "video", "image_then_video"] = "image"
    duration_seconds: int = 5
    image_url: Optional[str] = None
    video_url: Optional[str] = None
    audio_url: Optional[str] = None
    description: Optional[str] = None
    camera_angle: Optional[str] = None
    setting: Optional[str] = None
    characters: Optional[List[str]] = None
    actions: Optional[str] = None
    dialogues: Optional[List[Dict[str, str]]] = None
    mood: Optional[str] = None
    lighting: Optional[str] = None
    special_effects: Optional[str] = None
    transitions: Optional[str] = None
    storyboard_data: Optional[Dict[str, Any]] = None

class ShotCreate(ShotBase):
    pass

class ShotUpdate(BaseModel):
    shot_number: Optional[int] = None
    visual_type: Optional[Literal["image", "video", "image_then_video"]] = None
    duration_seconds: Optional[int] = None
    image_url: Optional[str] = None
    video_url: Optional[str] = None
    audio_url: Optional[str] = None
    description: Optional[str] = None
    camera_angle: Optional[str] = None
    setting: Optional[str] = None
    characters: Optional[List[str]] = None
    actions: Optional[str] = None
    dialogues: Optional[List[Dict[str, str]]] = None
    mood: Optional[str] = None
    lighting: Optional[str] = None
    special_effects: Optional[str] = None
    transitions: Optional[str] = None
    storyboard_data: Optional[Dict[str, Any]] = None

class ShotInDB(ShotBase):
    id: UUID
    created_at: datetime

    class Config:
        from_attributes = True

class Shot(ShotInDB):
    components: List["ShotComponent"] = []

class ShotVideoUpdate(BaseModel):
    video_url: str

class ShotAudioUpdate(BaseModel):
    audio_url: str

class ShotComponentBase(BaseModel):
    shot_id: UUID
    component_type: Literal["dialogue", "sound_effect", "music"]
    content: str
    media_url: Optional[str] = None
    start_time_ms: int = 0
    duration_ms: Optional[int] = None

class ShotComponentCreate(ShotComponentBase):
    pass

class ShotComponentUpdate(BaseModel):
    component_type: Optional[Literal["dialogue", "sound_effect", "music"]] = None
    content: Optional[str] = None
    media_url: Optional[str] = None
    start_time_ms: Optional[int] = None
    duration_ms: Optional[int] = None

class ShotComponentInDB(ShotComponentBase):
    id: UUID
    created_at: datetime

    class Config:
        from_attributes = True

class ShotComponent(ShotComponentInDB):
    pass

# Create a response model that includes all shot data
class ShotResponse(Shot):
    pass

# Update Shot model to include components
Shot.update_forward_refs() 