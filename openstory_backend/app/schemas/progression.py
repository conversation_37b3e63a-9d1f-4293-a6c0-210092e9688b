from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
from uuid import UUID

class CharacterProgressionBase(BaseModel):
    character_id: UUID
    episode_id: UUID
    state_description: Optional[str] = None
    cumulative_traits: Optional[Dict[str, Any]] = None
    cumulative_memories: Optional[Dict[str, Any]] = None

class CharacterProgressionCreate(CharacterProgressionBase):
    pass

class CharacterProgressionUpdate(BaseModel):
    state_description: Optional[str] = None
    cumulative_traits: Optional[Dict[str, Any]] = None
    cumulative_memories: Optional[Dict[str, Any]] = None

class CharacterProgressionInDB(CharacterProgressionBase):
    id: UUID
    created_at: datetime

    class Config:
        from_attributes = True

class CharacterProgression(CharacterProgressionInDB):
    pass 