from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.models.asset import AssetType
from app.schemas.asset import Asset, AssetCreate, AssetUpdate
from app.services.asset_service import AssetService

router = APIRouter()

@router.get("/", response_model=List[Asset])
def get_assets(
    skip: int = 0,
    limit: int = 100,
    asset_type: Optional[AssetType] = None,
    character_id: Optional[int] = None,
    voice_id: Optional[int] = None,
    db: Session = Depends(deps.get_db)
):
    """
    Get all assets with optional filtering.
    If no filters are provided, returns assets grouped by type.
    """
    asset_service = AssetService(db)
    return asset_service.get_assets(
        skip=skip,
        limit=limit,
        asset_type=asset_type,
        character_id=character_id,
        voice_id=voice_id
    )

@router.post("/", response_model=Asset)
def create_asset(
    *,
    db: Session = Depends(deps.get_db),
    asset_in: AssetCreate
):
    """
    Create new asset.
    """
    asset_service = AssetService(db)
    return asset_service.create_asset(asset=asset_in)

@router.get("/{asset_id}", response_model=Asset)
def get_asset(
    *,
    db: Session = Depends(deps.get_db),
    asset_id: int
):
    """
    Get asset by ID.
    """
    asset_service = AssetService(db)
    asset = asset_service.get_asset(asset_id=asset_id)
    if not asset:
        raise HTTPException(status_code=404, detail="Asset not found")
    return asset

@router.put("/{asset_id}", response_model=Asset)
def update_asset(
    *,
    db: Session = Depends(deps.get_db),
    asset_id: int,
    asset_in: AssetUpdate
):
    """
    Update asset.
    """
    asset_service = AssetService(db)
    asset = asset_service.update_asset(asset_id=asset_id, asset=asset_in)
    if not asset:
        raise HTTPException(status_code=404, detail="Asset not found")
    return asset

@router.delete("/{asset_id}")
def delete_asset(
    *,
    db: Session = Depends(deps.get_db),
    asset_id: int
):
    """
    Delete asset.
    """
    asset_service = AssetService(db)
    success = asset_service.delete_asset(asset_id=asset_id)
    if not success:
        raise HTTPException(status_code=404, detail="Asset not found")
    return {"status": "success"} 