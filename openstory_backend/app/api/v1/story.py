from typing import List, Optional
from fastapi import APIRouter, HTTPException, status, Depends
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from app.utils.logger import logger
from app.services.video_generation import (
    VideoGenerationResult,
    video_service,
)
from uuid import uuid4
import json
from app.services.story_service import StoryService, StoryCreate, StoryResponse
from app.db.session import get_db
from app.schemas.story import StoryUpdate
from app.schemas.character import Character
from app.services.prompt_service import get_prompt_service
from app.api.v1.llm import get_openai_response

router = APIRouter()
prompt_service = get_prompt_service()
story_service = StoryService()

class GenerateDramaContent(BaseModel):
    user_story: str = Field(..., description="Paragraph describing the drama concept")
    story_type: str = Field(..., description="Type of story to generate")
    model: str = Field("gpt-4o-mini", description="OpenAI model to use")
    temperature: float = Field(0.7, description="Sampling temperature")
    max_tokens: Optional[int] = Field(None, description="Max tokens to generate")

class DramaContent(BaseModel):
    show: Dict[str, Any]
    episodes: List[Dict[str, Any]]
    characters: List[Dict[str, Any]]

class CharacterDialogue(BaseModel):
    character_name: str
    line: str
    emotion: str

class Shot(BaseModel):
    id: str
    shot_number: int
    shot_name: str
    shot_type: str
    shot_setting: str
    shot_framing: str
    shot_angle: str
    shot_movement: str
    character_composition: Optional[Dict[str, str]] = None
    character_dialogue: Optional[CharacterDialogue] = None
    characters: Optional[List[str]] = None

class StoryboardRequest(BaseModel):
    storyline: str = Field(..., description="The main storyline for the storyboard")
    characters: List[Character] = Field(
        ..., description="Character descriptions for the storyboard"
    )
    story_type: str = Field(..., description="The type of story to generate")
    model: str = Field("gpt-4o", description="OpenAI model to use")
    temperature: float = Field(0.7, description="Sampling temperature")
    max_tokens: Optional[int] = Field(None, description="Max tokens to generate")
    
class StoryboardResponse(BaseModel):
    shots: List[Shot] = Field(..., description="List of storyboard shots")

# Helper Functions
async def get_llm_content(
    template_id: str,
    variables: Dict[str, Any],
    model: str,
    temperature: float,
    max_tokens: Optional[int] = None,
) -> Dict[str, Any]:
    """Centralized helper to get LLM content from a template"""
    # Render the template
    rendered = prompt_service.render_template(
        template_id=template_id, variables=variables
    )

    # Prepare the messages
    messages = []
    if rendered["system_prompt"]:
        messages.append({"role": "system", "content": rendered["system_prompt"]})
    messages.append({"role": "user", "content": rendered["user_prompt"]})

    # Call OpenAI
    response = await get_openai_response(
        messages=messages,
        model=model,
        temperature=temperature,
        max_tokens=max_tokens,
        functions=rendered.get("functions"),
    )

    # Handle function call
    if "function_call" not in response:
        raise HTTPException(
            status_code=500, detail="LLM did not return structured data"
        )

    # Parse arguments
    fc = response["function_call"]
    if not isinstance(fc, dict) or "arguments" not in fc:
        raise HTTPException(status_code=500, detail="Invalid function call format")

    # Return the parsed arguments
    return fc["arguments"]


#generate stort by concept
@router.post(
    "/generate-content", response_model=DramaContent, status_code=status.HTTP_200_OK
)
async def generate_drama_content(request: GenerateDramaContent) -> DramaContent:
    """
    Generate drama content (show, episodes, characters) but don't save to database.
    Returns raw content that can be displayed and optionally saved later.
    """
    try:
        # Get content from LLM
        data = await get_llm_content(
            template_id="story_generator",
            variables={
                "user_story": request.user_story,
                "story_type": request.story_type,
            },
            model=request.model,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
        )

        # Return structured content
        return DramaContent(
            show=data["story"],
            episodes=data.get("episodes", []),
            characters=data.get("characters", []),
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to generate content: {str(e)}"
        )

# Add the storyboard endpoint
@router.post(
    "/storyboard", response_model=StoryboardResponse, status_code=status.HTTP_200_OK
)
async def generate_storyboard(request: StoryboardRequest) -> StoryboardResponse:
    """
    Generate a storyboard with shots based on a storyline and character descriptions.
    Returns a structured shot list that can be used for visualization.
    """
    try:
        # Get content from LLM using the storyboard_generator template
        data = await get_llm_content(
            template_id="storyboard_generator",
            variables={
                "storyline": request.storyline,
                "characters": json.dumps(
                    [character.dict() for character in request.characters]
                ),
                "story_type": request.story_type,
            },
            model=request.model,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
        )

        # Process the shots data to ensure all required fields are present
        processed_shots = []
        for shot in data.get("shots", []):
            # Process character_dialogue if it exists
            character_dialogue = None
            if "character_dialogue" in shot and shot["character_dialogue"]:
                dialogue_data = shot["character_dialogue"]
                # Handle different possible formats
                if isinstance(dialogue_data, dict):
                    if "character_name" in dialogue_data:
                        # Already in correct format
                        character_dialogue = dialogue_data
                    else:
                        # Convert from {character_name: {line: "...", emotion: "..."}} format
                        for char_name, dialogue in dialogue_data.items():
                            if isinstance(dialogue, dict):
                                character_dialogue = {
                                    "character_name": char_name,
                                    "line": dialogue.get("line", ""),
                                    "emotion": dialogue.get("emotion", ""),
                                }
                                break

            # Create a base shot with required fields
            processed_shot = {
                "id": str(uuid4()),
                "shot_number": shot["shot_number"],
                "shot_name": shot["shot_name"],
                "shot_type": shot["shot_type"],
                "shot_setting": shot["shot_setting"],
                "shot_framing": shot["shot_framing"],
                "shot_angle": shot["shot_angle"],
                "shot_movement": shot["shot_movement"],
                "character_composition": shot.get("character_composition", {}),
                "character_dialogue": character_dialogue,
                "characters": shot.get("characters", []),
            }
            processed_shots.append(processed_shot)

        # Return structured storyboard content
        return StoryboardResponse(shots=processed_shots)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to generate storyboard: {str(e)}"
        )


@router.post("/create", response_model=VideoGenerationResult)
async def create_story(story: StoryCreate, db: Session = Depends(get_db)) -> VideoGenerationResult:
    """Generate video from frame data."""
    try:
        logger.info(f"Received story data: {story.dict()}")
        return await video_service.generate_story_video(story.dict())
    except Exception as e:
        logger.error(f"Error generating video: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/getStoriesByUserId", response_model=List[StoryResponse])
async def get_stories_by_user_id(
    user_id: str,
    skip: int = 0,
    limit: int = 100,
    # current_user: User = Depends(get_current_active_user)
):

    try:
        styles = story_service.get_stories_by_user_id(user_id,skip, limit)
        return styles
    except Exception as e:
        logger.error(f"Error fetching movie styles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching movie styles.",
        )

@router.post("/save", response_model=dict)
async def save_story(data: dict):
    try:
        
        story_id = story_service.save_story(data)
        return {"id": story_id}
    except Exception as e:
        logger.error(f"Error saving story: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/update", response_model=dict)
async def update_story(data: dict):
    try:
        story_id = story_service.update_story(data)
        return {"id": story_id}
    except Exception as e:
        logger.error(f"Error updating story: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/stories/", response_model=StoryResponse)
def create_story(story: StoryCreate, db: Session = Depends(get_db)):
    story_service = StoryService(db)
    return story_service.save_story(story)

@router.get("/stories/", response_model=List[StoryResponse])
def read_stories(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    story_service = StoryService(db)
    return story_service.get_stories(skip=skip, limit=limit, status=status)

@router.get("/stories/{story_id}", response_model=StoryResponse)
def read_story(story_id: str, db: Session = Depends(get_db)):
    story_service = StoryService(db)
    story = story_service.get_story(story_id)
    if story is None:
        raise HTTPException(status_code=404, detail="Story not found")
    return story

@router.put("/stories/{story_id}", response_model=StoryResponse)
def update_story(story_id: str, story: StoryUpdate, db: Session = Depends(get_db)):
    story_service = StoryService(db)
    updated_story = story_service.update_story(story_id, story)
    if updated_story is None:
        raise HTTPException(status_code=404, detail="Story not found")
    return updated_story

@router.delete("/stories/{story_id}")
def delete_story(story_id: str, db: Session = Depends(get_db)):
    story_service = StoryService(db)
    if not story_service.delete_story(story_id):
        raise HTTPException(status_code=404, detail="Story not found")
    return {"message": "Story deleted successfully"}
