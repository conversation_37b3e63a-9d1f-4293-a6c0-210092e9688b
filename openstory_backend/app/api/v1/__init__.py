from fastapi import APIRouter
from app.api.v1.auth import router as auth_router
from app.api.v1.characters import router as characters_router
from app.api.v1.sessions import router as sessions_router
from app.api.v1.llm import router as llm_router
from app.api.v1.movie_styles import router as movie_styles_router
from app.api.v1.assets import router as assets_router
from app.api.v1.frame import router as frame_router
from app.api.v1.story import router as story_router
from app.api.v1.healthcheck import router as healthcheck_router

api_router = APIRouter()

# Include all routers

# DataBase routers
api_router.include_router(healthcheck_router, prefix="/health", tags=["health"])

# Auth routers
api_router.include_router(auth_router, prefix="/auth", tags=["auth"])
api_router.include_router(sessions_router, prefix="/sessions", tags=["sessions"])

# Assets routers
api_router.include_router(assets_router, prefix="/assets", tags=["assets"])

# Characters routers
api_router.include_router(characters_router, prefix="/characters", tags=["characters"])

# Create Jobs routers
api_router.include_router(llm_router, prefix="/llm", tags=["llm"])
api_router.include_router(movie_styles_router, prefix="/movie-styles", tags=["movie-styles"])
api_router.include_router(frame_router, prefix="/frames", tags=["frames"])
api_router.include_router(story_router, prefix="/stories", tags=["stories"]) 