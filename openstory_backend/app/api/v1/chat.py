import json
import asyncio
from typing import AsyncGenerator, List, Dict
from datetime import datetime, timezone

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from openai import AsyncOpenAI, OpenAIError

from app.utils.logger import logger
from app.core.config import get_settings
from app.schemas.chat import ChatR<PERSON><PERSON>, ChatResponse
from app.utils.llm_logger import (
    log_openai_request,
    log_openai_response,
    log_openai_stream_complete,
)
from app.services.agent_service import story_agent_service

# Configuration
router = APIRouter()
settings = get_settings()
openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)


async def stream_chat_response(
    messages: List[Dict[str, str]],
    model: str,
    temperature: float,
    max_tokens: int,
    user_id: str,
) -> AsyncGenerator[str, None]:
    """Stream chat response from OpenAI"""

    stream_params = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens,
        "stream": True,
    }

    try:
        # Log the request
        request_metadata = log_openai_request(
            model=model,
            messages=messages,
            functions=None,
            temperature=temperature,
            max_tokens=max_tokens,
            user_id=user_id,
            streaming=True,
        )

        logger.info(f"Sending chat stream request to OpenAI: model={model}")
        stream = await openai_client.chat.completions.create(**stream_params)

        # Log that streaming has started
        log_openai_response(
            response="Chat streaming started",
            request_metadata=request_metadata,
            is_streaming=True,
        )

        full_content = ""

        async for chunk in stream:
            delta = chunk.choices[0].delta

            # Stream content
            if delta and delta.content:
                full_content += delta.content
                yield f"data: {json.dumps({'type': 'content', 'data': delta.content})}\n\n"

            # Check finish reason
            finish_reason = chunk.choices[0].finish_reason
            if finish_reason:
                yield f"data: {json.dumps({'type': 'finish', 'reason': finish_reason, 'full_content': full_content})}\n\n"

                # Log completion
                log_openai_stream_complete(request_metadata)
                break

    except OpenAIError as e:
        logger.error(f"OpenAI API Error: {e}")
        yield f"data: {json.dumps({'type': 'error', 'detail': f'OpenAI API Error: {e}'})}\n\n"
    except Exception as e:
        logger.error(f"Internal Server Error: {e}")
        yield f"data: {json.dumps({'type': 'error', 'detail': f'Internal Server Error: {e}'})}\n\n"


@router.post("/stream", response_class=StreamingResponse)
async def chat_stream(
    request: ChatRequest,
) -> StreamingResponse:
    """
    Stream chat response using Server-Sent Events
    """
    logger.info("Chat stream request received")

    try:
        # Build messages for OpenAI API
        messages = []

        # Add system message for story creation context
        system_message = {
            "role": "system",
            "content": "You are OpenStory AI, a helpful assistant specialized in creative storytelling, character development, and narrative creation. You help users create engaging stories, develop characters, and provide creative writing assistance. Be friendly, creative, and supportive.",
        }
        messages.append(system_message)

        # Add conversation history
        for msg in request.conversation_history:
            messages.append({"role": msg.role, "content": msg.content})

        # Add current user message
        messages.append({"role": "user", "content": request.message})

        # Return streaming response
        return StreamingResponse(
            stream_chat_response(
                messages=messages,
                model=request.model,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                user_id="anonymous",  # 暂时使用匿名用户
            ),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control",
            },
        )

    except Exception as e:
        logger.exception(f"Chat stream error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.post("/", response_model=ChatResponse)
async def chat_complete(
    request: ChatRequest,
) -> ChatResponse:
    """
    Get complete chat response (non-streaming)
    """
    logger.info("Chat completion request received")

    try:
        # Build messages for OpenAI API
        messages = []

        # Add system message
        system_message = {
            "role": "system",
            "content": "You are OpenStory AI, a helpful assistant specialized in creative storytelling, character development, and narrative creation. You help users create engaging stories, develop characters, and provide creative writing assistance. Be friendly, creative, and supportive.",
        }
        messages.append(system_message)

        # Add conversation history
        for msg in request.conversation_history:
            messages.append({"role": msg.role, "content": msg.content})

        # Add current user message
        messages.append({"role": "user", "content": request.message})

        # Log the request
        request_metadata = log_openai_request(
            model=request.model,
            messages=messages,
            functions=None,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            user_id="anonymous",  # 暂时使用匿名用户
            streaming=False,
        )

        # Call OpenAI API
        response = await openai_client.chat.completions.create(
            model=request.model,
            messages=messages,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
        )

        # Log the response
        log_openai_response(response=response, request_metadata=request_metadata)

        # Extract response content
        assistant_message = response.choices[0].message.content

        return ChatResponse(
            message=assistant_message, timestamp=datetime.now(timezone.utc)
        )

    except OpenAIError as e:
        logger.error(f"OpenAI API Error: {e}")
        raise HTTPException(status_code=500, detail=f"OpenAI API Error: {e}")
    except Exception as e:
        logger.exception(f"Chat completion error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


async def stream_agent_response(message: str, session_id: str = "default"):
    """Stream agent response with interactive confirmation"""
    try:
        logger.info("Agent interactive stream request received")

        # Use interactive chat instead of regular chat
        response = await story_agent_service.interactive_chat(message, session_id)

        if isinstance(response, dict):
            if response.get("type") == "confirmation_request":
                # Send confirmation request
                yield f"data: {json.dumps({'type': 'confirmation_request', 'data': response['message'], 'confirmation_id': response['confirmation_id']})}\n\n"
                yield f"data: {json.dumps({'type': 'finish', 'reason': 'confirmation_needed'})}\n\n"
            else:
                # Send regular response
                message_text = response.get("message", str(response))
                # Simulate streaming by chunking the response
                chunk_size = 50
                for i in range(0, len(message_text), chunk_size):
                    chunk = message_text[i : i + chunk_size]
                    yield f"data: {json.dumps({'type': 'content', 'data': chunk})}\n\n"
                    await asyncio.sleep(0.1)
                yield f"data: {json.dumps({'type': 'finish', 'reason': 'stop'})}\n\n"
        else:
            # Handle string response
            message_text = str(response)
            chunk_size = 50
            for i in range(0, len(message_text), chunk_size):
                chunk = message_text[i : i + chunk_size]
                yield f"data: {json.dumps({'type': 'content', 'data': chunk})}\n\n"
                await asyncio.sleep(0.1)
            yield f"data: {json.dumps({'type': 'finish', 'reason': 'stop'})}\n\n"

    except Exception as e:
        logger.error(f"Agent interactive stream error: {e}")
        yield f"data: {json.dumps({'type': 'error', 'detail': f'Agent Error: {e}'})}\n\n"


@router.post("/agent/stream", response_class=StreamingResponse)
async def agent_stream(
    request: ChatRequest,
) -> StreamingResponse:
    """
    Stream chat response using LlamaIndex Agent with interactive tool confirmation
    """
    logger.info("Agent interactive stream request received")

    try:
        # Generate session_id from conversation history or use default
        session_id = (
            f"session_{hash(str(request.conversation_history))}"
            if request.conversation_history
            else "default"
        )

        # Return streaming response
        return StreamingResponse(
            stream_agent_response(request.message, session_id),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control",
            },
        )

    except Exception as e:
        logger.exception(f"Agent stream error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.post("/agent", response_model=ChatResponse)
async def agent_chat(
    request: ChatRequest,
) -> ChatResponse:
    """
    Get complete agent response (non-streaming) using LlamaIndex Agent with interactive confirmation
    """
    logger.info("Agent interactive chat request received")

    try:
        # Generate session_id from conversation history or use default
        session_id = (
            f"session_{hash(str(request.conversation_history))}"
            if request.conversation_history
            else "default"
        )

        # Get response from agent
        response = await story_agent_service.interactive_chat(
            request.message, session_id
        )

        if isinstance(response, dict):
            response_message = response.get("message", str(response))
        else:
            response_message = str(response)

        return ChatResponse(
            message=response_message, timestamp=datetime.now(timezone.utc)
        )

    except Exception as e:
        logger.exception(f"Agent chat error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")
