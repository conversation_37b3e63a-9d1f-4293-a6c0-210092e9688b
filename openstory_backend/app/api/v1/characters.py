from fastapi import APIRouter, Depends, HTTPException, status
from typing import List
from uuid import UUID
from app.api.v1.deps import get_current_active_user, verify_owner_permission
from app.schemas.character import (
    Character,
    CharacterCreate,
    CharacterUpdate,
    CharacterCreate,
)

from app.schemas.user import User
from app.services.character_services import (
    CharacterService
)

router = APIRouter()
character_service = CharacterService()

# Character Endpoints

@router.post("/", response_model=Character, status_code=status.HTTP_201_CREATED)
async def create_character(
    character: CharacterCreate, current_user: User = Depends(get_current_active_user)
):
    """Create a new character."""
    return await character_service.create(current_user.id, character)


@router.post("/create", response_model=Character, status_code=status.HTTP_201_CREATED)
async def create_character(characterData: CharacterCreate):
    """Create a new character."""
    return await character_service.create(characterData)


@router.get("/getCharactersByUserId", response_model=List[Character])
async def get_by_user_id(
    user_id,
    skip: int = 0,
    limit: int = 100,
):
    """Get all characters created by the current user."""
    return character_service.get_by_user_id(user_id, skip, limit)


@router.get("/{character_id}", response_model=Character)
async def get_character(
    character_id: UUID, current_user: User = Depends(get_current_active_user)
):
    """Get a specific character by ID."""
    character = character_service.get_by_id(character_id)
    if not character:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Character not found"
        )

    # For characters, we might want to allow public access in the future
    # but for now, only the creator can access
    await verify_owner_permission(character.creator_user_id, current_user)

    return character


@router.put("/{character_id}", response_model=Character)
async def update_character(
    character_id: UUID,
    character_update: CharacterUpdate,
    current_user: User = Depends(get_current_active_user),
):
    """Update a character."""
    character = character_service.get_by_id(character_id)
    if not character:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Character not found"
        )

    # Check if user is the creator
    await verify_owner_permission(character.creator_user_id, current_user)

    updated_character = character_service.update(character_id, character_update)
    if not updated_character:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update character",
        )

    return updated_character


@router.delete("/{character_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_character(
    character_id: UUID, current_user: User = Depends(get_current_active_user)
):
    """Delete a character."""
    character = character_service.get_by_id(character_id)
    if not character:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Character not found"
        )

    # Check if user is the creator
    await verify_owner_permission(character.creator_user_id, current_user)

    if not character_service.delete(character_id):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete character",
        )
