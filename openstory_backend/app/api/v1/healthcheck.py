from fastapi import APIRouter, Depends
from typing import Dict, Any

from app.utils.logger import logger
from app.core.config import get_settings
from app.db.session import get_db


router = APIRouter(tags=["health"])


@router.get("/", summary="Health check endpoint")
async def health_check() -> Dict[str, Any]:
    """
    Health check endpoint for the API.
    Returns basic information about the application status.
    This endpoint doesn't require authentication.
    """
    settings = get_settings()

    return {
        "status": "ok",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT,
        "message": "Drama API is running",
    }


@router.get("/db", summary="Database health check")
async def db_health_check(db=Depends(get_db)) -> Dict[str, Any]:
    """
    Checks database connectivity.
    Returns status of the database connection.
    This endpoint doesn't require authentication.
    """
    try:
        # Try to execute a simple query to check DB connectivity
        db.execute("SELECT 1")
        return {"status": "ok", "message": "Database connection successful"}
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        return {"status": "error", "message": f"Database connection failed: {str(e)}"}

