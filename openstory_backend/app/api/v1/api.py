from fastapi import APIRouter

from app.api.v1 import (
    assets,
    auth,
    healthcheck,
    llm,
    chat,
    characters,
    sessions,
    movie_styles,
    frame,
    story,
)

api_router = APIRouter()
api_router.include_router(healthcheck.router, prefix="/health", tags=["health"])
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(characters.router, prefix="/characters", tags=["characters"])
api_router.include_router(sessions.router, prefix="/sessions", tags=["sessions"])
api_router.include_router(llm.router, prefix="/llm", tags=["llm"])
api_router.include_router(chat.router, prefix="/chat", tags=["chat"])
api_router.include_router(movie_styles.router, prefix="/movie-styles", tags=["movie-styles"])
api_router.include_router(frame.router, prefix="/frames", tags=["frames"])
api_router.include_router(story.router, prefix="/stories", tags=["stories"])
api_router.include_router(assets.router, prefix="/assets", tags=["assets"])
