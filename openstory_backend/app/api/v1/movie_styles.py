from typing import List
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status

from app.utils.logger import logger
from app.schemas.movie_style import (
    MovieStyle,
    MovieStyleCreate,
    MovieStyleUpdate,
)
from app.api.v1.deps import get_current_active_user
from app.schemas.user import User
from app.services.movie_style_service import MovieStyleService


router = APIRouter()
movie_style_service = MovieStyleService()


@router.get("/", response_model=List[MovieStyle])
async def read_movie_styles(
    skip: int = 0,
    limit: int = 100,
    # current_user: User = Depends(get_current_active_user)
):
    """
    Get all movie styles.
    Requires valid Supabase JWT token in Authorization header.
    Example: Authorization: Bearer <your_jwt_token>
    """
    try:
        styles = movie_style_service.get_movie_styles(skip, limit)
        logger.info(f"Found {len(styles)} movie styles")
        return styles
    except Exception as e:
        logger.error(f"Error fetching movie styles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching movie styles.",
        )


@router.post("/", response_model=MovieStyle)
async def create_movie_style(
    movie_style_in: MovieStyleCreate,
    current_user: User = Depends(get_current_active_user),
):
    """
    Create new movie style.
    """
    try:
        return movie_style_service.create_movie_style(movie_style_in)
    except Exception as e:
        logger.error(f"Error creating movie style: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while creating the movie style.",
        )


@router.get("/{movie_style_id}", response_model=MovieStyle)
async def read_movie_style(
    movie_style_id: UUID, current_user: User = Depends(get_current_active_user)
):
    """
    Get movie style by ID.
    """
    try:
        movie_style = movie_style_service.get_movie_style(movie_style_id)
        if not movie_style:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Movie style not found"
            )
        return movie_style
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching movie style: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching the movie style.",
        )


@router.put("/{movie_style_id}", response_model=MovieStyle)
async def update_movie_style(
    movie_style_id: UUID,
    movie_style_in: MovieStyleUpdate,
    current_user: User = Depends(get_current_active_user),
):
    """
    Update a movie style.
    """
    try:
        movie_style = movie_style_service.update_movie_style(
            movie_style_id, movie_style_in
        )
        if not movie_style:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Movie style not found"
            )
        return movie_style
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating movie style: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating the movie style.",
        )


@router.delete("/{movie_style_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_movie_style(
    movie_style_id: UUID, current_user: User = Depends(get_current_active_user)
):
    """
    Delete a movie style.
    """
    try:
        if not movie_style_service.delete_movie_style(movie_style_id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Movie style not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting movie style: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while deleting the movie style.",
        )

