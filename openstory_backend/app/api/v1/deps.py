from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OA<PERSON>2P<PERSON><PERSON><PERSON>earer
from typing import Dict, Any
from uuid import UUID
from supabase import create_client, Client as SupabaseClient
from functools import lru_cache

from app.utils.logger import logger
from app.core.security import verify_supabase_token, log_token_payload
from app.core.config import get_settings
from app.services.auth import AuthService
from app.services.user import UserService
from app.schemas.user import User


# Dependency to get Supabase client instance
@lru_cache()
def get_supabase_client_cached() -> SupabaseClient:
    settings = get_settings()
    logger.debug(f"Creating cached Supabase client for URL: {settings.SUPABASE_URL}")
    return create_client(settings.SUPABASE_URL, settings.SUPABASE_KEY)


# The actual dependency function FastAPI will use
def get_supabase_client() -> SupabaseClient:
    logger.debug("Retrieving Supabase client")
    return get_supabase_client_cached()


oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/token")


async def get_current_user(token: str = Depends(oauth2_scheme)) -> User:
    """Dependency to get the current authenticated user."""
    logger.info(
        f"=== get_current_user dependency called with token: {token[:10]}... ==="
    )
    auth_service = AuthService()
    try:
        user = await auth_service.get_current_user(token)
        if not user:
            logger.error("Auth service returned no user for token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        logger.info(f"Successfully retrieved user: {user.id}")
        return user
    except Exception as e:
        logger.exception(f"Exception in get_current_user: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication error: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_supabase_user(request: Request) -> Dict[str, Any]:
    """Dependency to get the validated Supabase JWT payload."""
    logger.info("=== get_supabase_user dependency called ===")
    try:
        payload = await verify_supabase_token(request)
        logger.info("Supabase token validation successful")
        return payload
    except Exception as e:
        logger.exception(f"Exception in get_supabase_user: {str(e)}")
        raise


async def get_current_user_from_supabase(
    payload: Dict[str, Any] = Depends(get_supabase_user),
) -> User:
    """
    Dependency to get the current user based on Supabase JWT.
    Verifies user exists in our database and creates profile if it's their first time.
    """
    logger.info("=== get_current_user_from_supabase called ===")
    log_token_payload(payload, "Supabase payload")

    user_service = UserService()
    user_id = payload.get("sub")
    email = payload.get("email")
    username = payload.get("user_metadata", {}).get("username")

    logger.info(
        f"Token data - user_id: {user_id[:8] if user_id else 'None'}, email: {email[:4] if email else 'None'}, username: {username or 'None'}"
    )

    if not user_id or not email:
        logger.error(f"Missing sub or email in token payload")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid user ID or email in token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        logger.info(f"Looking up user by ID: {user_id[:8]}...")
        user = user_service.get_user_by_id(user_id)
        logger.info(f"User lookup result: {'Found' if user else 'Not found'}")

        # if not user:
        #     logger.info(f"User {user_id[:8]}... not found locally, creating profile")
        #     try:
        #         user_data_to_create = {
        #             "id": user_id,
        #             "email": email,
        #             "username": username or f"user_{user_id[:8]}"
        #         }
        #         logger.info(f"Creating user with data: {json.dumps(user_data_to_create)}")
        #         user = user_service.create_user(user_data_to_create)
        #         logger.info(f"Successfully created local profile for user {user_id[:8]}...")
        #     except Exception as e:
        #         logger.exception(f"Failed to create local user profile: {str(e)}")
        #         raise HTTPException(
        #             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        #             detail=f"Could not create user profile after authentication: {str(e)}",
        #             headers={"WWW-Authenticate": "Bearer"},
        #         )

        if not user:
            logger.error(f"User not found or could not be created {user_id}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"User not found or could not be created",
                headers={"WWW-Authenticate": "Bearer"},
            )

        logger.info(f"Successfully retrieved/created user: {user.id}")
        return user
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(
            f"Unexpected error in get_current_user_from_supabase: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"User authentication error: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: User = Depends(get_current_user_from_supabase),
) -> User:
    """Dependency to get the current authenticated and active user."""
    logger.info(f"=== get_current_active_user called for user: {current_user.id} ===")
    return current_user


async def check_owner_permission(
    resource_owner_id: UUID,
    current_user: User = Depends(get_current_user_from_supabase),
) -> bool:
    """Check if the current user is the owner of a resource."""
    is_owner = str(resource_owner_id) == str(current_user.id)
    logger.info(
        f"Permission check: user {current_user.id} is {'owner' if is_owner else 'not owner'} of resource {resource_owner_id}"
    )
    return is_owner


async def verify_owner_permission(
    resource_owner_id: UUID,
    current_user: User = Depends(get_current_user_from_supabase),
) -> None:
    """Verify the current user is the owner of a resource, raising an exception if not."""
    if str(resource_owner_id) != str(current_user.id):
        logger.warning(
            f"Permission denied: user {current_user.id} attempted to access resource owned by {resource_owner_id}"
        )
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to access this resource",
        )

