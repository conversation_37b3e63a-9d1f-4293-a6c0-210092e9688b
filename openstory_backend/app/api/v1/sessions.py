from fastapi import APIRouter, Depends, HTTPException, status
from typing import List
from uuid import UUID
from app.api.v1.deps import get_current_active_user
from app.schemas.session import Session, SessionCreate, Message, MessageCreate
from app.schemas.user import User
from app.services.session import SessionService, MessageService

router = APIRouter()
session_service = SessionService()
message_service = MessageService()

@router.post("/", response_model=Session, status_code=status.HTTP_201_CREATED)
async def create_session(
    session: SessionCreate,
    current_user: User = Depends(get_current_active_user)
):
    """Create a new chat session with a character progression."""

    # Override the user_id with the current user's ID for security
    session_data = session.model_dump()
    session_data["user_id"] = current_user.id
    
    # Create the session
    created_session = session_service.create(SessionCreate(**session_data))
    if not created_session:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create session"
        )
    
    return created_session

@router.get("/", response_model=List[Session])
async def get_user_sessions(
    current_user: User = Depends(get_current_active_user)
):
    """Get all sessions for the current user."""
    return session_service.get_by_user_id(current_user.id)

@router.get("/{session_id}", response_model=Session)
async def get_session(
    session_id: UUID,
    current_user: User = Depends(get_current_active_user)
):
    """Get a specific session by ID."""
    session = session_service.get_by_id(session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    # Check if the user owns the session
    if str(session.user_id) != str(current_user.id):
        # For sessions, we might want to allow other permissions in the future,
        # but for now, only the session creator can access it
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return session

@router.delete("/{session_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_session(
    session_id: UUID,
    current_user: User = Depends(get_current_active_user)
):
    """Delete a session."""
    session = session_service.get_by_id(session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    # Check if the user owns the session
    if str(session.user_id) != str(current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    if not session_service.delete(session_id):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete session"
        )

# Messages

@router.post("/messages", response_model=Message, status_code=status.HTTP_201_CREATED)
async def create_message(
    message: MessageCreate,
    current_user: User = Depends(get_current_active_user)
):
    """Create a new message in a session."""
    # Verify the session exists and the user owns it
    session = session_service.get_by_id(message.session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    if str(session.user_id) != str(current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Create the message
    created_message = message_service.create(message)
    
    # TODO: If sender is "user", we would typically trigger an AI response here
    
    return created_message

@router.get("/messages/{session_id}", response_model=List[Message])
async def get_session_messages(
    session_id: UUID,
    current_user: User = Depends(get_current_active_user)
):
    """Get all messages for a specific session."""
    # Verify the session exists and the user owns it
    session = session_service.get_by_id(session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    if str(session.user_id) != str(current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return message_service.get_by_session_id(session_id)

@router.delete("/messages/{message_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_message(
    message_id: UUID,
    current_user: User = Depends(get_current_active_user)
):
    """Delete a message."""
    message = message_service.get_by_id(message_id)
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Message not found"
        )
    
    # Verify the user owns the session that contains this message
    session = session_service.get_by_id(message.session_id)
    if str(session.user_id) != str(current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    if not message_service.delete(message_id):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete message"
        ) 