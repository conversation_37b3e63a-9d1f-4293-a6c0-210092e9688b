from fastapi import APIRouter, HTTPException
from app.services.video_generation import (
    VideoGenerationConfig,
    VideoGenerationResult,
    video_service,
)

from app.utils.logger import logger
from app.schemas.frame import FrameCreate


router = APIRouter()


def convert_to_config(frame: FrameCreate) -> VideoGenerationConfig:
    return VideoGenerationConfig(
        shot_name=frame.shot_name,
        shot_type=frame.shot_type,
        shot_setting=frame.shot_setting,
        shot_framing=frame.shot_framing,
        shot_angle=frame.shot_angle,
        shot_movement=frame.shot_movement,
        character_composition=frame.character_composition,
        character_dialogue=frame.character_dialogue,
        characters=frame.characters,
        request_id=frame.request_id,
        prompt_image=frame.prompt_image,
        user_id="temp_user",
        movie_style=frame.movie_style
    )


@router.post("/create_frame", response_model=VideoGenerationResult)
def create_frame(frame: FrameCreate) -> VideoGenerationResult:
    """Generate video from frame data."""
    try:
        video_config = convert_to_config(frame)
        return video_service.generate_frame(video_config)
    except Exception as e:
        logger.error(f"Error generating video: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
