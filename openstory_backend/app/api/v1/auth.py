from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordBearer
from app.services.auth import AuthService
from app.schemas.auth import Token
from app.schemas.user import User
from app.api.v1.deps import get_current_user_from_supabase, get_supabase_user
from typing import Optional, Dict, Any, Tuple
import logging

router = APIRouter()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/refresh")

@router.post("/signout")
async def sign_out(token: str = Depends(oauth2_scheme)):
    """Handles server-side session invalidation with Supabase if needed."""
    auth_service = AuthService()
    success = await auth_service.sign_out(token)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to sign out server-side session"
        )
    
    return {"message": "Server-side sign out attempted"}

@router.post("/refresh", response_model=Token)
async def refresh_token_route(refresh_token: str):
    """Refreshes the access token using a refresh token."""
    auth_service = AuthService()
    new_access_token, error = await auth_service.refresh_token(refresh_token)
    
    if error:
        # Make sure error detail is a string
        error_detail = str(error) if error else "Unknown refresh error"
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_detail
        )
    
    return Token(
        access_token=new_access_token,
        refresh_token=refresh_token
    )

@router.get("/me", response_model=Dict[str, Any])
async def get_me_route(payload: Dict[str, Any] = Depends(get_supabase_user)):
    """Returns the raw validated JWT payload for the current user."""
    return payload

@router.get("/profile", response_model=User)
async def get_profile_route(current_user: User = Depends(get_current_user_from_supabase)):
    """
    Returns the local user profile associated with the validated JWT.
    Will create the local profile on first access.
    
    Requires Authorization: Bearer <supabase_jwt_token>
    """
    return current_user 