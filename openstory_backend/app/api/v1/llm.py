import json
import os
import re
from typing import Any, Dict, Optional, List, AsyncGenerator

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from openai import AsyncOpenAI, OpenAIError

from app.utils.logger import logger
from app.api.v1.deps import get_current_user_from_supabase
from app.core.config import get_settings
from app.schemas.user import User
from app.services.prompt_service import get_prompt_service, PromptService
from app.utils.llm_logger import (
    log_openai_request,
    log_openai_response,
    log_openai_stream_complete,
)


# --- Configuration & Initialization ---
router = APIRouter()
settings = get_settings()

# Updated initialization to be compatible with newer OpenAI SDK
openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
PROMPTS_DIR = os.path.join(os.path.dirname(__file__), "..", "prompts")


# --- Pydantic Models ---
class LLMRequest(BaseModel):
    """Request model for LLM API calls"""

    template_id: str = Field(..., description="The identifier of the prompt template")
    variables: Dict[str, Any] = Field(
        {}, description="Variables to be used in the template"
    )
    model: str = Field("gpt-4o-mini", description="The OpenAI model to use")
    temperature: float = Field(0.7, description="Sampling temperature")
    max_tokens: Optional[int] = Field(
        None, description="Maximum number of tokens to generate"
    )
    stream: bool = Field(True, description="Whether to stream the response")


class PromptData(BaseModel):
    system_prompt_template: Optional[str] = None
    user_prompt_template: str
    functions: Optional[List[Dict[str, Any]]] = None


# --- Helper Functions ---
def load_prompt(prompt_id: str) -> PromptData:
    """Loads prompt data from a JSON file."""
    file_path = os.path.join(PROMPTS_DIR, f"{prompt_id}.json")
    logger.info(f"Loading prompt from: {file_path}")

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail=f"Prompt '{prompt_id}' not found.")

    try:
        with open(file_path, "r") as f:
            data = json.load(f)
        return PromptData(**data)
    except json.JSONDecodeError:
        raise HTTPException(
            status_code=500,
            detail=f"Error reading prompt file '{prompt_id}'. Invalid JSON.",
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Could not load prompt '{prompt_id}': {e}"
        )


def format_prompt(template: Optional[str], variables: Dict[str, Any]) -> Optional[str]:
    """Formats a prompt template string with given variables."""
    if template is None:
        return None

    try:
        return template.format(**variables)
    except KeyError as e:
        raise HTTPException(
            status_code=400, detail=f"Missing required variable '{e}' for the prompt."
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error formatting prompt: {e}")


def extract_json_from_text(text: str) -> Optional[Dict[str, Any]]:
    """
    Extracts JSON from text content if function calling fails.
    Looks for content between ```json and ``` markers or any JSON-like structure.
    """
    if not text:
        return None

    # Try to find JSON between markdown code blocks
    json_pattern = r"```(?:json)?\s*([\s\S]*?)```"
    matches = re.findall(json_pattern, text)

    # Try each match
    for match in matches:
        try:
            return json.loads(match.strip())
        except json.JSONDecodeError:
            continue

    # If no matches, look for anything that looks like JSON object
    try:
        # Find text that starts with { and ends with }
        curly_pattern = r"\{[\s\S]*\}"
        curly_matches = re.findall(curly_pattern, text)

        for match in curly_matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue

        return None
    except Exception:
        return None


def parse_function_call(function_call: Any) -> Dict[str, Any]:
    """Safely parse an OpenAI function call object into a standardized dictionary."""
    # Default result structure
    result = {"name": "unknown", "arguments": {}}

    if function_call is None:
        return result

    # Extract name
    if hasattr(function_call, "name"):
        result["name"] = function_call.name
    elif isinstance(function_call, dict) and "name" in function_call:
        result["name"] = function_call["name"]

    # Extract arguments
    arguments = None
    if hasattr(function_call, "arguments"):
        arguments = function_call.arguments
    elif isinstance(function_call, dict) and "arguments" in function_call:
        arguments = function_call["arguments"]

    # Parse arguments
    if arguments:
        if isinstance(arguments, str):
            try:
                result["arguments"] = json.loads(arguments)
            except json.JSONDecodeError:
                result["arguments"] = {"error": "Failed to parse arguments JSON"}
        elif isinstance(arguments, dict):
            result["arguments"] = arguments

    return result


# --- Streaming Logic ---
async def stream_openai_response(
    messages: List[Dict[str, str]],
    model: str,
    temperature: float,
    max_tokens: Optional[int],
    functions: Optional[List[Dict[str, Any]]],
    user_id: Optional[str] = None,
) -> AsyncGenerator[str, None]:
    """Calls OpenAI API and streams the response chunks"""
    stream_params = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "stream": True,
    }

    if max_tokens is not None:
        stream_params["max_tokens"] = max_tokens

    if functions:
        stream_params["functions"] = functions
        stream_params["function_call"] = {"name": functions[0]["name"]}
        logger.info(f"Function call enabled: {functions[0]['name']}")

    try:
        # Log the OpenAI request before sending
        request_metadata = log_openai_request(
            model=model,
            messages=messages,
            functions=functions,
            temperature=temperature,
            max_tokens=max_tokens,
            user_id=user_id,
            streaming=True,
        )

        logger.info(f"Sending streaming request to OpenAI: model={model}")
        stream = await openai_client.chat.completions.create(**stream_params)

        # Log that streaming has started
        log_openai_response(
            response="Streaming started",
            request_metadata=request_metadata,
            is_streaming=True,
        )

        full_content = ""
        function_call_detected = False

        async for chunk in stream:
            delta = chunk.choices[0].delta

            # Stream content
            if delta and delta.content:
                full_content += delta.content
                yield f"data: {json.dumps({'type': 'content', 'data': delta.content})}\n\n"

            # Stream function call
            if delta and delta.function_call:
                function_call_detected = True
                name = delta.function_call.name
                args = delta.function_call.arguments

                if name:
                    yield f"data: {json.dumps({'type': 'function_start', 'name': name})}\n\n"

                if args:
                    yield f"data: {json.dumps({'type': 'function_args', 'args_chunk': args})}\n\n"

            # Check finish reason
            finish_reason = chunk.choices[0].finish_reason
            if finish_reason:
                # Try to extract JSON if needed
                if functions and not function_call_detected:
                    extracted_json = extract_json_from_text(full_content)
                    if extracted_json:
                        function_name = (
                            functions[0]["name"] if functions else "extract_json"
                        )
                        yield f"data: {json.dumps({'type': 'function_start', 'name': function_name})}\n\n"
                        yield f"data: {json.dumps({'type': 'function_args', 'args_chunk': json.dumps(extracted_json)})}\n\n"

                yield f"data: {json.dumps({'type': 'finish', 'reason': finish_reason})}\n\n"

                # Log completion of streaming
                log_openai_stream_complete(request_metadata)
                break

    except OpenAIError as e:
        # Log the error
        log_openai_response(
            response=str(e),
            request_metadata=request_metadata
            if "request_metadata" in locals()
            else {"request_id": "unknown"},
            is_error=True,
        )
        yield f"data: {json.dumps({'type': 'error', 'detail': f'OpenAI API Error: {e}'})}\n\n"
    except Exception as e:
        # Log the error
        log_openai_response(
            response=str(e),
            request_metadata=request_metadata
            if "request_metadata" in locals()
            else {"request_id": "unknown"},
            is_error=True,
        )
        yield f"data: {json.dumps({'type': 'error', 'detail': f'Internal Server Error: {e}'})}\n\n"


# --- Non-streaming helper ---
async def get_openai_response(
    messages: List[Dict[str, str]],
    model: str,
    temperature: float,
    max_tokens: Optional[int],
    functions: Optional[List[Dict[str, Any]]],
    user_id: Optional[str] = None,
) -> Dict[str, Any]:
    """Call OpenAI API and get a non-streaming response"""
    completion_params = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
    }

    if max_tokens is not None:
        completion_params["max_tokens"] = max_tokens

    if functions:
        completion_params["functions"] = functions
        completion_params["function_call"] = {"name": functions[0]["name"]}
        logger.info(f"Function call enabled: {functions[0]['name']}")

    try:
        # Log the OpenAI request before sending
        request_metadata = log_openai_request(
            model=model,
            messages=messages,
            functions=functions,
            temperature=temperature,
            max_tokens=max_tokens,
            user_id=user_id,
            streaming=False,
        )

        logger.info(f"Sending non-streaming request to OpenAI: model={model}")
        response = await openai_client.chat.completions.create(**completion_params)

        # Log the successful response
        log_openai_response(response=response, request_metadata=request_metadata)

        # Create a standardized response dictionary
        result = {}

        # Get message content
        choice = response.choices[0]
        message = choice.message
        result["content"] = getattr(message, "content", "") or ""

        # Check for function call
        function_call = getattr(message, "function_call", None)

        if function_call:
            # Process function call with standard parser
            result["function_call"] = parse_function_call(function_call)
        else:
            # Try to extract JSON from content
            extracted_json = extract_json_from_text(result["content"])
            if extracted_json and functions:
                function_name = functions[0]["name"]
                result["function_call"] = {
                    "name": function_name,
                    "arguments": extracted_json,
                }

        return result
    except OpenAIError as e:
        # Log the error
        log_openai_response(
            response=str(e),
            request_metadata=request_metadata
            if "request_metadata" in locals()
            else {"request_id": "unknown"},
            is_error=True,
        )
        raise HTTPException(status_code=500, detail=f"OpenAI API Error: {e}")
    except Exception as e:
        # Log the error
        log_openai_response(
            response=str(e),
            request_metadata=request_metadata
            if "request_metadata" in locals()
            else {"request_id": "unknown"},
            is_error=True,
        )
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {e}")


# --- API Endpoints ---
@router.post("/stream", response_class=StreamingResponse)
async def handle_llm_stream_request(
    request_data: LLMRequest,
    current_user: User = Depends(get_current_user_from_supabase),
    prompt_service: PromptService = Depends(get_prompt_service),
) -> StreamingResponse:
    """
    Handles a request to the LLM, streams responses using Server-Sent Events.
    Requires authentication.
    """
    logger.info(
        f"Received LLM stream request for template_id: {request_data.template_id} "
        f"by user: {current_user.id}"
    )

    try:
        # Render the template with the provided variables
        rendered = prompt_service.render_template(
            template_id=request_data.template_id, variables=request_data.variables
        )

        # Prepare the messages for the OpenAI API
        messages = []

        if rendered["system_prompt"]:
            messages.append({"role": "system", "content": rendered["system_prompt"]})
        messages.append({"role": "user", "content": rendered["user_prompt"]})

        # Return streaming response
        return StreamingResponse(
            stream_openai_response(
                messages=messages,
                model=request_data.model,
                temperature=request_data.temperature,
                max_tokens=request_data.max_tokens,
                functions=rendered["functions"],
                user_id=current_user.id,
            ),
            media_type="text/event-stream",
        )
    except ValueError as e:
        # Handle template not found or invalid variables
        logger.error(f"Template error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.post("/complete")
async def handle_llm_completion_request(
    request_data: LLMRequest,
    current_user: User = Depends(get_current_user_from_supabase),
    prompt_service: PromptService = Depends(get_prompt_service),
) -> Dict[str, Any]:
    """
    Handles a request to the LLM with a non-streaming completion.
    Requires authentication.
    """
    logger.info(
        f"Received LLM completion request for template_id: {request_data.template_id} "
        f"by user: {current_user.id}"
    )

    try:
        # Render the template with the provided variables
        rendered = prompt_service.render_template(
            template_id=request_data.template_id, variables=request_data.variables
        )

        # Prepare the messages for the OpenAI API
        messages = []

        if rendered["system_prompt"]:
            messages.append({"role": "system", "content": rendered["system_prompt"]})
        messages.append({"role": "user", "content": rendered["user_prompt"]})

        # Get OpenAI response
        response = await get_openai_response(
            messages=messages,
            model=request_data.model,
            temperature=request_data.temperature,
            max_tokens=request_data.max_tokens,
            functions=rendered["functions"],
            user_id=current_user.id,
        )

        return response
    except ValueError as e:
        # Handle template not found or invalid variables
        logger.error(f"Template error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.post("/prompt")
async def handle_prompt_template_request(
    request_data: LLMRequest,
    current_user: User = Depends(get_current_user_from_supabase),
    prompt_service: PromptService = Depends(get_prompt_service),
) -> Dict[str, Any]:
    """
    Process a prompt template and return the completion result.
    This is similar to /complete but specifically designed for template-based prompts.
    """
    logger.info(f"Prompt template request: template_id={request_data.template_id}")

    # Force request to be non-streaming
    request_data.stream = False

    try:
        # Use the render_template method which handles all the processing
        rendered = prompt_service.render_template(
            template_id=request_data.template_id, variables=request_data.variables
        )

        # Build the messages for the OpenAI API
        messages = []

        # Add system message if present
        if rendered["system_prompt"]:
            messages.append({"role": "system", "content": rendered["system_prompt"]})

        # Add user message
        messages.append({"role": "user", "content": rendered["user_prompt"]})

        # Get functions if available
        functions = rendered["functions"]

        # Call the non-streaming OpenAI API
        response = await get_openai_response(
            messages=messages,
            model=request_data.model,
            temperature=request_data.temperature,
            max_tokens=request_data.max_tokens,
            functions=functions,
            user_id=current_user.id,
        )

        # Ensure we return a simple dict with just the data we need
        result = {
            "content": response.get("content", ""),
        }

        # Include function call if present
        if "function_call" in response:
            result["function_call"] = response["function_call"]

        return result
    except ValueError as e:
        logger.error(f"Template error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal Server Error: {str(e)}")


@router.get("/templates")
async def list_templates(
    current_user: User = Depends(get_current_user_from_supabase),
    prompt_service: PromptService = Depends(get_prompt_service),
) -> List[Dict[str, str]]:
    """
    Lists all available prompt templates.
    Requires authentication.
    """
    return prompt_service.list_templates()
