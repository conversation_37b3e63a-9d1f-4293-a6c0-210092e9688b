import uuid
from sqlalchemy import Column, String, Text, Integer, DateTime, ForeignKey, text, Boolean
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from app.db.session import Base
from app.models.voice import Voice
from datetime import datetime
from typing import Dict, Any


class Character(Base):
    __tablename__ = "characters"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    story_id = Column(UUID(as_uuid=True), ForeignKey("stories.id", ondelete="CASCADE"))
    name = Column(String, nullable=False)
    gender = Column(String)
    age = Column(String)
    appearance = Column(Text)
    personality = Column(Text)
    backstory = Column(Text)
    oneliner = Column(Text)
    voice_id = Column(String)
    creator_user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    avatar_urls = Column(JSONB)
    preview_img = Column(String)
    is_public = Column(Boolean)
    voice = Column(JSONB)
    voice_name = Column(String)
    created_at = Column(DateTime(timezone=True), server_default="now()", nullable=False)
    updated_at = Column(DateTime(timezone=True))

    # Relationships
    story = relationship("Story", back_populates="characters")
    creator = relationship("User", back_populates="created_characters")
    progressions = relationship("CharacterProgression", back_populates="character", cascade="all, delete-orphan")
    voice = relationship("Voice", back_populates="characters")
    assets = relationship("Asset", back_populates="character")

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": str(self.id),
            "story_id": str(self.story_id) if self.story_id else None,
            "name": self.name,
            "gender": self.gender,
            "age": self.age,
            "appearance": self.appearance,
            "personality": self.personality,
            "backstory": self.backstory,
            "oneliner": self.oneliner,
            "voice_id": self.voice_id,
            "creator_user_id": str(self.creator_user_id) if self.creator_user_id else None,
            "avatar_urls": self.avatar_urls,
            "preview_img": self.preview_img,
            "is_public": self.is_public,
            "voice": self.voice,
            "voice_name": self.voice_name,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        } 
