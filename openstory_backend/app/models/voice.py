import uuid
from sqlalchemy import Column, String, Text, DateTime, text, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.session import Base


class Voice(Base):
    __tablename__ = "voices"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    voice_id = Column(String, nullable=True)
    description = Column(Text, nullable=True)
    gender = Column(String, nullable=True)
    age = Column(Integer, nullable=True)
    voice_provider = Column(String, nullable=True)
    example_clip = Column(String, nullable=True)
    created_at = Column(DateTime, server_default=text("now()"))

    # Relationships
    characters = relationship("Character", back_populates="voice") 
    assets = relationship("Asset", back_populates="voice")
