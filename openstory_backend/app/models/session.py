import uuid
from sqlalchemy import Column, String, Text, DateTime, ForeignKey, text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.db.session import Base


class Session(Base):
    __tablename__ = "sessions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    character_progression_id = Column(UUID(as_uuid=True), ForeignKey("character_progressions.id"))
    impersonated_character_id = Column(UUID(as_uuid=True), ForeignKey("characters.id"), nullable=True)
    started_at = Column(DateTime, server_default=text("now()"))

    # Relationships
    user = relationship("User", back_populates="sessions")
    character_progression = relationship("CharacterProgression", back_populates="sessions")
    impersonated_character = relationship("Character", foreign_keys=[impersonated_character_id])
    messages = relationship("Message", back_populates="session", cascade="all, delete-orphan")


class Message(Base):
    __tablename__ = "messages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(UUID(as_uuid=True), ForeignKey("sessions.id"))
    sender = Column(String, nullable=False)
    content = Column(Text, nullable=True)
    timestamp = Column(DateTime, server_default=text("now()"))

    # Relationships
    session = relationship("Session", back_populates="messages") 