from datetime import datetime
from typing import List, Dict, Any, Optional
from uuid import UUID
from sqlalchemy import Column, String, Boolean, Text, ForeignKey, DateTime, JSON
from sqlalchemy.dialects.postgresql import UUID as PGUUID, JSONB
from sqlalchemy.orm import relationship
from pydantic import BaseModel, Field
from app.schemas.frame import FrameBase
from app.db.session import Base

class Story(BaseModel):
    id: Optional[UUID] = None
    user_id: UUID
    concept: str = ""
    movie_style: Any = Field(default_factory=dict)
    story_line: str
    characters: List[Dict[str, Any]] = Field(default_factory=list)
    frames: List[FrameBase] = Field(default_factory=list)
    result: Optional[str] = None
    result_time: str = "0:00"
    status: str = "pending"
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
        json_encoders = {
            UUID: str,
            datetime: lambda dt: dt.isoformat() if dt else None
        }

class StoryCreate(BaseModel):
    concept: str = ""
    movie_style: Optional[Any] = Field(default_factory=dict)
    storyLine: str = ""
    characters: List[Dict[str, Any]] = Field(default_factory=list)
    frames: List[FrameBase] = Field(default_factory=list)
    result: Optional[str] = None
    result_time: str = "0:00"

    class Config:
        from_attributes = True
        json_encoders = {
            UUID: str,
            datetime: lambda dt: dt.isoformat() if dt else None
        }

class StoryUpdate(BaseModel):
    concept: Optional[str] = None
    movie_style: Optional[Dict[str, Any]] = None
    story_line: Optional[str] = None
    characters: Optional[List[Dict[str, Any]]] = None
    frames: Optional[List[FrameBase]] = None
    result: Optional[str] = None
    result_time: Optional[str] = None
    status: Optional[str] = None

class Story(Base):
    __tablename__ = "stories"

    id = Column(PGUUID, primary_key=True, server_default="gen_random_uuid()")
    user_id = Column(PGUUID, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    title = Column(String, nullable=False)
    description = Column(Text)
    is_public = Column(Boolean, default=False)
    image_url = Column(String)
    category = Column(String)
    movie_style = Column(JSONB, default={})
    storyline = Column(Text, default="")
    result = Column(Text)
    result_time = Column(String)
    created_at = Column(DateTime(timezone=True), server_default="now()", nullable=False)
    updated_at = Column(DateTime(timezone=True))

    # Relationships
    frames = relationship("Frame", back_populates="story", cascade="all, delete-orphan")
    characters = relationship("Character", back_populates="story", cascade="all, delete-orphan")
    user = relationship("User", back_populates="stories")

    def __repr__(self):
        return f"<Story {self.title}>"

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "title": self.title,
            "description": self.description,
            "is_public": self.is_public,
            "image_url": self.image_url,
            "category": self.category,
            "movie_style": self.movie_style,
            "storyline": self.storyline,
            "characters": [char.to_dict() for char in self.characters],
            "frames": [frame.to_dict() for frame in self.frames],
            "result": self.result,
            "result_time": self.result_time,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        } 