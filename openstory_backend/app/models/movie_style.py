import uuid
from sqlalchemy import Column, String, DateTime, text
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from app.db.session import Base


class MovieStyle(Base):
    __tablename__ = "movie_styles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    styles = Column(JSONB, nullable=False)
    created_at = Column(DateTime, server_default=text("now()"))
    updated_at = Column(DateTime, server_default=text("now()"), onupdate=text("now()"))

    # Relationships
    shows = relationship("Show", back_populates="movie_style") 