from sqlalchemy import Column, Integer, String, Enum, DateTime, func, Index
from sqlalchemy.orm import relationship
from app.db.session import Base
import enum


class AssetType(enum.Enum):
    CHARACTER = "character"
    VOICE = "voice"
    BGM = "bgm"
    SCENE_IMAGE = "scene_image"


class StoreType(enum.Enum):
    EXTERNAL_URL = "external_url"
    SUPABASE_STORAGE = "supabase_storage"


class AccessType(enum.Enum):
    PUBLIC = "public"
    PRIVATE = "private"


class Asset(Base):
    __tablename__ = "assets"

    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now())

    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    type = Column(Enum(AssetType), nullable=False)
    store_type = Column(Enum(StoreType), nullable=False)
    access_type = Column(Enum(AccessType), nullable=False)
    external_url = Column(String, nullable=True)
    bucket = Column(String, nullable=True)
    path = Column(String, nullable=True)
    foreign_id = Column(String, nullable=True, index=True)

    # Relationships
    character = relationship(
        "Character",
        back_populates="assets",
        primaryjoin="and_(Asset.foreign_id == Character.id, Asset.type == 'character')",
    )
    voice = relationship(
        "Voice",
        back_populates="assets",
        primaryjoin="and_(Asset.foreign_id == Voice.id, Asset.type == 'voice')",
    )

    __table_args__ = (Index("idx_type_accesstype", type, access_type),)
