import uuid
from sqlalchemy import Column, String, Text, DateTime, ForeignKey, text, Enum, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
from typing import Dict, Any

from app.db.session import Base


class Frame(Base):
    __tablename__ = "frames"

    id = Column(UUID(as_uuid=True), primary_key=True, server_default="gen_random_uuid()")
    story_id = Column(UUID(as_uuid=True), ForeignKey("stories.id", ondelete="CASCADE"), nullable=False)
    shot_name = Column(String, nullable=False)
    shot_type = Column(String, nullable=False)
    shot_setting = Column(String, nullable=False)
    shot_framing = Column(String, nullable=False)
    shot_angle = Column(String, nullable=False)
    shot_movement = Column(String, nullable=False)
    character_composition = Column(Text)
    character_dialogue = Column(Text)
    image_url = Column(String)
    status = Column(String, nullable=False, default="pending")
    created_at = Column(DateTime(timezone=True), server_default="now()", nullable=False)
    updated_at = Column(DateTime(timezone=True))

    # Relationships
    story = relationship("Story", back_populates="frames")

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": str(self.id),
            "story_id": str(self.story_id),
            "shot_name": self.shot_name,
            "shot_type": self.shot_type,
            "shot_setting": self.shot_setting,
            "shot_framing": self.shot_framing,
            "shot_angle": self.shot_angle,
            "shot_movement": self.shot_movement,
            "character_composition": self.character_composition,
            "character_dialogue": self.character_dialogue,
            "image_url": self.image_url,
            "status": self.status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class FrameComponent(Base):
    __tablename__ = "frame_components"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    frame_id = Column(UUID(as_uuid=True), ForeignKey("frames.id"))
    component_type = Column(String, nullable=False)
    content = Column(String, nullable=True)
    media_url = Column(String, nullable=True)
    start_time_ms = Column(Integer, nullable=True)
    duration_ms = Column(Integer, nullable=True)
    created_at = Column(DateTime, server_default=text("now()"))

    # Relationships
    frame = relationship("Frame", back_populates="components") 