import uuid
from sqlalchemy import Column, Text, DateTime, ForeignKey, text
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from app.db.session import Base


class CharacterProgression(Base):
    __tablename__ = "character_progressions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    character_id = Column(UUID(as_uuid=True), ForeignKey("characters.id"))
    episode_id = Column(UUID(as_uuid=True), ForeignKey("episodes.id"))
    cumulative_traits = Column(JSONB, nullable=True)
    cumulative_memories = Column(JSONB, nullable=True)
    state_description = Column(Text, nullable=True)
    created_at = Column(DateTime, server_default=text("now()"))

    # Relationships
    character = relationship("Character", back_populates="progressions")
    episode = relationship("Episode", back_populates="character_progressions")
    sessions = relationship("Session", back_populates="character_progression", cascade="all, delete-orphan") 