import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

# Configure the logger with a more visible format
logging.basicConfig(
    level=logging.INFO,
    format='\033[1;36m[LLM_LOG]\033[0m %(message)s',  # Cyan color for visibility
    stream=sys.stderr  # Force output to stderr which is more likely to show in terminal
)
logger = logging.getLogger("llm_logger")  # Give it a specific name

# Check if we should log to file
LOG_TO_FILE = os.getenv("LOG_LLM_TO_FILE", "").lower() in ("true", "1", "yes")
LOG_DIR = os.getenv("LOG_DIR", "logs")

if LOG_TO_FILE and not os.path.exists(LOG_DIR):
    try:
        os.makedirs(LOG_DIR)
    except Exception as e:
        logger.error(f"Failed to create log directory: {e}")
        LOG_TO_FILE = False

# Force the logger to propagate
logger.propagate = True
# Set level to ensure it's not filtered
logger.setLevel(logging.INFO)

# Add a direct stderr handler to be sure
stderr_handler = logging.StreamHandler(sys.stderr)
stderr_handler.setLevel(logging.INFO)
stderr_handler.setFormatter(logging.Formatter('\033[1;36m[LLM_LOG]\033[0m %(message)s'))
logger.addHandler(stderr_handler)

def truncate_long_text(text: str, max_length: int = 500) -> str:
    """Truncate long text for logging purposes"""
    if text is None:
        return "(None)"
    if not isinstance(text, str):
        text = str(text)
    if len(text) <= max_length:
        return text
    return text[:max_length] + f"... [truncated, {len(text)} chars total]"


def safe_get_function_name(function_call: Any) -> str:
    """Safely extract the name from a function call object, handling different types"""
    if function_call is None:
        return "unknown"
        
    try:
        # Try direct attribute access first
        if hasattr(function_call, "name"):
            return function_call.name
            
        # Try dictionary access
        if isinstance(function_call, dict) and "name" in function_call:
            return function_call["name"]
            
        # Try generic string conversion as last resort
        return str(function_call)
    except Exception:
        return "unknown"


def log_openai_request(
    model: str,
    messages: List[Dict[str, str]],
    functions: Optional[List[Dict[str, Any]]] = None,
    temperature: float = 0.7,
    max_tokens: Optional[int] = None,
    user_id: Optional[str] = None,
    request_id: Optional[str] = None,
    streaming: bool = False,
    **kwargs
) -> Dict[str, Any]:
    """
    Log OpenAI API request details
    
    Args:
        model: The OpenAI model being used
        messages: The messages being sent to the API
        functions: Any functions being passed to the API
        temperature: The temperature setting
        max_tokens: Maximum tokens setting
        user_id: Optional user ID for tracking
        request_id: Optional request ID for tracking
        streaming: Whether this is a streaming request
        **kwargs: Any additional parameters
        
    Returns:
        Dictionary with metadata about the request for correlation with response
    """
    # Generate a timestamp and request ID for correlation
    timestamp = datetime.now().isoformat()
    request_metadata = {
        "timestamp": timestamp,
        "request_id": request_id or f"req_{int(time.time() * 1000)}",
        "model": model,
        "user_id": user_id,
        "streaming": streaming
    }
    
    # More noticeable separator
    print('\n\033[1;33m' + '='*50 + '\033[0m', file=sys.stderr)
    
    # Log the request
    logger.info(
        f"OpenAI Request: model={model}, user={user_id or 'unknown'}, "
        f"streaming={streaming}, temp={temperature}, tokens={max_tokens or 'auto'}"
    )
    
    # Log the prompt (messages)
    for i, msg in enumerate(messages):
        role = msg.get("role", "unknown")
        content = msg.get("content", "")
        logger.info(
            f"Message {i+1} ({role}): {truncate_long_text(content)}"
        )
    
    # Log function calls if present
    if functions:
        logger.info(f"Functions: {[f.get('name', 'unnamed') for f in functions]}")
    
    # More noticeable separator
    print('\033[1;33m' + '='*50 + '\033[0m', file=sys.stderr)
        
    # Write detailed log to file if enabled
    if LOG_TO_FILE:
        try:
            log_file = os.path.join(
                LOG_DIR, 
                f"openai_requests_{datetime.now().strftime('%Y%m%d')}.jsonl"
            )
            
            log_entry = {
                "type": "request",
                "timestamp": timestamp,
                "request_id": request_metadata["request_id"],
                "model": model,
                "user_id": user_id,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "streaming": streaming,
                "messages": messages,
                "functions": functions
            }
            
            with open(log_file, "a") as f:
                f.write(json.dumps(log_entry) + "\n")
                
        except Exception as e:
            logger.error(f"Failed to write request log to file: {e}")
    
    return request_metadata


def log_openai_response(
    response: Any,
    request_metadata: Dict[str, Any],
    is_streaming: bool = False,
    is_error: bool = False
) -> None:
    """
    Log OpenAI API response
    
    Args:
        response: The response from OpenAI
        request_metadata: Metadata from the corresponding request
        is_streaming: Whether this is a streaming response
        is_error: Whether this response is an error
    """
    request_id = request_metadata.get("request_id", "unknown")
    
    if is_error:
        print('\n\033[1;31m' + '='*50 + '\033[0m', file=sys.stderr)  # Red error marker
        logger.error(
            f"OpenAI Error: request_id={request_id}, error={str(response)}"
        )
        print('\033[1;31m' + '='*50 + '\033[0m', file=sys.stderr)
        return
    
    # For streaming responses, just log the first response
    if is_streaming:
        logger.info(
            f"OpenAI Streaming Response Started: request_id={request_id}"
        )
        return
    
    # For non-streaming, log the content and/or function call
    try:
        content = ""
        function_call = None
        
        # More noticeable separator
        print('\n\033[1;32m' + '='*50 + '\033[0m', file=sys.stderr)  # Green for response
        
        # Extract content based on response structure
        if hasattr(response, "choices") and len(response.choices) > 0:
            message = response.choices[0].message
            content = getattr(message, "content", "") or ""
            function_call = getattr(message, "function_call", None)
        elif isinstance(response, dict):
            content = response.get("content", "")
            function_call = response.get("function_call")
        
        # Log the extracted information - Split into multiple lines for clarity
        logger.info(f"OpenAI Response: request_id={request_id}")
        if content:
            # Print content on separate lines for better readability
            logger.info(f"Content:")
            # Split by lines to make the output more readable
            for line in content.split('\n'):
                if line.strip():  # Skip empty lines
                    logger.info(f"  {line}")
        else:
            logger.info("Content: <empty>")
        
        if function_call:
            # Use our safe function to extract the name
            function_name = safe_get_function_name(function_call)
            logger.info(f"Function Call: request_id={request_id}, function={function_name}")
            
            # Also log the function arguments if available
            if hasattr(function_call, "arguments"):
                args = getattr(function_call, "arguments", "{}")
                try:
                    # Try to parse as JSON for pretty display
                    parsed_args = json.loads(args) if isinstance(args, str) else args
                    args_str = json.dumps(parsed_args, indent=2)
                    logger.info(f"Function Arguments:")
                    for line in args_str.split('\n'):
                        logger.info(f"  {line}")
                except:
                    # Just show as string if not JSON
                    logger.info(f"Function Arguments: {truncate_long_text(args)}")
        
        # More noticeable separator
        print('\033[1;32m' + '='*50 + '\033[0m', file=sys.stderr)
        
        # Write detailed log to file if enabled
        if LOG_TO_FILE:
            try:
                log_file = os.path.join(
                    LOG_DIR, 
                    f"openai_responses_{datetime.now().strftime('%Y%m%d')}.jsonl"
                )
                
                # Convert to serializable format
                if not isinstance(response, dict):
                    if hasattr(response, "model_dump"):
                        serialized_response = response.model_dump()
                    elif hasattr(response, "to_dict"):
                        serialized_response = response.to_dict()
                    else:
                        serialized_response = {"content": content}
                        if function_call:
                            # Handle FunctionCall object more safely
                            if hasattr(function_call, "model_dump"):
                                serialized_response["function_call"] = function_call.model_dump()
                            elif hasattr(function_call, "name") and hasattr(function_call, "arguments"):
                                serialized_response["function_call"] = {
                                    "name": function_call.name,
                                    "arguments": function_call.arguments
                                }
                            else:
                                # Last resort fallback
                                serialized_response["function_call"] = {
                                    "name": safe_get_function_name(function_call),
                                    "arguments": str(getattr(function_call, "arguments", "{}"))
                                }
                else:
                    serialized_response = response
                
                log_entry = {
                    "type": "response",
                    "timestamp": datetime.now().isoformat(),
                    "request_id": request_id,
                    "response": serialized_response
                }
                
                with open(log_file, "a") as f:
                    f.write(json.dumps(log_entry) + "\n")
                    
            except Exception as e:
                logger.error(f"Failed to write response log to file: {e}")
    
    except Exception as e:
        logger.error(f"Error logging OpenAI response: {e}")


def log_openai_stream_complete(request_metadata: Dict[str, Any]) -> None:
    """Log completion of a streaming response"""
    request_id = request_metadata.get("request_id", "unknown")
    
    # More noticeable marker
    print('\n\033[1;32m' + '='*50 + '\033[0m', file=sys.stderr)  # Green for completion
    logger.info(f"OpenAI Streaming Response Completed: request_id={request_id}")
    print('\033[1;32m' + '='*50 + '\033[0m', file=sys.stderr) 