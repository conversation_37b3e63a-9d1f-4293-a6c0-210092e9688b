import json
import base64

from typing import Dict, Any
from jose import jwt, J<PERSON><PERSON><PERSON><PERSON>
from fastapi import Request, HTTPException, status

from app.utils.logger import logger
from app.core.config import get_settings

settings = get_settings()

# DEVELOPMENT OVERRIDE - REMOVE IN PRODUCTION
# This is the default Supabase JWT signing key for local development
SUPABASE_DEV_JWT_SECRET = settings.JWT_SECRET
# Force development mode for now - REMOVE IN PRODUCTION
DEV_MODE = True


def safe_log_token(token: str) -> str:
    """Create a logging-safe token representation that masks most of the content."""
    if not token:
        return "<no-token>"
    return f"{token[:10]}...{token[-5:]}" if len(token) > 15 else "<short-token>"


def log_headers(request: Request) -> None:
    """Log request headers in a structured format."""
    headers = dict(request.headers.items())
    # Mask Authorization header
    if "authorization" in headers:
        auth = headers["authorization"]
        if auth.startswith("Bearer "):
            headers["authorization"] = f"Bearer {safe_log_token(auth[7:])}"

    logger.info(f"Request headers: {json.dumps(headers)}")


def log_token_payload(payload: Dict[str, Any], label: str = "Token payload") -> None:
    """Log token payload with sensitive fields masked."""
    if not payload:
        logger.info(f"{label}: <empty>")
        return

    # Create a copy to avoid modifying the original
    safe_payload = payload.copy()

    # Mask potentially sensitive fields
    for field in ["email", "sub", "session_id"]:
        if field in safe_payload:
            val = safe_payload[field]
            if isinstance(val, str) and len(val) > 8:
                safe_payload[field] = f"{val[:4]}...{val[-4:]}"

    # Log the safe payload
    logger.info(f"{label}: {json.dumps(safe_payload)}")


def decode_token_parts(token: str) -> None:
    """Decode and log the header and payload of a JWT token without verification."""
    try:
        parts = token.split(".")
        if len(parts) != 3:
            logger.warning(f"Token doesn't have 3 parts: {len(parts)} parts found")
            return

        # Decode header
        header_bytes = base64.urlsafe_b64decode(
            parts[0] + "=" * (4 - len(parts[0]) % 4)
        )
        header = json.loads(header_bytes)
        logger.info(f"Token header: {json.dumps(header)}")

        # Decode payload
        payload_bytes = base64.urlsafe_b64decode(
            parts[1] + "=" * (4 - len(parts[1]) % 4)
        )
        payload = json.loads(payload_bytes)
        log_token_payload(payload, "Raw decoded payload")

    except Exception as e:
        logger.error(f"Error decoding token parts: {str(e)}")


async def verify_supabase_token(request: Request):
    """
    Verifies the Supabase JWT token from the Authorization header.
    Returns the decoded token payload if valid.
    """
    logger.info("=== Starting JWT token verification ===")
    log_headers(request)

    auth = request.headers.get("Authorization")
    if not auth or not auth.startswith("Bearer "):
        logger.error("Missing or invalid Authorization header")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    token = auth.split(" ")[1]
    logger.info(f"Verifying token: {safe_log_token(token)}")

    # Log if token is suspiciously short
    if len(token) < 50:
        logger.warning(f"Token is unusually short: {len(token)} characters")

    # Decode token parts for debugging without verification
    decode_token_parts(token)

    # First try to decode without verification (just for logging)
    try:
        unverified_payload = jwt.decode(
            token,
            key="",  # Empty key for unverified decode
            options={"verify_signature": False},
        )
        log_token_payload(unverified_payload, "Unverified token payload")

        # Log specific important fields
        logger.info(f"Token issuer (iss): {unverified_payload.get('iss', '<missing>')}")
        logger.info(
            f"Token subject (sub): {unverified_payload.get('sub', '<missing>')}"
        )
        logger.info(
            f"Token audience (aud): {unverified_payload.get('aud', '<missing>')}"
        )
        logger.info(
            f"Token expiration (exp): {unverified_payload.get('exp', '<missing>')}"
        )

        # DEVELOPMENT MODE OVERRIDE - Skip verification in development
        if DEV_MODE or settings.ENVIRONMENT == "development":
            logger.warning("DEVELOPMENT MODE: Bypassing token verification")
            return unverified_payload

    except Exception as e:
        logger.warning(f"Couldn't decode token for debugging: {e}")

    # Log JWT secret information
    jwt_secret = settings.JWT_SECRET
    logger.info(
        f"JWT_SECRET: {jwt_secret[:3]}...{jwt_secret[-3:]} (length: {len(jwt_secret)})"
    )

    # Try using the Supabase dev JWT secret if in development
    if DEV_MODE or settings.ENVIRONMENT == "development":
        logger.info(f"Using Supabase development JWT secret instead")
        jwt_secret = SUPABASE_DEV_JWT_SECRET
        logger.info(
            f"Using SUPABASE_DEV_JWT_SECRET: {jwt_secret[:3]}...{jwt_secret[-3:]} (length: {len(jwt_secret)})"
        )

    try:
        # Verify and decode the JWT
        payload = jwt.decode(
            token,
            jwt_secret,
            algorithms=["HS256"],
            options={
                "verify_signature": True,
                "verify_aud": False,
                "verify_iat": False,
                "verify_exp": False,  # Disable expiration check in development
                "verify_nbf": False,
                "verify_iss": False,
                "verify_sub": False,
                "verify_jti": False,
                "verify_at_hash": False,
            },
        )

        logger.info("JWT signature verification passed!")
        log_token_payload(payload, "Verified token payload")

        # Get the issuer from the token
        token_issuer = payload.get("iss", "")
        logger.info(f"Token issuer: {token_issuer}")

        # Define valid issuers - be flexible for development
        valid_issuers = [
            f"{settings.SUPABASE_URL}/auth/v1",
            "http://127.0.0.1:54321/auth/v1",
            "http://localhost:54321/auth/v1",
            "http://*************:54321/auth/v1",
        ]

        # Check if token issuer matches any valid issuer
        issuer_valid = any(token_issuer == issuer for issuer in valid_issuers)
        logger.info(f"Issuer valid: {issuer_valid}")
        logger.info(f"Valid issuers: {valid_issuers}")

        # For development, we'll log but not enforce issuer validation
        if not issuer_valid:
            logger.warning(f"Token has invalid issuer: {token_issuer}")

        logger.info("=== JWT token verification successful ===")
        return payload
    except JWTError as e:
        logger.error(f"JWT validation error: {str(e)}")
        logger.error("=== JWT token verification failed ===")

        # DEVELOPMENT MODE OVERRIDE - Skip verification in development
        if DEV_MODE or settings.ENVIRONMENT == "development":
            logger.warning(
                "DEVELOPMENT MODE: Returning unverified payload after JWT error"
            )
            try:
                unverified_payload = jwt.decode(
                    token,
                    key="",  # Empty key for unverified decode
                    options={"verify_signature": False, "verify_exp": False},
                )
                return unverified_payload
            except Exception as inner_e:
                logger.error(
                    f"Cannot decode token even without verification: {inner_e}"
                )

        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Invalid authentication credentials: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

