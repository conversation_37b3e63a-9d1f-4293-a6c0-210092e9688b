from pydantic_settings import BaseSettings
from functools import lru_cache
from typing import Optional
import os

from app.utils.logger import logger


class Settings(BaseSettings):
    """
    Application settings class that loads environment variables.
    Loaded from environment variables at startup.
    """

    # Core application settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Drama API"

    # Auth settings
    ALGORITHM: str = "HS256"
    JWT_SECRET: str = "your-secret-key"  # Default, should be overridden in env
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days

    # Supabase settings
    SUPABASE_URL: str
    SUPABASE_KEY: str
    SUPABASE_JWT_TOKEN: Optional[str] = None
    SUPABASE_EMAIL: Optional[str] = None
    SUPABASE_PASSWORD: Optional[str] = None

    # Database settings
    DATABASE_URL: Optional[str] = None

    # Environment settings
    ENVIRONMENT: str = "development"

    # Google settings
    GOOGLE_CLIENT_ID: Optional[str] = None
    GOOGLE_CLIENT_SECRET: Optional[str] = None

    # OpenAI settings
    OPENAI_API_KEY: Optional[str] = None
    openai_api_key: Optional[str] = None  # Lowercase version for backward compatibility

    # Runway settings
    RUNWAYML_API_SECRET: str | None = None

    # Elevenlabs settings
    ELEVENLABS_API_KEY: str | None = None

    # fal settings
    FAL_KAY: str | None = None

    # Lip sync settings
    LIP_SYNC_KEY: str | None = None
    LIP_SYNC_HOST: str | None = None

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        # Allow case-insensitive environment variables
        case_sensitive = False
        # Either include missing fields or use this to allow extra fields
        extra = "ignore"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Ensure both uppercase and lowercase versions are populated
        if (
            hasattr(self, "OPENAI_API_KEY")
            and self.OPENAI_API_KEY
            and not hasattr(self, "openai_api_key")
        ):
            self.openai_api_key = self.OPENAI_API_KEY
        elif (
            hasattr(self, "openai_api_key")
            and self.openai_api_key
            and not hasattr(self, "OPENAI_API_KEY")
        ):
            self.OPENAI_API_KEY = self.openai_api_key


# Clear the LRU cache to ensure settings are reloaded
def clear_settings_cache():
    logger.info("Clearing settings cache")
    if hasattr(get_settings, "cache_clear"):
        get_settings.cache_clear()


@lru_cache()
def get_settings() -> Settings:
    settings = Settings()

    # Ensure openai_api_key is always set
    if not hasattr(settings, "openai_api_key") or settings.openai_api_key is None:
        env_key = os.environ.get("OPENAI_API_KEY") or os.environ.get("openai_api_key")
        if env_key:
            settings.openai_api_key = env_key
            if hasattr(settings, "OPENAI_API_KEY"):
                settings.OPENAI_API_KEY = env_key

    # Log masked secrets and critical configuration for debugging
    jwt_secret = settings.JWT_SECRET or ""
    masked_jwt = (
        jwt_secret[:5] + "..." + jwt_secret[-5:]
        if len(jwt_secret) > 10
        else "NOT_SET_PROPERLY"
    )

    logger.info("=== Loading application settings ===")
    logger.info(f"SUPABASE_URL: {settings.SUPABASE_URL}")
    logger.info(f"JWT_SECRET: {masked_jwt} (length: {len(jwt_secret)})")
    logger.info(f"ALGORITHM: {settings.ALGORITHM}")
    logger.info(f"ENVIRONMENT: {settings.ENVIRONMENT}")
    if hasattr(settings, "openai_api_key") and settings.openai_api_key:
        logger.info(
            f"OpenAI API Key: {settings.openai_api_key[:5]}...{settings.openai_api_key[-5:] if len(settings.openai_api_key) > 10 else ''}"
        )
    logger.info(f"Using env file: {os.path.exists('.env')}")
    logger.info("=== Settings loaded ===")

    return settings
