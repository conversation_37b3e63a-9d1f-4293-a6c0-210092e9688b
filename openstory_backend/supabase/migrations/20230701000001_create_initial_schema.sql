-- Create schema
CREATE SCHEMA IF NOT EXISTS public;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create users table
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    first_name TEXT,
    display_name TEXT,
    username TEXT UNIQUE,
    hashed_password TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- <PERSON><PERSON> shows table
CREATE TABLE IF NOT EXISTS public.shows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    title TEXT,
    description TEXT,
    image_url TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NULL
);

-- Create episodes table
CREATE TABLE IF NOT EXISTS public.episodes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    show_id UUID REFERENCES public.shows(id) ON DELETE CASCADE,
    title TEXT,
    synopsis TEXT,
    oneliner TEXT,
    permission TEXT CHECK (permission IN ('private','public')) DEFAULT 'private',
    stitched_video_url TEXT,
    image_url TEXT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create characters table
CREATE TABLE IF NOT EXISTS public.characters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    creator_user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT,
    gender TEXT,
    age INTEGER,
    oneliner TEXT,
    backstory TEXT,
    personality TEXT,
    appearance TEXT,
    avatar_urls JSONB,
    voice_description TEXT,
    voice_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create character_progressions table
CREATE TABLE IF NOT EXISTS public.character_progressions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    character_id UUID REFERENCES public.characters(id) ON DELETE CASCADE,
    episode_id UUID REFERENCES public.episodes(id) ON DELETE CASCADE,
    cumulative_traits JSONB,
    cumulative_memories JSONB,
    state_description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create frames table
CREATE TABLE IF NOT EXISTS public.frames (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    episode_id UUID REFERENCES public.episodes(id) ON DELETE CASCADE,
    frame_number INTEGER,
    image_url TEXT,
    video_url TEXT,
    visual_type TEXT CHECK (visual_type IN ('image','video','image_then_video')) DEFAULT 'image',
    duration_seconds INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create frame_components table
CREATE TABLE IF NOT EXISTS public.frame_components (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    frame_id UUID REFERENCES public.frames(id) ON DELETE CASCADE,
    component_type TEXT CHECK (component_type IN ('dialogue','sound_effect')),
    content TEXT,
    media_url TEXT,
    start_time_ms INTEGER,
    duration_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create sessions table
CREATE TABLE IF NOT EXISTS public.sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    character_progression_id UUID REFERENCES public.character_progressions(id) ON DELETE CASCADE,
    impersonated_character_id UUID REFERENCES public.characters(id) ON DELETE SET NULL,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create messages table
CREATE TABLE IF NOT EXISTS public.messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES public.sessions(id) ON DELETE CASCADE,
    sender TEXT CHECK (sender IN ('user','character')),
    content TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create jobs table
CREATE TABLE IF NOT EXISTS public.jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    frame_id UUID REFERENCES public.frames(id) ON DELETE SET NULL,
    status TEXT DEFAULT 'pending',
    job_type TEXT NOT NULL,
    payload JSONB,
    result JSONB,
    error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_shows_user_id ON public.shows(user_id);
CREATE INDEX IF NOT EXISTS idx_episodes_show_id ON public.episodes(show_id);
CREATE INDEX IF NOT EXISTS idx_characters_creator_user_id ON public.characters(creator_user_id);
CREATE INDEX IF NOT EXISTS idx_character_progressions_character_id ON public.character_progressions(character_id);
CREATE INDEX IF NOT EXISTS idx_character_progressions_episode_id ON public.character_progressions(episode_id);
CREATE INDEX IF NOT EXISTS idx_frames_episode_id ON public.frames(episode_id);
CREATE INDEX IF NOT EXISTS idx_frame_components_frame_id ON public.frame_components(frame_id);
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON public.sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_character_progression_id ON public.sessions(character_progression_id);
CREATE INDEX IF NOT EXISTS idx_messages_session_id ON public.messages(session_id);
CREATE INDEX IF NOT EXISTS idx_jobs_frame_id ON public.jobs(frame_id);
CREATE INDEX IF NOT EXISTS idx_jobs_status ON public.jobs(status);

-- Create RLS policies if needed
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.shows ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.episodes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.character_progressions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.frames ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.frame_components ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.jobs ENABLE ROW LEVEL SECURITY;

-- Create basic RLS policies
CREATE POLICY "Users can read their own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can read shows they created" ON public.shows
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can read public episodes" ON public.episodes
  FOR SELECT USING (permission = 'public' OR (SELECT user_id FROM public.shows WHERE id = show_id) = auth.uid());

CREATE POLICY "Users can read characters they created" ON public.characters
  FOR SELECT USING (creator_user_id = auth.uid());

CREATE POLICY "Users can read their character progressions" ON public.character_progressions
  FOR SELECT USING ((SELECT creator_user_id FROM public.characters WHERE id = character_id) = auth.uid());

-- Function to create a user in the public schema when a user is created in the auth schema
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, username)
  VALUES (new.id, new.email, new.email);
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to call the function above
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user(); 