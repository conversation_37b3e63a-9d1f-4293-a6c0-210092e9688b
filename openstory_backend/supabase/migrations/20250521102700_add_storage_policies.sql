-- Create storage bucket for characters if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('characters', 'characters', true)
ON CONFLICT (id) DO NOTHING;

-- Drop existing policies if any
DROP POLICY IF EXISTS "Allow authenticated users to upload character images" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to character images" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to update their own character images" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to delete their own character images" ON storage.objects;

-- Create policy for authenticated users to upload images
CREATE POLICY "Allow authenticated users to upload character images"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'characters');

-- Create policy for public access to images
CREATE POLICY "Allow public access to character images"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'characters');

-- Create policy for users to update their own images
CREATE POLICY "Allow users to update their own character images"
ON storage.objects FOR UPDATE
TO authenticated
USING (bucket_id = 'characters')
WITH CHECK (bucket_id = 'characters');

-- Create policy for users to delete their own images
CREATE POLICY "Allow users to delete their own character images"
ON storage.objects FOR DELETE
TO authenticated
USING (bucket_id = 'characters'); 