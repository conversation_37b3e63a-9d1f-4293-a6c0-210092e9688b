-- Add voice_description column to characters table
ALTER TABLE characters
ADD COLUMN IF NOT EXISTS voice_description TEXT;

-- Add visual_style column to characters table
ALTER TABLE characters
ADD COLUMN IF NOT EXISTS visual_style TEXT;

-- Add new columns to shots table
ALTER TABLE shots
ADD COLUMN IF NOT EXISTS shot_name TEXT,
ADD COLUMN IF NOT EXISTS shot_type TEXT,
ADD COLUMN IF NOT EXISTS shot_setting TEXT,
ADD COLUMN IF NOT EXISTS shot_framing TEXT,
ADD COLUMN IF NOT EXISTS shot_angle TEXT,
ADD COLUMN IF NOT EXISTS shot_movement TEXT,
ADD COLUMN IF NOT EXISTS character_composition JSONB,
ADD COLUMN IF NOT EXISTS character_dialogue JSONB,
ADD COLUMN IF NOT EXISTS characters JSONB,
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'pending';

-- Update RLS policies
DROP POLICY IF EXISTS "Users can select their own characters" ON characters;
DROP POLICY IF EXISTS "Users can insert their own characters" ON characters;
DROP POLICY IF EXISTS "Users can update their own characters" ON characters;
DROP POLICY IF EXISTS "Users can delete their own characters" ON characters;

CREATE POLICY "Users can select their own characters"
ON characters FOR SELECT
TO authenticated
USING (auth.uid() = creator_user_id);

CREATE POLICY "Users can insert their own characters"
ON characters FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = creator_user_id);

CREATE POLICY "Users can update their own characters"
ON characters FOR UPDATE
TO authenticated
USING (auth.uid() = creator_user_id)
WITH CHECK (auth.uid() = creator_user_id);

CREATE POLICY "Users can delete their own characters"
ON characters FOR DELETE
TO authenticated
USING (auth.uid() = creator_user_id); 