-- Create categories table
CREATE TABLE IF NOT EXISTS public.categories (
    id UUID PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Add comment for documentation
COMMENT ON TABLE public.categories IS 'Categories or genres for shows';

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS categories_name_idx ON public.categories (name);

-- Set up RLS (Row Level Security)
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;

-- Create policy for public access to categories (read-only)
CREATE POLICY "Categories are viewable by everyone" 
ON public.categories 
FOR SELECT 
USING (true);

-- Create policy for admins to manage categories
-- Temporarily commented out due to an issue with public.users availability during migration
-- CREATE POLICY "Admins can manage categories" 
-- ON public.categories 
-- USING (auth.uid() IN (
--     SELECT id FROM public.users WHERE email = '<EMAIL>'
-- )); 