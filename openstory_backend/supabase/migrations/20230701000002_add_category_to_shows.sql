-- Add category field to shows table
ALTER TABLE public.shows ADD COLUMN category TEXT;

-- Update existing shows with default categories
UPDATE public.shows
SET category = 'Technology'
WHERE title = 'The Endless Code';

UPDATE public.shows
SET category = 'Business'
WHERE title = 'Silicon Valley Dreams';

UPDATE public.shows
SET category = 'Mystery'
WHERE title = 'Midnight Mystery';

-- Create index for faster filtering/searching
CREATE INDEX IF NOT EXISTS shows_category_idx ON public.shows (category);

-- Add category field to shows table in seed data
COMMENT ON COLUMN public.shows.category IS 'Category or genre of the show (e.g., Drama, Comedy, Mystery, etc.)'; 