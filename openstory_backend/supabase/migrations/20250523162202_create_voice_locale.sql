-- Create voice_locale table
CREATE TABLE IF NOT EXISTS voice_locale (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    display_name TEXT NOT NULL,
    description TEXT NOT NULL,
    voice_id TEXT NOT NULL UNIQUE,
    gender TEXT NOT NULL CHECK (gender IN ('Male', 'Female')),
    age TEXT NOT NULL CHECK (age IN ('Young', 'Middle-aged', 'Older')),
    accent TEXT NOT NULL CHECK (accent IN ('British', 'American')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    CONSTRAINT voice_locale_display_name_unique UNIQUE (display_name),
    CONSTRAINT voice_locale_description_length CHECK (length(description) >= 10),
    CONSTRAINT voice_locale_voice_id_format CHECK (voice_id ~ '^[a-zA-Z0-9]{20}$')
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_voice_locale_gender ON voice_locale(gender);
CREATE INDEX IF NOT EXISTS idx_voice_locale_age ON voice_locale(age);
CREATE INDEX IF NOT EXISTS idx_voice_locale_accent ON voice_locale(accent);

-- Add comments to table and columns
COMMENT ON TABLE voice_locale IS 'Stores voice locale information for ElevenLabs voice synthesis';
COMMENT ON COLUMN voice_locale.id IS 'Unique identifier for the voice locale';
COMMENT ON COLUMN voice_locale.display_name IS 'Display name of the voice';
COMMENT ON COLUMN voice_locale.description IS 'Detailed description of the voice characteristics';
COMMENT ON COLUMN voice_locale.voice_id IS 'ElevenLabs voice ID';
COMMENT ON COLUMN voice_locale.gender IS 'Voice gender (Male or Female)';
COMMENT ON COLUMN voice_locale.age IS 'Voice age group (Young, Middle-aged, or Older)';
COMMENT ON COLUMN voice_locale.accent IS 'Voice accent (British or American)';
COMMENT ON COLUMN voice_locale.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN voice_locale.updated_at IS 'Timestamp when the record was last updated';

-- Truncate table before inserting data
TRUNCATE TABLE voice_locale CASCADE;

-- Insert initial data
INSERT INTO voice_locale (display_name, description, voice_id, gender, age, accent) VALUES
    ('Tess', 'British girl, shy and curious', 'tLoqUTqlfad43AJil9ae', 'Female', 'Young', 'British'),
    ('Domi', 'American girl, assertive', 'AZnzlk1XvdvUeBnXmlld', 'Female', 'Young', 'American'),
    ('Valeria', 'British young lady, soft and calm', 'asI5kXBtm8kXmesclRSS', 'Female', 'Middle-aged', 'British'),
    ('Alessia', 'American lady, cheerful', 'RpwA0lB8s6tVGdpoMUrP', 'Female', 'Middle-aged', 'American'),
    ('Samara', 'British older woman, confident', '19STyYD15bswVz51nqLf', 'Female', 'Older', 'British'),
    ('Athena', 'American older woman, sultry', '3IErRyHnub5B02E4Kv7P', 'Female', 'Older', 'American'),
    ('Benji', 'British lad, flirty and casual', 'jyYV4jm5Wq39qXvc4ERa', 'Male', 'Young', 'British'),
    ('Jacky', 'American young man, cheerful', 'WTUK291rZZ9CLPCiFTfh', 'Male', 'Young', 'American'),
    ('Ian', 'British man, conversatinoal', 'v7AjIzCg6vdhCmXwBrb1', 'Male', 'Middle-aged', 'British'),
    ('Mark', 'American man, casual and cheerful', 'UgBBYS2sOqTuMpoF3BR0', 'Male', 'Middle-aged', 'American'),
    ('George', 'British older gentleman, conversational', 'JBFqnCBsd6RMkjVDRZzb', 'Male', 'Older', 'British'),
    ('Spuds', 'American grandma, wise', 'NOpBlnGInO9m6vDvFkFC', 'Male', 'Older', 'American');

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_voice_locale_updated_at
    BEFORE UPDATE ON voice_locale
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create a view for common voice queries
CREATE OR REPLACE VIEW voice_locale_summary AS
SELECT 
    id,
    display_name,
    description,
    voice_id,
    gender,
    age,
    accent,
    created_at
FROM voice_locale
ORDER BY gender, age, accent;

-- Create a function to get voice recommendations based on character traits
CREATE OR REPLACE FUNCTION get_voice_recommendations(
    p_gender TEXT,
    p_age TEXT,
    p_accent TEXT
) RETURNS TABLE (
    id UUID,
    display_name TEXT,
    description TEXT,
    voice_id TEXT,
    gender TEXT,
    age TEXT,
    accent TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        vl.id,
        vl.display_name,
        vl.description,
        vl.voice_id,
        vl.gender,
        vl.age,
        vl.accent
    FROM voice_locale vl
    WHERE 
        (p_gender IS NULL OR vl.gender = p_gender)
        AND (p_age IS NULL OR vl.age = p_age)
        AND (p_accent IS NULL OR vl.accent = p_accent)
    ORDER BY 
        CASE 
            WHEN vl.gender = p_gender THEN 1
            ELSE 2
        END,
        CASE 
            WHEN vl.age = p_age THEN 1
            ELSE 2
        END,
        CASE 
            WHEN vl.accent = p_accent THEN 1
            ELSE 2
        END;
END;
$$ LANGUAGE plpgsql; 