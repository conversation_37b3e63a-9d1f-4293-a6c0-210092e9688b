-- Add title column to stories table
ALTER TABLE stories ADD COLUMN IF NOT EXISTS title TEXT;

-- Update RLS policies
ALTER TABLE stories ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own stories" ON stories;
DROP POLICY IF EXISTS "Users can insert their own stories" ON stories;
DROP POLICY IF EXISTS "Users can update their own stories" ON stories;
DROP POLICY IF EXISTS "Users can delete their own stories" ON stories;

-- Create new policies
CREATE POLICY "Users can view their own stories"
ON stories FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own stories"
ON stories FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own stories"
ON stories FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own stories"
ON stories FOR DELETE
USING (auth.uid() = user_id); 