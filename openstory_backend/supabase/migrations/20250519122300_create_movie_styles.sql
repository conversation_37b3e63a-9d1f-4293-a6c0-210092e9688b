-- Create movie_styles table
CREATE TABLE IF NOT EXISTS movie_styles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    weight INTEGER NOT NULL DEFAULT 50,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS policies for movie_styles table
ALTER TABLE movie_styles ENABLE ROW LEVEL SECURITY;

-- Create policy for authenticated users to select movie_styles
CREATE POLICY "Allow authenticated users to select movie_styles"
    ON movie_styles FOR SELECT
    TO authenticated
    USING (true);

-- Create policy for authenticated users to insert movie_styles
CREATE POLICY "Allow authenticated users to insert movie_styles"
    ON movie_styles FOR INSERT
    TO authenticated
    WITH CHECK (true);

-- Create policy for authenticated users to update movie_styles
CREATE POLICY "Allow authenticated users to update movie_styles"
    ON movie_styles FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Create policy for authenticated users to delete movie_styles
CREATE POLICY "Allow authenticated users to delete movie_styles"
    ON movie_styles FOR DELETE
    TO authenticated
    USING (true); 