-- Drop existing constraints
ALTER TABLE shows DROP CONSTRAINT IF EXISTS shows_movie_style_id_fkey;
ALTER TABLE jobs DROP CONSTRAINT IF EXISTS jobs_movie_style_id_fkey;

-- Drop existing table if it exists
DROP TABLE IF EXISTS movie_styles;

-- Create movie_styles table with J<PERSON><PERSON><PERSON> column
CREATE TABLE movie_styles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    styles JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add movie_style_id back to shows, jobs, and movies tables
ALTER TABLE shows ADD COLUMN IF NOT EXISTS movie_style_id UUID REFERENCES movie_styles(id);
ALTER TABLE jobs ADD COLUMN IF NOT EXISTS movie_style_id UUID REFERENCES movie_styles(id);

-- Enable RLS
ALTER TABLE movie_styles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow authenticated users to select movie_styles" ON movie_styles;
DROP POLICY IF EXISTS "Allow authenticated users to insert movie_styles" ON movie_styles;
DROP POLICY IF EXISTS "Allow authenticated users to update movie_styles" ON movie_styles;
DROP POLICY IF EXISTS "Allow authenticated users to delete movie_styles" ON movie_styles;

-- Create policies for authenticated users
CREATE POLICY "Allow authenticated users to select movie_styles"
    ON movie_styles FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Allow authenticated users to insert movie_styles"
    ON movie_styles FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Allow authenticated users to update movie_styles"
    ON movie_styles FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Allow authenticated users to delete movie_styles"
    ON movie_styles FOR DELETE
    TO authenticated
    USING (true);

-- Insert the new movie styles data
INSERT INTO movie_styles (styles) VALUES (
    '[
        {
            "tag": "Use Case",
            "options": ["Drama", "Advertisement"]
        },
        {
            "tag": "Visual Style",
            "options": ["Realistic", "Pixar", "Ghibli", "Neo Art"]
        },
        {
            "tag": "World Remix",
            "options": ["Sex and the City", "Harry Potter"]
        },
        {
            "tag": "Vibes",
            "options": ["Engaging", "Think out of the box"]
        }
    ]'::jsonb
); 