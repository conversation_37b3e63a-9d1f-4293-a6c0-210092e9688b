-- Update stories table
ALTER TABLE stories
ADD COLUMN IF NOT EXISTS title TEXT NOT NULL,
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS status TEXT NOT NULL DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

-- Update frames table
ALTER TABLE frames
ADD COLUMN IF NOT EXISTS shot_name TEXT NOT NULL,
ADD COLUMN IF NOT EXISTS shot_type TEXT NOT NULL,
ADD COLUMN IF NOT EXISTS shot_setting TEXT NOT NULL,
ADD COLUMN IF NOT EXISTS shot_framing TEXT NOT NULL,
ADD COLUMN IF NOT EXISTS shot_angle TEXT NOT NULL,
ADD COLUMN IF NOT EXISTS shot_movement TEXT NOT NULL,
ADD COLUMN IF NOT EXISTS character_composition TEXT,
ADD COLUMN IF NOT EXISTS character_dialogue TEXT,
ADD COLUMN IF NOT EXISTS image_url TEXT,
ADD COLUMN IF NOT EXISTS status TEXT NOT NULL DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS story_id UUID NOT NULL,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

-- Update characters table
ALTER TABLE characters
ADD COLUMN IF NOT EXISTS name TEXT NOT NULL,
ADD COLUMN IF NOT EXISTS gender TEXT,
ADD COLUMN IF NOT EXISTS age TEXT,
ADD COLUMN IF NOT EXISTS appearance TEXT,
ADD COLUMN IF NOT EXISTS personality TEXT,
ADD COLUMN IF NOT EXISTS backstory TEXT,
ADD COLUMN IF NOT EXISTS oneliner TEXT,
ADD COLUMN IF NOT EXISTS voice_id TEXT,
ADD COLUMN IF NOT EXISTS story_id UUID,
ADD COLUMN IF NOT EXISTS creator_user_id TEXT,
ADD COLUMN IF NOT EXISTS avatar_urls JSONB,
ADD COLUMN IF NOT EXISTS preview_img TEXT,
ADD COLUMN IF NOT EXISTS is_public BOOLEAN,
ADD COLUMN IF NOT EXISTS voice JSONB,
ADD COLUMN IF NOT EXISTS voice_name TEXT,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

-- Create indexes
CREATE INDEX IF NOT EXISTS ix_stories_id ON stories(id);
CREATE INDEX IF NOT EXISTS ix_frames_id ON frames(id);
CREATE INDEX IF NOT EXISTS ix_frames_story_id ON frames(story_id);
CREATE INDEX IF NOT EXISTS ix_characters_id ON characters(id);
CREATE INDEX IF NOT EXISTS ix_characters_story_id ON characters(story_id);

-- Create foreign key constraints
ALTER TABLE frames
ADD CONSTRAINT fk_frames_story_id
FOREIGN KEY (story_id) REFERENCES stories(id);

ALTER TABLE characters
ADD CONSTRAINT fk_characters_story_id
FOREIGN KEY (story_id) REFERENCES stories(id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_stories_updated_at
    BEFORE UPDATE ON stories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_frames_updated_at
    BEFORE UPDATE ON frames
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_characters_updated_at
    BEFORE UPDATE ON characters
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column(); 