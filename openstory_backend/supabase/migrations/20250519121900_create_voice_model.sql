-- Create voices table
CREATE TABLE IF NOT EXISTS voices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    voice_id TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add voice_id column to characters table
ALTER TABLE characters ADD COLUMN IF NOT EXISTS voice_id UUID REFERENCES voices(id);

-- Add preview_img and is_public columns to characters table
ALTER TABLE characters ADD COLUMN IF NOT EXISTS preview_img TEXT;
ALTER TABLE characters ADD COLUMN IF NOT EXISTS is_public BOOLEAN NOT NULL DEFAULT false;

-- Migrate existing voice data
INSERT INTO voices (id, voice_id, description)
SELECT 
    gen_random_uuid(),
    voice_id,
    voice_description
FROM characters
WHERE voice_id IS NOT NULL OR voice_description IS NOT NULL;

-- Update character voice_id references
UPDATE characters c
SET voice_id = v.id
FROM voices v
WHERE c.voice_id = v.voice_id;

-- First, drop any views or triggers that might depend on voice_description
DO $$ 
DECLARE
    view_cmd text;
    trigger_cmd text;
BEGIN
    -- Drop any views that might reference voice_description
    FOR view_cmd IN 
        SELECT 'DROP VIEW IF EXISTS ' || quote_ident(schemaname) || '.' || quote_ident(viewname)
        FROM pg_views 
        WHERE schemaname = 'public' 
        AND definition LIKE '%voice_description%'
    LOOP
        EXECUTE view_cmd;
    END LOOP;
    
    -- Drop any triggers that might reference voice_description
    FOR trigger_cmd IN 
        SELECT 'DROP TRIGGER IF EXISTS ' || quote_ident(tgname) || ' ON characters'
        FROM pg_trigger 
        WHERE tgrelid = 'characters'::regclass 
        AND tgfoid IN (SELECT oid FROM pg_proc WHERE prosrc LIKE '%voice_description%')
    LOOP
        EXECUTE trigger_cmd;
    END LOOP;
END $$;

-- Now safely drop the old voice columns
ALTER TABLE characters DROP COLUMN IF EXISTS voice_description;
ALTER TABLE characters DROP COLUMN IF EXISTS voice_id;

-- Add RLS policies for voices table
ALTER TABLE voices ENABLE ROW LEVEL SECURITY;

-- Create policy for authenticated users to select voices
CREATE POLICY "Allow authenticated users to select voices"
    ON voices FOR SELECT
    TO authenticated
    USING (true);

-- Create policy for authenticated users to insert voices
CREATE POLICY "Allow authenticated users to insert voices"
    ON voices FOR INSERT
    TO authenticated
    WITH CHECK (true);

-- Create policy for authenticated users to update voices
CREATE POLICY "Allow authenticated users to update voices"
    ON voices FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Create policy for authenticated users to delete voices
CREATE POLICY "Allow authenticated users to delete voices"
    ON voices FOR DELETE
    TO authenticated
    USING (true); 