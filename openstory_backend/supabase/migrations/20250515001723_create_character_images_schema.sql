-- 1. Create the character_images table
CREATE TABLE public.character_images (
  id            uuid primary key default gen_random_uuid(),
  character_id  uuid not null references public.characters(id) on delete cascade,
  bucket        text not null default 'characters',   -- storage bucket name
  path          text not null,                       -- e.g. 'u123/sha.webp'
  width         int   not null,
  height        int   not null,
  mime_type     text  not null,
  file_size     int   not null,
  sha256        text  unique,                         -- dedupe / cache key
  created_at    timestamptz default now()
);

-- 2. Row-Level Security (RLS) for public.characters table
-- Assuming 'user_id' is the column in your 'characters' table that links to 'auth.users.id'
-- If your column name is different, please adjust 'c.user_id' and 'user_id' below.
ALTER TABLE public.characters ENABLE ROW LEVEL SECURITY;

-- Drop existing policy if it exists, to avoid error on re-run
DROP POLICY IF EXISTS "owner access" ON public.characters;

CREATE POLICY "owner access" ON public.characters
  FOR ALL -- Or specify SELECT, INSERT, UPDATE, DELETE as needed
  USING (auth.uid() = creator_user_id) -- Changed from user_id to creator_user_id based on your Character model
  WITH CHECK (auth.uid() = creator_user_id); -- Changed from user_id to creator_user_id

-- 3. Row-Level Security (RLS) for public.character_images table
ALTER TABLE public.character_images ENABLE ROW LEVEL SECURITY;

-- Drop existing policy if it exists, to avoid error on re-run
DROP POLICY IF EXISTS "owner via character" ON public.character_images;

CREATE POLICY "owner via character" ON public.character_images
  FOR ALL -- Or specify SELECT, INSERT, UPDATE, DELETE as needed
  USING (
    EXISTS (
      SELECT 1
      FROM public.characters c
      WHERE c.id = character_images.character_id AND c.creator_user_id = auth.uid() -- Changed from c.user_id to c.creator_user_id
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1
      FROM public.characters c
      WHERE c.id = character_images.character_id AND c.creator_user_id = auth.uid() -- Changed from c.user_id to c.creator_user_id
    )
  );

-- 4. Convenience view characters_with_images
CREATE OR REPLACE VIEW public.characters_with_images AS
SELECT
  c.*,
  COALESCE(
    json_agg(
      json_build_object(
        'url',    '/storage/v1/object/public/'||ci.bucket||'/'||ci.path, -- Assuming public URLs for simplicity in view, signed URLs handled by app
        'width',  ci.width,
        'height', ci.height,
        'bucket', ci.bucket,
        'path',   ci.path,
        'mime_type', ci.mime_type,
        'file_size', ci.file_size,
        'sha256',    ci.sha256,
        'image_id',  ci.id,
        'created_at', ci.created_at
      )
    ) FILTER (WHERE ci.id IS NOT NULL),
    '[]'::json
  ) AS images
FROM public.characters c
LEFT JOIN public.character_images ci ON ci.character_id = c.id
GROUP BY c.id;
