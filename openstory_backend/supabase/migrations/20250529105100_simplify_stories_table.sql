-- Create table if not exists
CREATE TABLE IF NOT EXISTS stories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    movie_style JSONB DEFAULT '{}',
    story_line TEXT,
    frames <PERSON><PERSON><PERSON><PERSON> DEFAULT '[]',
    result TEXT,
    result_time TEXT DEFAULT '0:00',
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ
);

-- Add indexes if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_stories_user_id') THEN
        CREATE INDEX idx_stories_user_id ON stories(user_id);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_stories_status') THEN
        CREATE INDEX idx_stories_status ON stories(status);
    END IF;
END $$;

-- Add RLS policies
ALTER TABLE stories ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own stories" ON stories;
DROP POLICY IF EXISTS "Users can insert their own stories" ON stories;
DROP POLICY IF EXISTS "Users can update their own stories" ON stories;
DROP POLICY IF EXISTS "Users can delete their own stories" ON stories;

-- Create new policies
CREATE POLICY "Users can view their own stories"
ON stories FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own stories"
ON stories FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own stories"
ON stories FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own stories"
ON stories FOR DELETE
USING (auth.uid() = user_id); 