-- This file has been temporarily disabled by renaming it to .sql.disabled
-- Original content was:
-- -- Create a function to seed data only if it doesn't exist yet
-- CREATE OR REPLACE FUNCTION seed_data()
-- RETURNS void AS $$
-- DECLARE
--   seed_count INTEGER;
-- BEGIN
--   -- Check if seed data already exists
--   SELECT COUNT(*) INTO seed_count FROM example_table;
--   
--   -- Only seed if the table is empty
--   IF seed_count = 0 THEN
--     -- Insert seed data
--     INSERT INTO example_table (id, name, description)
--     VALUES 
--       ('d5f5c4e0-db78-4db0-9a8f-1234567890ab', 'Example 1', 'Description for example 1'),
--       ('b9a8c7d6-e5f4-4db0-9a8f-1234567890cd', 'Example 2', 'Description for example 2'),
--       ('a1b2c3d4-e5f6-4db0-9a8f-1234567890ef', 'Example 3', 'Description for example 3');
--       
--     RAISE NOTICE 'Seed data inserted successfully';
--   ELSE
--     RAISE NOTICE 'Seed data already exists, skipping';
--   END IF;
-- END;
-- $$ LANGUAGE plpgsql;
-- 
-- -- Execute the seed function
-- SELECT seed_data();
-- 
-- -- Clean up (optional)
-- DROP FUNCTION IF EXISTS seed_data(); 