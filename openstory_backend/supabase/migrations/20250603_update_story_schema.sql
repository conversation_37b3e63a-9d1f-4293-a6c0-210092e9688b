-- Remove JSONB fields from stories table
ALTER TABLE stories
DROP COLUMN IF EXISTS characters,
DROP COLUMN IF EXISTS frames;

-- Update movie_style to be J<PERSON>NB
ALTER TABLE stories
ALTER COLUMN movie_style TYPE JSONB USING movie_style::J<PERSON><PERSON><PERSON>,
ALTER COLUMN movie_style SET DEFAULT '{}';

-- Add ON DELETE CASCADE to foreign keys
ALTER TABLE frames
DROP CONSTRAINT IF EXISTS fk_frames_story_id,
ADD CONSTRAINT fk_frames_story_id 
FOREIGN KEY (story_id) 
REFERENCES stories(id) 
ON DELETE CASCADE;

ALTER TABLE characters
DROP CONSTRAINT IF EXISTS fk_characters_story_id,
ADD CONSTRAINT fk_characters_story_id 
FOREIGN KEY (story_id) 
REFERENCES stories(id) 
ON DELETE CASCADE;

-- Add foreign key for creator_user_id in characters table
ALTER TABLE characters
DROP CONSTRAINT IF EXISTS fk_characters_creator_user_id,
ADD CONSTRAINT fk_characters_creator_user_id 
FOR<PERSON><PERSON><PERSON>EY (creator_user_id) 
REFERENCES users(id); 