-- Create enum type for visual_type
CREATE TYPE visual_type AS ENUM ('image', 'video', 'video_no_lipsync', 'video_with_lipsync');

-- Add new columns
ALTER TABLE frames ADD COLUMN IF NOT EXISTS source_url TEXT;
ALTER TABLE frames ADD COLUMN IF NOT EXISTS character_id UUID REFERENCES characters(id);

-- Migrate data from old columns to new ones
UPDATE frames 
SET source_url = COALESCE(image_url, video_url),
    visual_type = CASE 
        WHEN video_url IS NOT NULL THEN 'video'::visual_type
        ELSE 'image'::visual_type
    END;

-- Drop old columns
ALTER TABLE frames DROP COLUMN IF EXISTS image_url;
ALTER TABLE frames DROP COLUMN IF EXISTS video_url;
ALTER TABLE frames DROP COLUMN IF EXISTS frame_number;
ALTER TABLE frames DROP COLUMN IF EXISTS duration_seconds;

-- First drop the default value
ALTER TABLE frames ALTER COLUMN visual_type DROP DEFAULT;

-- Create a temporary column for the conversion
ALTER TABLE frames ADD COLUMN visual_type_new visual_type;

-- Update the temporary column with converted values
UPDATE frames SET visual_type_new = 
    CASE 
        WHEN visual_type = 'image' THEN 'image'::visual_type
        WHEN visual_type = 'video' THEN 'video'::visual_type
        WHEN visual_type = 'video_no_lipsync' THEN 'video_no_lipsync'::visual_type
        WHEN visual_type = 'video_with_lipsync' THEN 'video_with_lipsync'::visual_type
        ELSE 'image'::visual_type
    END;

-- Drop the old column and rename the new one
ALTER TABLE frames DROP COLUMN visual_type;
ALTER TABLE frames RENAME COLUMN visual_type_new TO visual_type;

-- Add NOT NULL constraint and new default value
ALTER TABLE frames ALTER COLUMN visual_type SET NOT NULL;
ALTER TABLE frames ALTER COLUMN visual_type SET DEFAULT 'image'::visual_type; 