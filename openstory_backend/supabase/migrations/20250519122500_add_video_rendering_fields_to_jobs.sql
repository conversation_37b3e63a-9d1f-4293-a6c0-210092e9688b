-- Add video rendering specific fields to jobs table
ALTER TABLE jobs
    ADD COLUMN IF NOT EXISTS frames JSONB,
    ADD COLUMN IF NOT EXISTS output_video_url TEXT,
    ADD COLUMN IF NOT EXISTS render_settings JSONB,
    ADD COLUMN IF NOT EXISTS progress INTEGER DEFAULT 0,
    ADD COLUMN IF NOT EXISTS total_frames INTEGER,
    ADD COLUMN IF NOT EXISTS render_start_time TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS render_end_time TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS error_details JSONB;

-- Add comments to the new columns
COMMENT ON COLUMN jobs.frames IS 'List of frame IDs to be rendered into a video';
COMMENT ON COLUMN jobs.output_video_url IS 'URL of the generated video after rendering';
COMMENT ON COLUMN jobs.render_settings IS 'Configuration for video rendering (resolution, fps, transitions, etc.)';
COMMENT ON COLUMN jobs.progress IS 'Rendering progress (0-100)';
COMMENT ON COLUMN jobs.total_frames IS 'Total number of frames to be rendered';
COMMENT ON COLUMN jobs.render_start_time IS 'Time when rendering started';
COMMENT ON COLUMN jobs.render_end_time IS 'Time when rendering completed';
COMMENT ON COLUMN jobs.error_details IS 'Detailed error information if rendering fails'; 