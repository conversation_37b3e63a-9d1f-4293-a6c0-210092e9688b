-- Drop existing constraints
ALTER TABLE shows DROP CONSTRAINT IF EXISTS shows_movie_style_id_fkey;
ALTER TABLE jobs DROP CONSTRAINT IF EXISTS jobs_movie_style_id_fkey;

-- Drop existing table if it exists
DROP TABLE IF EXISTS movie_styles;

-- Create movie_styles table with J<PERSON><PERSON><PERSON> column
CREATE TABLE movie_styles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    styles JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add movie_style_id back to shows, jobs, and movies tables
ALTER TABLE shows ADD COLUMN IF NOT EXISTS movie_style_id UUID REFERENCES movie_styles(id);
ALTER TABLE jobs ADD COLUMN IF NOT EXISTS movie_style_id UUID REFERENCES movie_styles(id);

-- Enable RLS
ALTER TABLE movie_styles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow authenticated users to select movie_styles" ON movie_styles;
DROP POLICY IF EXISTS "Allow authenticated users to insert movie_styles" ON movie_styles;
DROP POLICY IF EXISTS "Allow authenticated users to update movie_styles" ON movie_styles;
DROP POLICY IF EXISTS "Allow authenticated users to delete movie_styles" ON movie_styles;

-- Create policies for authenticated users
CREATE POLICY "Allow authenticated users to select movie_styles"
    ON movie_styles FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Allow authenticated users to insert movie_styles"
    ON movie_styles FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Allow authenticated users to update movie_styles"
    ON movie_styles FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Allow authenticated users to delete movie_styles"
    ON movie_styles FOR DELETE
    TO authenticated
    USING (true);

-- Insert default movie styles as separate records
INSERT INTO movie_styles (styles) VALUES 
('{
    "category": "Use Case",
    "items": [
        "Commercial",
        "Social Media",
        "Film",
        "TV Show",
        "Documentary",
        "Music Video",
        "Animation",
        "Gaming",
        "Educational",
        "Corporate",
        "Event",
        "Personal",
        "Experimental",
        "Artistic",
        "Promotional"
    ]
}'::jsonb),
('{
    "category": "Visual Style",
    "items": [
        "Realistic",
        "Cartoon",
        "Anime",
        "3D",
        "2D",
        "Stop Motion",
        "Claymation",
        "Pixel Art",
        "Watercolor",
        "Oil Painting",
        "Sketch",
        "Comic Book",
        "Vintage",
        "Modern",
        "Minimalist"
    ]
}'::jsonb),
('{
    "category": "World Remix",
    "items": [
        "Fantasy",
        "Sci-Fi",
        "Historical",
        "Contemporary",
        "Post-Apocalyptic",
        "Steampunk",
        "Cyberpunk",
        "Western",
        "Noir",
        "Horror",
        "Romance",
        "Action",
        "Adventure",
        "Mystery",
        "Comedy"
    ]
}'::jsonb),
('{
    "category": "Vibes",
    "items": [
        "Dramatic",
        "Comedic",
        "Romantic",
        "Mysterious",
        "Horror",
        "Action",
        "Adventure",
        "Educational",
        "Inspirational",
        "Emotional",
        "Suspenseful",
        "Relaxing",
        "Energetic",
        "Melancholic",
-- Insert default movie styles
INSERT INTO movie_styles (styles) VALUES ('[
    {
        "category": "Use Case",
        "items": [
            "Commercial",
            "Social Media",
            "Film",
            "TV Show",
            "Documentary",
            "Music Video",
            "Animation",
            "Gaming",
            "Educational",
            "Corporate",
            "Event",
            "Personal",
            "Experimental",
            "Artistic",
            "Promotional"
        ]
    },
    {
        "category": "Visual Style",
        "items": [
            "Realistic",
            "Cartoon",
            "Anime",
            "3D",
            "2D",
            "Stop Motion",
            "Claymation",
            "Pixel Art",
            "Watercolor",
            "Oil Painting",
            "Sketch",
            "Comic Book",
            "Vintage",
            "Modern",
            "Minimalist"
        ]
    },
    {
        "category": "World Remix",
        "items": [
            "Fantasy",
            "Sci-Fi",
            "Historical",
            "Contemporary",
            "Post-Apocalyptic",
            "Steampunk",
            "Cyberpunk",
            "Western",
            "Noir",
            "Horror",
            "Romance",
            "Action",
            "Adventure",
            "Mystery",
            "Comedy"
        ]
    },
    {
        "category": "Vibes",
        "items": [
            "Dramatic",
            "Comedic",
            "Romantic",
            "Mysterious",
            "Horror",
            "Action",
            "Adventure",
            "Educational",
            "Inspirational",
            "Emotional",
            "Suspenseful",
            "Relaxing",
            "Energetic",
            "Melancholic",
            "Uplifting"
        ]
    }
]'::jsonb); 