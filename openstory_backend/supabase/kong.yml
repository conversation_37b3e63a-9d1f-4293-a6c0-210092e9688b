_format_version: "2.1"
_transform: true

services:
  - name: auth-v1
    url: http://auth:9999/verify
    routes:
      - name: auth-v1-route
        strip_path: true
        paths:
          - /auth/v1/verify
    plugins:
      - name: cors
  - name: auth-v1-admin
    url: http://auth:9999/admin
    routes:
      - name: auth-v1-admin-route
        strip_path: true
        paths:
          - /auth/v1/admin
    plugins:
      - name: cors
  - name: rest-v1
    url: http://rest:3000
    routes:
      - name: rest-v1-route
        strip_path: true
        paths:
          - /rest/v1
    plugins:
      - name: cors
  - name: realtime-v1
    url: http://realtime:4000/socket/websocket
    routes:
      - name: realtime-v1-route
        strip_path: true
        paths:
          - /realtime/v1/websocket
    plugins:
      - name: cors
  - name: storage-v1
    url: http://storage:5000
    routes:
      - name: storage-v1-route
        strip_path: true
        paths:
          - /storage/v1
    plugins:
      - name: cors 