-- This file is automatically executed when running 'supabase start' or creating a new instance

-- Example users table seed (replace with your actual tables)
INSERT INTO public.users (id, email, username, created_at)
VALUES 
  ('00000000-0000-0000-0000-000000000001', '<EMAIL>', 'admin', now()),
  ('00000000-0000-0000-0000-000000000002', '<EMAIL>', 'demo', now())
ON CONFLICT (id) DO NOTHING;

-- More tables can be added here
-- The ON CONFLICT clause ensures this is idempotent and can be run multiple times

-- Insert test data - NOTE: Using uuid_generate_v4() to create random UUIDs instead of hardcoded values

-- Create test users
INSERT INTO public.users (email, username, first_name, display_name)
VALUES 
  ('<EMAIL>', 'testuser', 'Test', 'Test User'),
  ('<EMAIL>', 'creatoruser', 'Creator', 'Content Creator')
ON CONFLICT (username) DO NOTHING;

-- <PERSON>reate test shows
INSERT INTO public.shows (user_id, title, description)
VALUES
  ((SELECT id FROM public.users WHERE email = '<EMAIL>'), 'My First Drama', 'A thrilling series about software development'),
  ((SELECT id FROM public.users WHERE email = '<EMAIL>'), 'Mystery Theater', 'A mysterious drama with unexpected twists');

-- Create test episodes
INSERT INTO public.episodes (show_id, title, synopsis, oneliner, permission)
VALUES
  ((SELECT id FROM public.shows WHERE title = 'My First Drama'), 'Pilot Episode', 'The team embarks on a new project', 'The adventure begins', 'private'),
  ((SELECT id FROM public.shows WHERE title = 'My First Drama'), 'The Bug', 'A mysterious bug appears in production', 'Chaos ensues', 'public'),
  ((SELECT id FROM public.shows WHERE title = 'Mystery Theater'), 'First Clue', 'The detective finds the first clue', 'The hunt begins', 'public');

-- Create test characters
INSERT INTO public.characters (creator_user_id, name, gender, age, oneliner, personality, appearance)
VALUES
  ((SELECT id FROM public.users WHERE email = '<EMAIL>'), 'John', 'male', 30, 'The brilliant but eccentric developer', 'Quirky, smart, detail-oriented', 'Tall with messy hair and glasses'),
  ((SELECT id FROM public.users WHERE email = '<EMAIL>'), 'Sarah', 'female', 28, 'The no-nonsense project manager', 'Organized, direct, logical', 'Professional attire with a sleek ponytail'),
  ((SELECT id FROM public.users WHERE email = '<EMAIL>'), 'Detective Morgan', 'male', 45, 'The detective who never gives up', 'Persistent, observant, intuitive', 'Trench coat and a well-worn hat');

-- Create test character progressions
INSERT INTO public.character_progressions (character_id, episode_id, state_description)
VALUES
  ((SELECT id FROM public.characters WHERE name = 'John'), (SELECT id FROM public.episodes WHERE title = 'Pilot Episode'), 'Excited about the new project'),
  ((SELECT id FROM public.characters WHERE name = 'Sarah'), (SELECT id FROM public.episodes WHERE title = 'Pilot Episode'), 'Planning the project timeline'),
  ((SELECT id FROM public.characters WHERE name = 'Detective Morgan'), (SELECT id FROM public.episodes WHERE title = 'First Clue'), 'Analyzing the crime scene');

-- Create test frames
INSERT INTO public.frames (episode_id, frame_number, visual_type, duration_seconds)
VALUES
  ((SELECT id FROM public.episodes WHERE title = 'Pilot Episode'), 1, 'image', 5),
  ((SELECT id FROM public.episodes WHERE title = 'Pilot Episode'), 2, 'image', 5),
  ((SELECT id FROM public.episodes WHERE title = 'First Clue'), 1, 'image', 5);

-- Create test frame components
INSERT INTO public.frame_components (frame_id, component_type, content, start_time_ms, duration_ms)
VALUES
  ((SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'Pilot Episode') AND frame_number = 1), 'dialogue', 'Let''s start this new project!', 0, 3000),
  ((SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'Pilot Episode') AND frame_number = 2), 'dialogue', 'I have the timeline ready.', 0, 3000),
  ((SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'First Clue') AND frame_number = 1), 'sound_effect', 'Dramatic music', 0, 5000);

-- Create test sessions
INSERT INTO public.sessions (user_id, character_progression_id)
VALUES
  ((SELECT id FROM public.users WHERE email = '<EMAIL>'), (SELECT id FROM public.character_progressions WHERE character_id = (SELECT id FROM public.characters WHERE name = 'Detective Morgan')));

-- Create test messages
INSERT INTO public.messages (session_id, sender, content)
VALUES
  ((SELECT id FROM public.sessions LIMIT 1), 'user', 'What do you think about the case?'),
  ((SELECT id FROM public.sessions LIMIT 1), 'character', 'I believe we''re dealing with a sophisticated criminal. The clues suggest premeditation.');

-- Create test jobs
INSERT INTO public.jobs (job_type, status, payload)
VALUES
  ('video_generation', 'pending', '{"quality": "high", "duration": 10}'),
  ('video_generation', 'completed', '{"quality": "medium", "duration": 5}');

-- First <NAME_EMAIL> user exists
INSERT INTO public.users (id, email, username, created_at)
VALUES 
  ('11111111-1111-1111-1111-111111111111', '<EMAIL>', 'testuseralt', now())
ON CONFLICT (email) DO UPDATE SET
  username = 'testuseralt',
  updated_at = now()
RETURNING id;

-- Create <NAME_EMAIL>
INSERT INTO public.shows (id, user_id, title, description, image_url, is_public, category, created_at, updated_at)
VALUES
  ('22222222-2222-2222-2222-222222222201', 
   (SELECT id FROM public.users WHERE email = '<EMAIL>'), 
   'The Endless Code', 
   'A dramatic series following a team of developers as they build a revolutionary app while navigating personal and professional challenges.',
   'https://images.unsplash.com/photo-1504384308090-c894fdcc538d',
   true,
   'Technology',
   now(),
   now()),
   
  ('22222222-2222-2222-2222-222222222202', 
   (SELECT id FROM public.users WHERE email = '<EMAIL>'), 
   'Silicon Valley Dreams', 
   'The rise and fall of a startup founder who sacrifices everything for success.',
   'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4',
   true,
   'Business',
   now(),
   now()),
   
  ('22222222-2222-2222-2222-222222222203', 
   (SELECT id FROM public.users WHERE email = '<EMAIL>'), 
   'Midnight Mystery', 
   'A detective solves complex cases in a city full of secrets.',
   'https://images.unsplash.com/photo-1509628061459-1328d06c2ced',
   true,
   'Mystery',
   now(),
   now())
ON CONFLICT (id) DO NOTHING;

-- Create episodes for the shows
INSERT INTO public.episodes (id, show_id, title, synopsis, oneliner, permission, image_url, created_at)
VALUES
  -- Episodes for "The Endless Code"
  ('33333333-3333-3333-3333-333333333301',
   (SELECT id FROM public.shows WHERE title = 'The Endless Code'),
   'Genesis',
   'The team begins work on a revolutionary new app that promises to change the world.',
   'Every line of code has a beginning.',
   'public',
   'https://images.unsplash.com/photo-1498050108023-c5249f4df085',
   now()),
   
  ('33333333-3333-3333-3333-333333333302',
   (SELECT id FROM public.shows WHERE title = 'The Endless Code'),
   'Deadline',
   'As the first milestone approaches, tensions rise and the team faces unexpected technical challenges.',
   'Time waits for no developer.',
   'public',
   'https://images.unsplash.com/photo-1434030216411-0b793f4b4173',
   now()),
   
  -- Episodes for "Silicon Valley Dreams"
  ('33333333-3333-3333-3333-333333333303',
   (SELECT id FROM public.shows WHERE title = 'Silicon Valley Dreams'),
   'The Pitch',
   'A brilliant but inexperienced founder makes her first pitch to potential investors.',
   'Sometimes the best ideas sound crazy at first.',
   'public',
   'https://images.unsplash.com/photo-1556761175-5973dc0f32e7',
   now()),
   
  -- Episodes for "Midnight Mystery"
  ('33333333-3333-3333-3333-333333333304',
   (SELECT id FROM public.shows WHERE title = 'Midnight Mystery'),
   'The First Clue',
   'Detective Morgan discovers a cryptic message at a crime scene that will change the course of the investigation.',
   'Every mystery begins with a single clue.',
   'public',
   'https://images.unsplash.com/photo-1594453295049-004272297087',
   now())
ON CONFLICT (id) DO NOTHING;

-- Create characters
INSERT INTO public.characters (id, creator_user_id, name, gender, age, oneliner, personality, appearance, created_at)
VALUES
  -- Characters for "The Endless Code"
  ('44444444-4444-4444-4444-444444444401',
   (SELECT id FROM public.users WHERE email = '<EMAIL>'),
   'Maya Chen',
   'female',
   28,
   'Brilliant lead developer with uncompromising standards.',
   'Perfectionist, passionate, focused, sometimes impatient. Values efficiency and elegant code above all else.',
   'Always dressed in simple, functional clothing. Short black hair, usually wears glasses and is rarely seen without her laptop.',
   now()),
   
  ('44444444-4444-4444-4444-444444444402',
   (SELECT id FROM public.users WHERE email = '<EMAIL>'),
   'Jackson Miller',
   'male',
   32,
   'Creative UX designer who brings humanity to technology.',
   'Empathetic, creative, diplomatic, occasionally scattered. Believes technology should serve human needs above all.',
   'Casual but stylish clothing, well-groomed beard, always sketching in his notebook.',
   now()),
   
  -- Characters for "Silicon Valley Dreams"
  ('44444444-4444-4444-4444-444444444403',
   (SELECT id FROM public.users WHERE email = '<EMAIL>'),
   'Sophia Wagner',
   'female',
   25,
   'First-time founder with a revolutionary idea and everything to prove.',
   'Ambitious, idealistic, resilient, somewhat naive. Willing to sacrifice everything for her vision.',
   'Professional attire that is slightly too formal for Silicon Valley. Always put-together even when exhausted.',
   now()),
   
  -- Characters for "Midnight Mystery"
  ('44444444-4444-4444-4444-444444444404',
   (SELECT id FROM public.users WHERE email = '<EMAIL>'),
   'Detective Alex Morgan',
   'male',
   45,
   'Veteran detective who sees patterns others miss.',
   'Observant, intuitive, methodical, slightly cynical. Dedicated to finding truth regardless of consequences.',
   'Classic trench coat, practical suits, perpetual five oclock shadow. Eyes that miss nothing.',
   now())
ON CONFLICT (id) DO NOTHING;

-- Create frames for episodes
INSERT INTO public.frames (id, episode_id, frame_number, visual_type, duration_seconds, created_at)
VALUES
  -- Frames for "Genesis" episode
  ('55555555-5555-5555-5555-555555555501',
   (SELECT id FROM public.episodes WHERE title = 'Genesis'),
   1,
   'image',
   8,
   now()),
   
  ('55555555-5555-5555-5555-555555555502',
   (SELECT id FROM public.episodes WHERE title = 'Genesis'),
   2,
   'image',
   5,
   now()),
   
  -- Frames for "Deadline" episode
  ('55555555-5555-5555-5555-555555555503',
   (SELECT id FROM public.episodes WHERE title = 'Deadline'),
   1,
   'image',
   10,
   now()),
   
  -- Frames for "The Pitch" episode
  ('55555555-5555-5555-5555-555555555504',
   (SELECT id FROM public.episodes WHERE title = 'The Pitch'),
   1,
   'image',
   12,
   now()),
   
  -- Frames for "The First Clue" episode
  ('55555555-5555-5555-5555-555555555505',
   (SELECT id FROM public.episodes WHERE title = 'The First Clue'),
   1,
   'image',
   15,
   now())
ON CONFLICT (id) DO NOTHING;

-- Create frame components
INSERT INTO public.frame_components (id, frame_id, component_type, content, start_time_ms, duration_ms, created_at)
VALUES
  -- Components for Genesis episode, frame 1
  ('66666666-6666-6666-6666-**********01',
   (SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'Genesis') AND frame_number = 1),
   'dialogue',
   'This is it, team. The project that could change everything.',
   0,
   4000,
   now()),
   
  ('66666666-6666-6666-6666-**********02',
   (SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'Genesis') AND frame_number = 1),
   'dialogue',
   'Maya Chen says: I believe in our vision.',
   0,
   8000,
   now()),
   
  ('66666666-6666-6666-6666-**********03',
   (SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'Genesis') AND frame_number = 1),
   'sound_effect',
   'Office ambience',
   0,
   8000,
   now()),
   
  -- Components for Genesis episode, frame 2
  ('66666666-6666-6666-6666-**********04',
   (SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'Genesis') AND frame_number = 2),
   'dialogue',
   'I have never seen code architecture this elegant before.',
   0,
   5000,
   now()),
   
  ('66666666-6666-6666-6666-**********05',
   (SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'Genesis') AND frame_number = 2),
   'dialogue',
   'Jackson Miller observes the code carefully.',
   0,
   5000,
   now()),
   
  -- Components for Deadline episode, frame 1
  ('66666666-6666-6666-6666-**********06',
   (SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'Deadline') AND frame_number = 1),
   'dialogue',
   'We are running out of time. The demo is in two days and we still have critical bugs.',
   0,
   6000,
   now()),
   
  ('66666666-6666-6666-6666-**********07',
   (SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'Deadline') AND frame_number = 1),
   'dialogue',
   'Maya Chen looks stressed as she analyzes the error logs.',
   0,
   10000,
   now()),
   
  ('66666666-6666-6666-6666-**********08',
   (SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'Deadline') AND frame_number = 1),
   'sound_effect',
   'Tense music',
   0,
   10000,
   now()),
   
  -- Components for The Pitch episode, frame 1
  ('66666666-6666-6666-6666-**********09',
   (SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'The Pitch') AND frame_number = 1),
   'dialogue',
   'Our platform will revolutionize how people connect with healthcare providers.',
   2000,
   7000,
   now()),
   
  ('66666666-6666-6666-6666-**********10',
   (SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'The Pitch') AND frame_number = 1),
   'dialogue',
   'Sophia Wagner stands confidently in front of the investors.',
   0,
   12000,
   now()),
   
  -- Components for The First Clue episode, frame 1
  ('66666666-6666-6666-6666-**********11',
   (SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'The First Clue') AND frame_number = 1),
   'dialogue',
   'These numbers are not random. They are coordinates.',
   3000,
   5000,
   now()),
   
  ('66666666-6666-6666-6666-**********12',
   (SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'The First Clue') AND frame_number = 1),
   'dialogue',
   'Detective Alex Morgan examines the evidence closely.',
   0,
   15000,
   now()),
   
  ('66666666-6666-6666-6666-**********13',
   (SELECT id FROM public.frames WHERE episode_id = (SELECT id FROM public.episodes WHERE title = 'The First Clue') AND frame_number = 1),
   'sound_effect',
   'Mystery reveal',
   8000,
   3000,
   now())
ON CONFLICT (id) DO NOTHING;

-- Add character progressions to link characters to episodes
INSERT INTO public.character_progressions (id, character_id, episode_id, state_description, created_at)
VALUES
  -- Maya in Genesis
  ('77777777-7777-7777-7777-777777777701',
   (SELECT id FROM public.characters WHERE name = 'Maya Chen'),
   (SELECT id FROM public.episodes WHERE title = 'Genesis'),
   'Excited but anxious about leading such an important project',
   now()),
   
  -- Jackson in Genesis
  ('77777777-7777-7777-7777-777777777702',
   (SELECT id FROM public.characters WHERE name = 'Jackson Miller'),
   (SELECT id FROM public.episodes WHERE title = 'Genesis'),
   'Inspired by the possibilities of the new project',
   now()),
   
  -- Maya in Deadline
  ('77777777-7777-7777-7777-777777777703',
   (SELECT id FROM public.characters WHERE name = 'Maya Chen'),
   (SELECT id FROM public.episodes WHERE title = 'Deadline'),
   'Stressed and working around the clock to fix bugs',
   now()),
   
  -- Sophia in The Pitch
  ('77777777-7777-7777-7777-777777777704',
   (SELECT id FROM public.characters WHERE name = 'Sophia Wagner'),
   (SELECT id FROM public.episodes WHERE title = 'The Pitch'),
   'Nervous but determined to convince investors',
   now()),
   
  -- Detective Morgan in The First Clue
  ('77777777-7777-7777-7777-777777777705',
   (SELECT id FROM public.characters WHERE name = 'Detective Alex Morgan'),
   (SELECT id FROM public.episodes WHERE title = 'The First Clue'),
   'Focused and in his element analyzing the evidence',
   now())
ON CONFLICT (id) DO NOTHING;

-- Placeholder for seed data. Currently empty to avoid errors during db reset. 