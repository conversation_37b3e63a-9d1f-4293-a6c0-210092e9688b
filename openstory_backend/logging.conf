[loggers]
keys=root,access,app

[handlers]
keys=access<PERSON><PERSON><PERSON>,info<PERSON><PERSON><PERSON>,error<PERSON><PERSON><PERSON>,console<PERSON><PERSON>ler

[formatters]
keys=defaultFormatter,accessFormatter

[logger_root]
level=NOTSET
handlers=

[logger_app]
level=DEBUG
handlers=infoHandler,errorHandler,consoleHandler
qualname=app
propagate=0

[logger_access]
level=INFO
handlers=accessHandler
qualname=access
propagate=0

[handler_accessHandler]
class=logging.handlers.TimedRotatingFileHandler
level=INFO
formatter=accessFormatter
args=('logs/access.log', 'midnight', 1, 7)

[handler_infoHandler]
class=logging.handlers.TimedRotatingFileHandler
level=INFO
formatter=defaultFormatter
args=('logs/info.log', 'midnight', 1, 7)

[handler_errorHandler]
class=logging.handlers.TimedRotatingFileHandler
level=ERROR
formatter=defaultFormatter
args=('logs/error.log', 'midnight', 1, 7)

[handler_consoleHandler]
class=StreamHandler
level=DEBUG
formatter=defaultFormatter
args=(sys.stdout,)

[formatter_accessFormatter]
format=%(asctime)s|%(message)s
datefmt=%Y-%m-%d %H:%M:%S

[formatter_defaultFormatter]
format=[%(asctime)s][%(levelname)s][%(filename)s:%(lineno)d] %(message)s
datefmt=%Y-%m-%d %H:%M:%S

