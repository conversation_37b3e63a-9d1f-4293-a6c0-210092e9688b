#!/usr/bin/env python3
"""
Test script for the Agent API endpoints
"""

import asyncio
import aiohttp
import json

async def test_agent_api():
    """Test the agent API endpoints"""
    base_url = "http://localhost:8000"
    
    # Test data
    test_message = "Help me create a story about a brave knight who saves a village from a dragon"
    
    async with aiohttp.ClientSession() as session:
        print("🧪 Testing Agent API endpoints...")
        
        # Test non-streaming endpoint
        print("\n1. Testing non-streaming agent endpoint...")
        try:
            async with session.post(
                f"{base_url}/api/v1/chat/agent",
                json={
                    "message": test_message,
                    "conversation_history": [],
                    "model": "gpt-4o-mini",
                    "temperature": 0.7,
                    "max_tokens": 1000
                },
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Non-streaming response received")
                    print(f"📝 Response preview: {data['message'][:200]}...")
                else:
                    print(f"❌ Non-streaming request failed: {response.status}")
                    text = await response.text()
                    print(f"Error: {text}")
        except Exception as e:
            print(f"❌ Non-streaming request error: {str(e)}")
        
        # Test streaming endpoint
        print("\n2. Testing streaming agent endpoint...")
        try:
            async with session.post(
                f"{base_url}/api/v1/chat/agent/stream",
                json={
                    "message": test_message,
                    "conversation_history": [],
                    "model": "gpt-4o-mini",
                    "temperature": 0.7,
                    "max_tokens": 1000
                },
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    print("✅ Streaming response started")
                    content_chunks = []
                    async for line in response.content:
                        line_str = line.decode('utf-8').strip()
                        if line_str.startswith('data: '):
                            data_str = line_str[6:]  # Remove 'data: ' prefix
                            try:
                                data = json.loads(data_str)
                                if data.get('type') == 'content':
                                    content_chunks.append(data.get('data', ''))
                                elif data.get('type') == 'finish':
                                    print("✅ Streaming completed")
                                    break
                                elif data.get('type') == 'error':
                                    print(f"❌ Streaming error: {data.get('detail')}")
                                    break
                            except json.JSONDecodeError:
                                continue
                    
                    full_content = ''.join(content_chunks)
                    print(f"📝 Streaming content preview: {full_content[:200]}...")
                else:
                    print(f"❌ Streaming request failed: {response.status}")
                    text = await response.text()
                    print(f"Error: {text}")
        except Exception as e:
            print(f"❌ Streaming request error: {str(e)}")
        
        print("\n🎉 Agent API test completed!")

if __name__ == "__main__":
    asyncio.run(test_agent_api())
