#!/usr/bin/env python3
import gradio as gr

def simple_func(name):
    return f"Hello, {name}!"

# Create a very simple app with tabs
with gr.Blocks() as demo:
    gr.Markdown("# Simple Tabs Test")
    
    with gr.Tabs():
        with gr.Tab("Tab 1"):
            name = gr.Textbox(label="Name")
            output = gr.Textbox(label="Output")
            button = gr.<PERSON><PERSON>("Greet")
            button.click(simple_func, inputs=name, outputs=output)
            
        with gr.Tab("Tab 2"):
            gr.Markdown("This is Tab 2")
            
        with gr.Tab("Tab 3"):
            gr.Markdown("This is Tab 3")

if __name__ == "__main__":
    demo.launch(server_name="127.0.0.1", server_port=7861) 