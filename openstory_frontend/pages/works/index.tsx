import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import NavBar from '@/components/NavBar';
import { Italiana } from 'next/font/google';
import { Folder, Plus } from 'lucide-react';
import { useAuthStore } from '@/store/authStore';
import { useTaskStore } from '@/store/task';
import { storyBoardService } from '@/services/storyBoardService';
import { StoryboardFrame } from '@/components/StoryBoard/StoryboardGrid';

const italiana = Italiana({ weight: '400', subsets: ['latin'] });

interface ResponseState {
  id: string;
  title: string;
  concept: string;
  movie_style: Record<string, string | null>;
  story_line: string;
  frames: StoryboardFrame[];
  characters: any[];
  result: any | null;
  result_time: string;
  created_at: Date;
  updated_at: Date; 
}

const WorksPage = () => {
  const router = useRouter();
  const [stories, setStories] = useState<ResponseState[]>([]);

  useEffect(() => {
    const fetchStories = async () => {
      try {
        const user = useAuthStore.getState().user;
        if (!user?.id) return;
        const result = await storyBoardService.getStories(user.id);
        console.log('result', result);
        setStories(result);
      } catch (error) {
        console.error('Error fetching stories:', error);
      }
    };

    fetchStories();
  }, []);
  
  const handleEditStory = async (story: ResponseState) => {
    await useTaskStore.setState({
      ...story,
      id: story.id,
      frames: story.frames, 
      title: story.title,
      concept: story.concept,
      movie_style: story.movie_style,
      story_line: story.story_line,
      result: story.result,
      result_time: story.result_time,
    });
    router.push('/storyBoard');
  }

  const getLastEditTime = (date: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays === 0) {
      return `Last edited ${Math.round(diffTime / (1000 * 60 * 60))} hours ago`;
    } else {
      return `Last edited ${diffDays} days ago`;
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-white via-blue-50 to-blue-100">
      {/* NavBar at the top */}
      <NavBar activePage="works" />
      
      {/* Main content */}
      <div className="flex flex-col items-center pt-28 px-6">
        <h1 className={`text-5xl ${italiana.className} text-blue-400 mb-8`}>My Works</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl w-full">
          {/* Create new story card */}
          <a 
            href="/create"
            className="bg-white rounded-xl p-4 shadow-md hover:shadow-lg transition-all cursor-pointer h-64 flex flex-col items-center justify-center border-2 border-dashed border-blue-300 hover:border-blue-400 group"
          >
            <div className="w-16 h-16 rounded-full bg-blue-50 flex items-center justify-center mb-4 group-hover:bg-blue-100 transition-colors">
              <Plus size={32} className="text-blue-400" />
            </div>
            <h3 className="text-xl font-medium text-blue-500">Create New Story</h3>
          </a>

          {/* Existing works */}
          {stories.map((item, index) => (
            <div 
              key={index} 
              onClick={() => handleEditStory(item)}
              className="bg-white rounded-xl p-4 shadow-md hover:shadow-lg transition-shadow cursor-pointer h-64 flex flex-col"
            >
              <div className="bg-gray-100 rounded-lg h-40 mb-4 relative overflow-hidden">
                {/* Thumbnail preview */}
                <img src={item?.frames?.[0]?.image_url || '/placeholder-image.svg'} alt="Thumbnail" className="w-full h-full object-cover" />
                <div className="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                {item.result_time}
                </div>
              </div>
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-xl font-medium">My Story #{item.title}</h3>
                  <p className="text-gray-500 text-sm mt-1">{getLastEditTime(new Date(item.updated_at || item.created_at))}</p>
                </div>
                <Folder size={20} className="text-blue-400 mt-1" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default WorksPage; 