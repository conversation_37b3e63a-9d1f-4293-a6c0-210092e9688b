import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import styled from 'styled-components';
import { ChevronDown, ChevronUp, AlertCircle, ArrowUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { koho } from "@/theme"; // Import KoHo font config
import { chatService, ChatMessage } from "@/services/chatService";

// Styled send button with arrow icon
const SendButton = styled.button`
  background: #2389e9;
  padding: 0;
  width: 32px;
  height: 32px;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  border-radius: 50%;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.2s ease-in-out;
  z-index: 2;

  &:hover {
    background: #1d7dd8;
    transform: translateY(-50%) scale(1.1);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  svg {
    width: 16px;
    height: 16px;
    transition: transform 0.2s ease-in-out;
  }

  &:hover svg {
    transform: translateY(-1px);
  }
`;

// 添加聊天容器的样式组件
const ChatContainer = styled.div`
  border: 2px solid #d1d5db;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  overflow: hidden;
  max-height: 65vh;
  display: flex;
  flex-direction: column;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.05) 0%,
      rgba(147, 51, 234, 0.05) 100%
    );
    pointer-events: none;
    z-index: 0;
  }

  & > * {
    position: relative;
    z-index: 1;
  }
`;

interface Message {
  id: string;
  content: string;
  sender: "user" | "assistant";
  timestamp: Date;
}

interface ChatbotProps {
  initialMessage?: string;
  position?: "floating" | "sidebar";
  defaultOpen?: boolean; // 新增默认展开状态属性
}

const Chatbot: React.FC<ChatbotProps> = ({
  initialMessage = "Hi! I'm OpenStory AI Agent. I can help you create stories, generate drama content, create storyboards, and even produce videos! What story would you like to create today?",
  position = "floating",
  defaultOpen = true,
}) => {
  const [isOpen, setIsOpen] = useState(
    position === "sidebar" ? defaultOpen : false
  );
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to the bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Add initial message when component mounts
  useEffect(() => {
    if (initialMessage) {
      setMessages([
        {
          id: "1",
          content: initialMessage,
          sender: "assistant",
          timestamp: new Date(),
        },
      ]);
    }
  }, [initialMessage]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading) return;

    const userMessageContent = inputValue.trim();

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: userMessageContent,
      sender: "user",
      timestamp: new Date(),
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setError(null);

    // Set loading state
    setIsLoading(true);

    try {
      // Convert messages to ChatMessage format for API
      const conversationHistory: ChatMessage[] = messages.map((msg) => ({
        role: msg.sender === "user" ? "user" : "assistant",
        content: msg.content,
        timestamp: msg.timestamp.toISOString(),
      }));

      // Create assistant message placeholder
      const assistantMessageId = (Date.now() + 1).toString();
      let assistantContent = "";

      // Add empty assistant message that will be updated
      const assistantMessage: Message = {
        id: assistantMessageId,
        content: "",
        sender: "assistant",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, assistantMessage]);

      // Send message with streaming
      await chatService.sendMessageWithCallback(
        userMessageContent,
        conversationHistory,
        {
          onContent: (content: string) => {
            assistantContent += content;
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === assistantMessageId
                  ? { ...msg, content: assistantContent }
                  : msg
              )
            );
          },
          onFinish: (fullContent: string) => {
            setMessages((prev) =>
              prev.map((msg) =>
                msg.id === assistantMessageId
                  ? { ...msg, content: fullContent }
                  : msg
              )
            );
          },
          onError: (error: string) => {
            console.error("Chat API error:", error);
            setError("Failed to get a response. Please try again.");
            // Remove the empty assistant message on error
            setMessages((prev) =>
              prev.filter((msg) => msg.id !== assistantMessageId)
            );
          },
        }
      );
    } catch (err) {
      console.error("Error sending message:", err);
      setError("Failed to get a response. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const toggleChat = () => {
    setIsOpen((prev) => !prev);
    if (!isOpen) {
      // Focus the input when opening
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  };

  // Format time as HH:MM
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <>
      {/* Floating button when chat is closed */}
      {!isOpen && position === "floating" && (
        <button
          onClick={toggleChat}
          className="fixed bottom-5 right-5 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center shadow-xl hover:shadow-2xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 z-50 backdrop-blur-sm border-2 border-blue-400"
          style={{
            boxShadow:
              "0 12px 24px rgba(59, 130, 246, 0.4), 0 6px 12px rgba(59, 130, 246, 0.3)",
          }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            className="w-6 h-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
            />
          </svg>
        </button>
      )}

      {/* Chat container - KoHo font applied */}
      {(isOpen || position === "sidebar") && (
        <div
          className={cn(
            "fixed bottom-32 right-5 z-40 flex flex-col gap-4 w-[380px]",
            koho.className
          )}
        >
          {/* 收起/展开按钮 - 在 sidebar 模式下始终显示 */}
          {position === "sidebar" && (
            <div className="self-end mb-2">
              <button
                onClick={toggleChat}
                className="bg-white/95 backdrop-blur-sm rounded-full p-3 shadow-lg hover:shadow-xl hover:bg-white transition-all duration-300 border-2 border-gray-200 hover:border-blue-300 group"
                aria-label={isOpen ? "Collapse chat" : "Expand chat"}
              >
                <div className="text-gray-600 group-hover:text-blue-600 transition-colors duration-300">
                  {isOpen ? <ChevronDown size={20} /> : <ChevronUp size={20} />}
                </div>
              </button>
            </div>
          )}

          {/* 使用新的聊天容器组件 */}
          {isOpen && (
            <ChatContainer>
              {/* 消息列表区域 */}
              <div
                className="flex-1 overflow-y-auto p-4 pb-28 flex flex-col gap-4"
                ref={messagesContainerRef}
              >
                {/* Messages in chronological order (oldest first) */}
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      "animate-slideIn",
                      message.sender === "user"
                        ? "self-end max-w-[85%]"
                        : "self-start w-full"
                    )}
                  >
                    {message.sender === "user" ? (
                      // User message bubble - ChatGPT style
                      <div className="bg-[#2563EB] text-white rounded-2xl px-4 py-3 font-koho shadow-sm">
                        <div className="text-[14px] leading-[20px] font-koho whitespace-pre-wrap">
                          {message.content}
                        </div>
                      </div>
                    ) : (
                      // Bot message - ChatGPT style (no bubble background)
                      <div className="w-full flex items-start gap-3 py-2 font-koho">
                        <div className="w-8 h-8 rounded-full bg-[#F6FAFF] flex-shrink-0 flex items-center justify-center overflow-hidden shadow-sm">
                          <Image
                            src="/images/logo/logo-metal.png"
                            alt="OpenStory AI"
                            width={24}
                            height={24}
                            className="rounded-full object-cover w-[24px] h-[24px]"
                          />
                        </div>
                        <div className="flex-1 flex flex-col gap-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="text-[14px] font-medium text-[#374151] leading-[18px] font-koho">
                              OpenStory AI
                            </span>
                            <span className="text-[11px] text-[#9CA3AF] leading-[14px] font-koho">
                              {formatTime(message.timestamp)}
                            </span>
                          </div>
                          <div className="text-[14px] text-[#374151] leading-[22px] font-koho whitespace-pre-wrap">
                            {message.content}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {/* Loading indicator */}
                {isLoading && (
                  <div
                    className="flex items-center space-x-2 p-4 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg self-start ml-1 mt-2 font-koho border-2 border-gray-200 transition-all duration-300"
                    style={{
                      boxShadow:
                        "0 8px 20px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08)",
                    }}
                  >
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce delay-150" />
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce delay-300" />
                  </div>
                )}

                {/* Error message */}
                {error && (
                  <div
                    className="flex items-center gap-2 p-4 bg-red-50/95 backdrop-blur-sm text-red-600 rounded-xl w-full mb-1 border-2 border-red-200 shadow-lg font-koho transition-all duration-300"
                    style={{
                      boxShadow:
                        "0 8px 20px rgba(239, 68, 68, 0.15), 0 4px 8px rgba(239, 68, 68, 0.1)",
                    }}
                  >
                    <AlertCircle size={16} />
                    <p className="text-sm">{error}</p>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>

              {/* 悬浮输入区域 - 覆盖在消息区域上方 */}
              <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-white via-white/95 to-transparent pointer-events-none">
                <form
                  onSubmit={handleSubmit}
                  className="relative pointer-events-auto"
                >
                  <div className="relative w-full">
                    <input
                      id="chatbot-input"
                      ref={inputRef}
                      type="text"
                      placeholder="Help me with...."
                      value={inputValue}
                      onChange={handleInputChange}
                      disabled={isLoading}
                      className="w-full bg-white/95 backdrop-blur-md border border-gray-300 rounded-2xl px-4 py-4 pr-12 text-sm text-[#374151] font-koho placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-300 transition-all duration-200 shadow-lg min-h-[52px]"
                      style={{
                        fontFamily: "var(--font-koho), KoHo, sans-serif",
                      }}
                    />
                    <SendButton
                      type="submit"
                      disabled={!inputValue.trim() || isLoading}
                      tabIndex={-1}
                      aria-label="Send"
                      style={{
                        opacity: inputValue.trim() && !isLoading ? 1 : 0.5,
                        pointerEvents:
                          inputValue.trim() && !isLoading ? "auto" : "none",
                      }}
                    >
                      <ArrowUp />
                    </SendButton>
                  </div>
                </form>
              </div>
            </ChatContainer>
          )}
        </div>
      )}
    </>
  );
};

export default Chatbot; 
