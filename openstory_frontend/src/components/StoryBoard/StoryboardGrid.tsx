"use client";
import { useState } from "react";
import Image from "next/image";
import { Plus, ChevronDown } from "lucide-react";
import { v4 as uuid } from "uuid";
import { koho } from "@/theme";
import { cn } from "@/lib/utils";
import { useTaskStore, useStoryStore } from "@/store/task";
import {
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  Dialog,
  DialogContent,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import { frameService } from "@/services/frameService";
import { LoadingButton } from "@mui/lab";
import { storyBoardService } from "@/services/storyBoardService";
import { Button } from "@mui/material";
import { useAuthStore } from "@/store/authStore";
/* ---------- types ---------- */
export interface StoryboardFrame {
  id: string;
  shot_name: string;
  image_url?: string;
  shot_type?:
    | "setting_only"
    | "setting_with_actors_no_dialogue"
    | "setting_with_actors_dialogue";
  shot_setting?: string;
  shot_framing?: "medium" | "close-up" | "wide angle";
  shot_angle?:
    | "eye-level"
    | "low angle"
    | "high angle"
    | "over the shoulder"
    | "dutch angle";
  shot_movement?:
    | "dolly in"
    | "pan"
    | "tracking shot"
    | "static"
    | "slow push in"
    | "slow push out";
  character_composition?: string;
  character_dialogue?: string;
  type?: "image" | "video" | "close-up"; // keeping for backward compatibility
  description?: string; // keeping for backward compatibility
  status?: "pending" | "done" | "editing";
  characters?: any[];
  result_type: string;
}

interface StoryboardGridProps {
  initialFrames?: StoryboardFrame[];
  onChange?: (frames: StoryboardFrame[]) => void;
  onUpdateShot?: (frame: StoryboardFrame) => void;
  className?: string;
}

/* ---------- presentational sub-component ---------- */
function FrameCard({
  frame,
  onUpdate,
  handleDeleteFrame,
}: {
  frame: StoryboardFrame;
  onUpdate: (id: string, field: string, value: any) => void;
  onUpdateShot: (frame: StoryboardFrame) => void;
  handleDeleteFrame: (id: string) => void;
}) {
  const { characters } = useTaskStore();
  const [selectedCharacters, setSelectedCharacters] = useState<any[]>(
    frame.characters || []
  );
  const [loading, setLoading] = useState(false);

  const handleTextChange = (field: string, value: string) => {
    onUpdate(frame.id, field, value);
  };

  const handleSelectChange = (
    field: string,
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    onUpdate(frame.id, field, e.target.value);
  };

  const handleCharacterChange = (event: any) => {
    const selectedIds = event.target.value;
    const selectedChars = characters.filter((char) =>
      selectedIds.includes(char.id)
    );
    setSelectedCharacters(selectedChars);
    onUpdate(frame.id, "characters", selectedChars);
  };

  const renderSelect = (
    field: string,
    value: string | undefined,
    options: string[],
    label: string
  ) => (
    <div className="mb-4">
      <label className="block text-[12px] font-medium text-[#666F8D] mb-1.5">
        {label}
      </label>
      <div className="relative">
        <select
          value={value || ""}
          onChange={(e) => handleSelectChange(field, e)}
          className="w-full p-2.5 border border-[#E3E6EA] rounded-xl text-[14px] text-[#666F8D] appearance-none pr-8 bg-white focus:outline-none focus:ring-2 focus:ring-blue-100 focus:border-blue-300 transition-all !rounded-xl"
        >
          <option value="" disabled>
            Select {label.toLowerCase()}
          </option>
          {options.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
        <ChevronDown
          size={16}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#666F8D]"
        />
      </div>
    </div>
  );

  const renderTextInput = (
    field: string,
    value: string | undefined,
    label: string,
    isLong: boolean = false
  ) => (
    <div className="mb-4">
      <label className="block text-[12px] font-medium text-[#666F8D] mb-1.5">
        {label}
      </label>
      {isLong ? (
        <textarea
          value={value || ""}
          onChange={(e) => handleTextChange(field, e.target.value)}
          className="w-full p-3 border border-[#E3E6EA] rounded-xl text-[14px] text-[#666F8D] min-h-[100px] resize-none bg-white focus:outline-none focus:ring-2 focus:ring-blue-100 focus:border-blue-300 transition-all shadow-sm !rounded-xl"
          placeholder={`Enter ${label.toLowerCase()}`}
        />
      ) : (
        <input
          type="text"
          value={value || ""}
          onChange={(e) => handleTextChange(field, e.target.value)}
          className="w-full p-3 border border-[#E3E6EA] rounded-xl text-[14px] text-[#666F8D] bg-white focus:outline-none focus:ring-2 focus:ring-blue-100 focus:border-blue-300 transition-all shadow-sm !rounded-xl"
          placeholder={`Enter ${label.toLowerCase()}`}
        />
      )}
    </div>
  );

  const handleCreateFrame = async (frame: any) => {
    try {
      setLoading(true);
      console.log("frame-------data", frame);
      // 处理 character_composition 和 character_dialogue
      const processedFrame = {
        ...frame,
        movie_style: JSON.stringify(useTaskStore.getState().movie_style),
        character_composition:
          typeof frame.character_composition === "object"
            ? JSON.stringify(frame.character_composition)
            : frame.character_composition,
        character_dialogue: frame.character_dialogue || "",
      };
      const result = await frameService.createFrame(processedFrame);
      const { frames } = useTaskStore.getState();
      if (result && result.video_url) {
        const next = frames?.map((f) =>
          f.id === frame.id
            ? { ...f, image_url: result.video_url, result_type: result.type }
            : f
        );
        useTaskStore.setState({ frames: next });
        console.log("next", next);
        setLoading(false);
      }
    } catch (error) {
      console.error("Error creating frame:", error);
      setLoading(false);
    }
  };

  return (
    <div className="w-[361px] shrink-0 bg-white rounded-xl overflow-hidden border border-[#E3E6EA] shadow-[0px_4px_16px_rgba(25,33,61,0.04)] flex flex-col gap-4 p-7 h-full hover:shadow-[0px_8px_24px_rgba(25,33,61,0.06)] transition-shadow duration-300 !rounded-xl relative">
      {/* Delete Button */}
      <IconButton
        size="small"
        onClick={() => handleDeleteFrame(frame.id)}
        sx={{
          position: "absolute",
          top: 8,
          right: 8,
          color: "error.main",
          "&:hover": {
            backgroundColor: "error.light",
            color: "error.contrastText",
          },
        }}
      >
        <DeleteIcon fontSize="small" />
      </IconButton>

      {/* Shot Name */}
      {renderTextInput("shot_name", frame.shot_name, "Shot Name")}

      {/* Image area */}
      <div className="relative w-full aspect-[4/3] rounded-xl mb-5 overflow-hidden shadow-sm !rounded-xl">
        {frame.image_url ? (
          <Image
            src={frame.image_url}
            alt={frame.shot_name}
            fill
            className="object-cover rounded-xl"
          />
        ) : (
          <Image
            src={"/placeholder-image.svg"}
            alt={"preview image"}
            fill
            className="object-cover rounded-xl"
          />
        )}
      </div>

      {/* Shot settings */}
      {renderSelect(
        "shot_type",
        frame.shot_type,
        [
          "setting_only",
          "setting_with_actors_no_dialogue",
          "setting_with_actors_dialogue",
        ],
        "Shot Type"
      )}

      {renderTextInput(
        "shot_setting",
        frame.shot_setting,
        "Shot Setting",
        true
      )}

      {renderSelect(
        "shot_framing",
        frame.shot_framing,
        ["medium", "close-up", "wide angle"],
        "Shot Framing"
      )}

      {renderSelect(
        "shot_angle",
        frame.shot_angle,
        [
          "eye-level",
          "low angle",
          "high angle",
          "over the shoulder",
          "dutch angle",
        ],
        "Shot Angle"
      )}

      {renderSelect(
        "shot_movement",
        frame.shot_movement,
        [
          "dolly in",
          "pan",
          "tracking shot",
          "static",
          "slow push in",
          "slow push out",
        ],
        "Shot Movement"
      )}

      {renderTextInput(
        "character_composition",
        frame.character_composition,
        "Character Composition",
        true
      )}
      {renderTextInput(
        "character_dialogue",
        frame.character_dialogue,
        "Character Dialogue",
        true
      )}

      {/* Character Selection */}
      <Box sx={{ mt: 2, display: "none" }}>
        <FormControl fullWidth>
          <InputLabel id="character-select-label">Characters</InputLabel>
          <Select
            labelId="character-select-label"
            multiple
            value={selectedCharacters.map((char) => char.id)}
            onChange={handleCharacterChange}
            label="Characters"
            sx={{
              "& .MuiSelect-select": {
                py: 1,
              },
            }}
          >
            {characters.map((char: any) => (
              <MenuItem key={char.id} value={char.id}>
                {char.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      {/* Update Shot Button */}
      <LoadingButton
        loading={loading}
        onClick={() => handleCreateFrame(frame)}
        className="mt-2 w-full p-3.5 bg-blue-500 text-white rounded-xl hover:bg-blue-600 text-[14px] font-medium transition-colors duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 active:translate-y-0 active:shadow-md !rounded-xl"
      >
        Update Shot
      </LoadingButton>
    </div>
  );
}

/* ---------- main component ---------- */
const StoryboardGrid = ({
  initialFrames = [],
  onChange,
  onUpdateShot = (frame) => console.log("Update shot", frame),
  className,
}: StoryboardGridProps) => {
  const { frames, result } = useTaskStore();
  const [submitting, setSubmitting] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  const handlePreviewOpen = () => {
    setPreviewOpen(true);
  };

  const handlePreviewClose = () => {
    setPreviewOpen(false);
  };

  /* insert AFTER the position `index` */
  const insertFrame = (index: number) => {
    const newFrame: StoryboardFrame = {
      id: uuid(),
      shot_name: "Untitled shot",
      image_url: "/placeholder-image.svg",
      result_type: "image",
    };
    const next = [
      ...frames.slice(0, index + 1),
      newFrame,
      ...frames.slice(index + 1),
    ];
    useTaskStore.setState({ frames: next });
    onChange?.(next);
  };

  /* Update a single field in a frame */
  const updateFrameField = (id: string, field: string, value: any) => {
    const next = frames.map((frame) =>
      frame.id === id ? { ...frame, [field]: value } : frame
    );
    useTaskStore.setState({ frames: next });
    onChange?.(next);
  };

  /* Handle update shot button click */
  const handleUpdateShot = (frame: StoryboardFrame) => {
    onUpdateShot(frame);
  };

  const handleDeleteFrame = (id: string) => {
    const next = frames.filter((frame) => frame.id !== id);
    useTaskStore.setState({ frames: next });
    onChange?.(next);
  };

  const generateStoryboard = async () => {
    try {
      setSubmitting(true);
      const story = useTaskStore.getState();
      const user = useAuthStore.getState().user;
      const processedFrame = story.frames.map((frame) => ({
        ...frame,
        character_composition:
          typeof frame.character_composition === "object"
            ? JSON.stringify(frame.character_composition)
            : frame.character_composition,
        // @ts-ignore
        character_dialogue: frame.character_dialogue || "",
      }));

      console.log("story------", story);
      const visual_style = story.movie_style["Visual Style"];

      // 准备发送给API的数据，包含BGM信息
      const storyData = {
        ...story,
        movie_style: visual_style,
        frames: processedFrame,
        user_id: user?.id,
        bgm: story.bgm || null,
      };

      const result = await storyBoardService.createStory(storyData);
      console.log("result------", result);
      if (result && result.video_url) {
        useTaskStore.setState({ result: result.video_url });
        alert("create success!");
        setSubmitting(false);
      } else {
        setSubmitting(false);
      }
    } catch (error) {
      console.error("Error creating frame:", error);
      setSubmitting(false);
    }
  };

  const handleSaveStory = async () => {
    const user = useAuthStore.getState().user;
    console.log("-------store", useTaskStore.getState());
    if (useTaskStore.getState().id) {
      await storyBoardService.updateStory({
        user_id: user?.id,
        story: {
          ...useTaskStore.getState(),
          frames: frames,
        },
        updated_at: new Date().toISOString(),
      });
    } else {
      const result = await storyBoardService.saveStory({
        user_id: user?.id,
        story: {
          ...useTaskStore.getState(),
          frames: frames,
        },
      });
      useTaskStore.setState({ id: result.data.id });
    }
    
  };

  return (
    <div
      className={cn("flex flex-col h-full text-sm", koho.className, className)}
    >
      <div className="flex items-center justify-start mb-6">
        <h2 className="text-xl font-medium text-gray-800 mr-4">Storyboard</h2>
        <div className="flex gap-2">
          <LoadingButton
            variant="contained"
            loading={submitting}
            onClick={() => {
              generateStoryboard();
            }}
          >
            Create Story
          </LoadingButton>
          {result &&(
            <LoadingButton
            variant="outlined"
            disabled={!result}
            onClick={handlePreviewOpen}
          >
            Preview Story
          </LoadingButton>
        )}
          <LoadingButton
            variant="contained"
            loading={saveLoading}
            onClick={handleSaveStory}
          >
            Save Editing
          </LoadingButton>
        </div>
      </div>

      <Dialog
        open={previewOpen}
        onClose={handlePreviewClose}
        maxWidth="md"
        fullWidth
      >
        <DialogContent>
          {result && (
            <video controls className="w-full h-auto" src={result}>
              Your browser does not support the video tag.
            </video>
          )}
        </DialogContent>
      </Dialog>

      {/* SCROLLER with transparent background */}
      <div className="flex-1 overflow-x-auto overflow-y-hidden pb-6 h-full">
        {/* one long horizontal row */}
        <div className="flex items-stretch gap-8 w-max px-6 py-6">
          {frames.map((frame, i) => (
            <div key={frame.id} className="flex items-center gap-6 group">
              <FrameCard
                frame={frame}
                onUpdate={updateFrameField}
                onUpdateShot={handleUpdateShot}
                handleDeleteFrame={handleDeleteFrame}
              />
              {/* plus button between cards */}
              {/* <button
                aria-label="Insert frame"
                onClick={() => insertFrame(i)}
                className="shrink-0 h-12 w-12 rounded-full bg-blue-500 text-white
                         flex items-center justify-center hover:bg-blue-600 self-center
                         opacity-0 group-hover:opacity-100 transition-all duration-200
                         shadow-md hover:shadow-lg transform hover:scale-110 active:scale-100"
              >
                <Plus size={20} />
              </button> */}
            </div>
          ))}

          {/* Add first frame if empty, otherwise add at the end */}
          <button
            aria-label={
              frames.length === 0 ? "Add first frame" : "Add frame at end"
            }
            onClick={() =>
              insertFrame(frames.length > 0 ? frames.length - 1 : 0)
            }
            className="self-center shrink-0 h-14 w-14 rounded-full bg-blue-500 text-white
                     flex items-center justify-center hover:bg-blue-600
                     shadow-lg hover:shadow-xl transition-all duration-200
                     transform hover:scale-110 active:scale-100"
          >
            <Plus size={24} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default StoryboardGrid;
