import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Avatar,
  Grid,
  Paper,
} from '@mui/material';
import { AudioPreview } from './AudioPreview';

interface CharacterDetailDialogProps {
  open: boolean;
  onClose: () => void;
  character: any;
}

export const CharacterDetailDialog: React.FC<CharacterDetailDialogProps> = ({
  open,
  onClose,
  character,
}) => {
  if (!character) return null;

  const getImgUrl = (character: any) => {
    if(character && character.avatar_urls) { 
      return character?.avatar_urls?.url;
    } else {
      return '/placeholder-image.svg';
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        }
      }}
    >
      <DialogTitle sx={{ 
        borderBottom: '1px solid #eee',
        pb: 2,
        display: 'flex',
        alignItems: 'center',
        gap: 2
      }}>
        <Avatar
          src={getImgUrl(character)}
          alt={character.name}
          sx={{ width: 56, height: 56 }}
        />
        <Box>
          <Typography variant="h6">{character.name}</Typography>
          <Typography variant="subtitle2" color="text.secondary">
            {character.gender} • {character.age} years old
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ mt: 2 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper elevation={0} sx={{ p: 2, bgcolor: '#f8f9fa' }}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Tagline
              </Typography>
              <Typography variant="body1" color="text.secondary">
                {character.oneliner}
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12}>
            <Paper elevation={0} sx={{ p: 2, bgcolor: '#f8f9fa' }}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Backstory
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ whiteSpace: 'pre-wrap' }}>
                {character.backstory}
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper elevation={0} sx={{ p: 2, bgcolor: '#f8f9fa' }}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Personality
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ whiteSpace: 'pre-wrap' }}>
                {character.personality}
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper elevation={0} sx={{ p: 2, bgcolor: '#f8f9fa' }}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Appearance
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ whiteSpace: 'pre-wrap' }}>
                {character.appearance}
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12}>
            <Paper elevation={0} sx={{ p: 2, bgcolor: '#f8f9fa' }}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Voice
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                {character.voice_description}
              </Typography>
              {character.voice_name && (
                <AudioPreview displayName={character.voice_name} />
              )}
            </Paper>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #eee' }}>
        <Button onClick={onClose} variant="outlined">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}; 