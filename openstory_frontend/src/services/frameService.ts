import axios from 'axios';
import { useAuthStore } from '@/store/authStore';
const API_HOST = process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:8000/api/v1';

export interface Frame {
  id: string;
  shot_name: string;
  shot_type?: "setting_only" | "setting_with_actors_no_dialogue" | "setting_with_actors_dialogue";
  shot_setting?: string;
  shot_framing?: "medium" | "close-up" | "wide angle";
  shot_angle?: "eye-level" | "low angle" | "high angle" | "over the shoulder" | "dutch angle";
  shot_movement?: "dolly in" | "pan" | "tracking shot" | "static" | "slow push in" | "slow push out";
  character_composition?: string;
  character_dialogue?: string;
  characters?: any[];
  status?: 'pending' | 'done' | 'editing';
}

export const frameService = {

  async createFrame(frame: Frame): Promise<any> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.post(`${API_HOST}/frames/create_frame`, frame, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error creating character:', error);
      throw error;
    }
  }
}; 
