import axios from 'axios';
import { useAuthStore } from '@/store/authStore';

const API_HOST = process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:8000/api/v1';

export interface MovieStyle {
  id: string;
  styles: any;
  created_at: string;
  updated_at: string;
}

export interface MovieStylesResponse {
  items: MovieStyle[];
  total: number;
}

export const movieStyleService = {
  async getMovieStyles(skip: number = 0, limit: number = 10): Promise<MovieStylesResponse> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.get(`${API_HOST}/movie-styles/`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          skip,
          limit
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching movie styles:', error);
      throw error;
    }
  }
}; 