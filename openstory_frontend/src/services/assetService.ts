import axios from 'axios';
import { useAuthStore } from '@/store/authStore';

const API_HOST = process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:8000/api/v1';

export const assetService = {
  async getAssets(skip: number = 0, limit: number = 10): Promise<any> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.get(`${API_HOST}/assets/`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        params: {
          skip,
          limit
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching movie styles:', error);
      throw error;
    }
  }
}; 