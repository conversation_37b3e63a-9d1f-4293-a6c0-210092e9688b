import axios from 'axios';
import { useAuthStore } from '@/store/authStore';

const API_HOST = process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:8000/api/v1';

export const generateEpisodeService = {
  async generateEpisode(concept: string, story_type: string): Promise<any> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.post(`${API_HOST}/stories/generate-content`, {
        user_story: concept,
        story_type,
        model: "gpt-4o-mini",
        max_tokens: 2048,
        temperature: 0.5,
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching movie styles:', error);
      throw error;
    }
  }
}; 

export const generateStoryService = {
  async generateStory(characters: any, storyline: string, story_type: string): Promise<any> {
    try {
      const token = useAuthStore.getState().token;
      const response = await axios.post(`${API_HOST}/stories/storyboard`, {
        storyline,
        characters,
        story_type,
        model: "gpt-4o-mini",
        max_tokens: 2048,
        temperature: 0.5,
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching movie styles:', error);
      throw error;
    }
  }
}; 
