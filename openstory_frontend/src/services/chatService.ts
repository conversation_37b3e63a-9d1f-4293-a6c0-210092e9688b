// import { supabase } from "@/lib/supabase"; // 暂时注释掉，移除认证

export interface ChatMessage {
  role: "user" | "assistant";
  content: string;
  timestamp?: string;
}

export interface ChatRequest {
  message: string;
  conversation_history: ChatMessage[];
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

export interface ChatResponse {
  message: string;
  timestamp: string;
}

export interface StreamChunk {
  type: "content" | "finish" | "error" | "confirmation_request";
  data?: string;
  reason?: string;
  full_content?: string;
  detail?: string;
  confirmation_id?: string;
}

class ChatService {
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
  }

  private getHeaders() {
    return {
      "Content-Type": "application/json",
      // 暂时移除认证，先调通功能
      // Authorization: `Bearer ${token}`,
    };
  }

  /**
   * Send a chat message and get streaming response using Agent
   */
  async sendMessageStream(
    message: string,
    conversationHistory: ChatMessage[] = [],
    options: {
      model?: string;
      temperature?: number;
      max_tokens?: number;
    } = {}
  ): Promise<ReadableStream<Uint8Array>> {
    const request: ChatRequest = {
      message,
      conversation_history: conversationHistory,
      model: options.model || "gpt-4o-mini",
      temperature: options.temperature || 0.7,
      max_tokens: options.max_tokens || 1000,
    };

    const headers = this.getHeaders();
    const response = await fetch(`${this.baseURL}/api/v1/chat/agent/stream`, {
      method: "POST",
      headers,
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.detail || `HTTP error! status: ${response.status}`
      );
    }

    if (!response.body) {
      throw new Error("No response body");
    }

    return response.body;
  }

  /**
   * Send a chat message and get complete response (non-streaming) using Agent
   */
  async sendMessage(
    message: string,
    conversationHistory: ChatMessage[] = [],
    options: {
      model?: string;
      temperature?: number;
      max_tokens?: number;
    } = {}
  ): Promise<ChatResponse> {
    const request: ChatRequest = {
      message,
      conversation_history: conversationHistory,
      model: options.model || "gpt-4o-mini",
      temperature: options.temperature || 0.7,
      max_tokens: options.max_tokens || 1000,
    };

    const headers = this.getHeaders();
    const response = await fetch(`${this.baseURL}/api/v1/chat/agent`, {
      method: "POST",
      headers,
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.detail || `HTTP error! status: ${response.status}`
      );
    }

    return response.json();
  }

  /**
   * Parse SSE stream and yield parsed chunks
   */
  async *parseSSEStream(
    stream: ReadableStream<Uint8Array>
  ): AsyncGenerator<StreamChunk, void, unknown> {
    const reader = stream.getReader();
    const decoder = new TextDecoder();
    let buffer = "";

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");

        // Keep the last incomplete line in buffer
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = line.slice(6); // Remove 'data: ' prefix
              if (data.trim()) {
                const chunk: StreamChunk = JSON.parse(data);
                yield chunk;
              }
            } catch (error) {
              console.error("Error parsing SSE data:", error);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * Send message with streaming response and handle the stream (with confirmation support)
   */
  async sendMessageWithCallback(
    message: string,
    conversationHistory: ChatMessage[] = [],
    callbacks: {
      onContent?: (content: string) => void;
      onFinish?: (fullContent: string) => void;
      onError?: (error: string) => void;
      onConfirmationRequest?: (message: string, confirmationId: string) => void;
    } = {},
    options: {
      model?: string;
      temperature?: number;
      max_tokens?: number;
    } = {}
  ): Promise<string> {
    try {
      const stream = await this.sendMessageStream(
        message,
        conversationHistory,
        options
      );
      let fullContent = "";

      for await (const chunk of this.parseSSEStream(stream)) {
        switch (chunk.type) {
          case "content":
            if (chunk.data) {
              fullContent += chunk.data;
              callbacks.onContent?.(chunk.data);
            }
            break;
          case "confirmation_request":
            if (chunk.data && chunk.confirmation_id) {
              callbacks.onConfirmationRequest?.(
                chunk.data,
                chunk.confirmation_id
              );
              return chunk.data; // Return the confirmation message
            }
            break;
          case "finish":
            const finalContent = chunk.full_content || fullContent;
            callbacks.onFinish?.(finalContent);
            return finalContent;
          case "error":
            const errorMessage = chunk.detail || "Unknown error occurred";
            callbacks.onError?.(errorMessage);
            throw new Error(errorMessage);
        }
      }

      return fullContent;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      callbacks.onError?.(errorMessage);
      throw error;
    }
  }
}

export const chatService = new ChatService();
