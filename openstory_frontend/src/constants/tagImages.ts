// Tag image mapping file
export type TagCategory = 'Use Case' | 'Visual Style' | 'World Remix' | 'Vibes';

// Define the structure for our mapping
export interface TagItem {
  itemName: string;
  image: string;
}

export interface TagCategoryData {
  styleName: string;
  items: TagItem[];
}

export type TagImageMap = TagCategoryData[];

// Image paths for each tag option
export const tagImageMap: TagImageMap = [
  {
    styleName: "Use Case",
    items: [
      { itemName: "Drama", image: "/images/tags/use-case/drama.jpg" },
      { itemName: "Advertisement", image: "/images/tags/use-case/advertisement.jpg" }
    ]
  },
  {
    styleName: "Visual Style",
    items: [
      { itemName: "Realistic", image: "/images/tags/visual-style/realistic.jpg" },
      { itemName: "Pixar", image: "/images/tags/visual-style/pixar.jpg" },
      { itemName: "Ghibli", image: "/images/tags/visual-style/ghibli.jpg" },
      { itemName: "Neo Art", image: "/images/tags/visual-style/neo-art.jpg" }
    ]
  },
  {
    styleName: "World Remix",
    items: [
      { itemName: "Sex and the City", image: "/images/tags/world-remix/sex-and-the-city.jpg" },
      { itemName: "Harry Potter", image: "/images/tags/world-remix/harry-potter.jpg" }
    ]
  },
  {
    styleName: "Vibes",
    items: [
      { itemName: "Engaging", image: "/images/tags/vibes/engaging.jpg" },
      { itemName: "Think out of the box", image: "/images/tags/vibes/think-out-of-box.jpg" }
    ]
  }
];

// Default fallback image if a specific one isn't found
export const defaultTagImage = "/images/tags/default.jpg";

// Helper function to get the image path for a tag and option
export function getTagImage(category: string, option: string): string {
  const categoryData = tagImageMap.find(cat => cat.styleName === category);
  const item = categoryData?.items.find(item => item.itemName === option);
  return item?.image || defaultTagImage;
} 