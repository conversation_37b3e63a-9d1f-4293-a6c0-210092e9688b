import { create } from "zustand";
import { persist } from "zustand/middleware";
import { StoryboardFrame } from "@/components/StoryBoard/StoryboardGrid";

export interface TaskState {
  id: string;
  title: string;
  concept: string;
  movie_style: Record<string, string | null>;
  story_line: string;
  frames: StoryboardFrame[];
  characters: any[];
  result: any | null;
  result_time: string;
  bgm?: string | null;
}

interface StoryState {
  user_id: string;
  stories: TaskState[];
}

export const useTaskStore = create<TaskState>()(
  persist(
    (set) => ({
      id: "",
      concept: "",
      movie_style: {},
      title: "",
      story_line:
        "A story about <PERSON> and <PERSON> in modern New York City. They meet by chance and navigate through the complexities of modern life together.",
      characters: [],
      frames: [
        {
          id: "1",
          shot_name: "Input shot name of frame",
          image_url: "/placeholder-image.svg",
          character_composition: "",
          character_dialogue: "",
          characters: [],
          shot_type: "setting_with_actors_no_dialogue",
          shot_setting: "Busy New York street with pedestrians and traffic",
          shot_framing: "wide angle",
          shot_angle: "eye-level",
          shot_movement: "static",
          result_type: "image",
          status: "pending",
        },
      ],
      result: null,
      result_time: "0:00",
      bgm: null,
    }),
    {
      name: "task-storage",
    }
  )
);

export const useStoryStore = create<StoryState>()(
  persist(
    (set) => ({
      user_id: "",
      stories: [],
    }),
    {
      name: "story-storage",
    }
  )
);
