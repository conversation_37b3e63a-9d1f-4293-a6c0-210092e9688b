import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { StoryboardFrame } from '../types/storyboard';

const defaultFrames: StoryboardFrame[] = [
  {
    id: '1',
    title: 'Frame 1',
    description: 'Description for frame 1',
    imageUrl: '/images/frame1.jpg',
    order: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    title: 'Frame 2',
    description: 'Description for frame 2',
    imageUrl: '/images/frame2.jpg',
    order: 2,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

interface StoryboardState {
  frames: StoryboardFrame[];
  addFrame: (frame: StoryboardFrame) => void;
  updateFrame: (id: string, frame: Partial<StoryboardFrame>) => void;
  removeFrame: (id: string) => void;
  clearFrames: () => void;
}

export const useStoryboardStore = create<StoryboardState>()(
  persist(
    (set) => ({
      frames: defaultFrames,
      addFrame: (frame) => set((state) => ({ frames: [...state.frames, frame] })),
      updateFrame: (id, frame) =>
        set((state) => ({
          frames: state.frames.map((f) => (f.id === id ? { ...f, ...frame } : f)),
        })),
      removeFrame: (id) =>
        set((state) => ({
          frames: state.frames.filter((f) => f.id !== id),
        })),
      clearFrames: () => set({ frames: [] }),
    }),
    {
      name: 'storyboard-storage', // unique name for localStorage
    }
  )
); 