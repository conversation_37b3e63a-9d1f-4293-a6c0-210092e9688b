{"name": "openstory", "version": "0.0.1", "private": true, "scripts": {"prepare": "husky install", "lint": "eslint '{pages,src}/**/*.{js,jsx,ts,tsx}'", "lint:style": "stylelint '{pages,src}/**/*.{css,scss}'", "lint:fix": "eslint '{pages,src}/**/*.{js,jsx,ts,tsx}' --fix", "lint:sfix": "stylelint '{pages,src}/**/*.{css,scss}' --fix", "lint:next": "next lint", "lint:lint-staged": "lint-staged", "cz": "git-cz", "dev": "env-cmd -f .env.dev next dev", "build": "next build", "build:dev": "env-cmd -f .env.dev next build", "build:test": "env-cmd -f .env.test next build", "build:prod": "env-cmd -f .env.prod next build", "start": "next start", "extract:i18n": "formatjs extract \"{pages,src}/**/*.{js,ts,tsx}\" --format lokalise --id-interpolation-pattern \"[sha512:contenthash:base64:6]\" --out-file src/content/en.json", "compile:i18n": "formatjs compile-folder --ast --format simple src/content/locales src/content/compiled-locales", "i18n": "npm run extract:i18n && npm run compile:i18n"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}, "cz-customizable": {"config": "./.cz-config.cjs"}}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.1", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.11.0", "@fontsource/roboto": "^4.5.8", "@mui/icons-material": "^5.14.8", "@mui/lab": "5.0.0-alpha.144", "@mui/material": "^5.14.8", "@mui/x-date-pickers": "5.0.20", "@sentry/nextjs": "^7.105.0", "@sentry/react": "^7.68.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.5", "@tailwindcss/line-clamp": "^0.4.4", "@types/aos": "^3.0.4", "@types/styled-components": "^5.1.34", "@types/uuid": "^10.0.0", "aos": "3.0.0-beta.6", "axios": "^0.27.2", "axios-hooks": "^3.1.5", "classnames": "^2.3.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "dayjs": "^1.11.10", "dotenv": "^16.3.1", "env-cmd": "^10.1.0", "eruda": "^2.11.3", "eslint-plugin-autofix": "^1.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-tsdoc": "^0.2.17", "framer-motion": "^11.18.0", "lodash": "^4.17.21", "lru-cache": "^7.18.3", "lucide-react": "^0.511.0", "next": "14.2.5", "next-videos": "1.5.0", "phone": "^3.1.57", "qrcode.react": "^4.2.0", "react": "18.3.1", "react-animated-number": "^0.4.4", "react-device-detect": "^2.2.3", "react-dom": "18.3.1", "react-draggable": "^4.4.5", "react-hook-form": "^7.46.1", "react-intl": "^5.25.1", "react-toastify": "^10.0.6", "sharp": "^0.30.7", "sonner": "^2.0.3", "styled-components": "^6.1.18", "stylelint-config-tailwindcss": "^0.0.7", "swiper": "^8.4.7", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.14", "tailwindcss-logical": "^3.0.1", "usehooks-ts": "^3.0.1", "uuid": "^11.1.0", "webpack": "^5.88.2", "zustand": "^5.0.4"}, "devDependencies": {"@babel/preset-react": "^7.25.9", "@commitlint/cli": "^17.7.1", "@commitlint/config-conventional": "^17.7.0", "@formatjs/cli": "^4.8.4", "@tailwindcss/forms": "^0.5.10", "@types/lodash": "^4.14.198", "@types/lru-cache": "^7.10.10", "@types/node": "17.0.23", "@types/qrcode.react": "^1.0.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-truncate": "^2.3.4", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.20", "babel-plugin-formatjs": "^10.5.3", "commitizen": "^4.3.0", "commitlint-config-git-commit-emoji": "^1.0.0", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^6.9.2", "eslint": "^8.48.0", "eslint-config-next": "13.0.2", "eslint-config-prettier": "^8.10.0", "eslint-import-resolver-typescript": "^3.6.0", "eslint-plugin-formatjs": "^5.2.14", "eslint-plugin-prettier": "^4.2.1", "husky": "^9.1.7", "lint-staged": "^13.3.0", "next-pwa": "^5.6.0", "prettier": "^2.8.8", "sass": "^1.66.1", "stylelint": "^14.16.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard-scss": "^5.0.0", "stylelint-prettier": "^2.0.0", "typescript": "5.1.6"}, "resolutions": {"strip-ansi": "6.0.1", "string-width": "4.2.2", "wrap-ansi": "7.0.0"}}